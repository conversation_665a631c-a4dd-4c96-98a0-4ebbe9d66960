# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Development Commands

**Development Server:**
```bash
npm run dev
```

**Build Commands:**
```bash
npm run build          # Production build
npm run build:dev      # Development build
```

**Code Quality:**
```bash
npm run lint           # Run ESLint
npm run preview        # Preview built application
```

## Architecture Overview

This is a React-based SaaS application for project idea validation and roadmap generation built with:

- **Frontend:** Vite + React 18 + TypeScript
- **UI Framework:** shadcn/ui components + Tailwind CSS
- **Backend:** Supabase (PostgreSQL + Edge Functions)
- **State Management:** Zustand stores
- **Routing:** React Router v6
- **Forms:** React Hook Form + Zod validation
- **AI Integration:** OpenAI + Anthropic via Supabase Edge Functions

### Key Application Flow

1. **Authentication:** Users sign up/login via Supabase Auth
2. **Idea Validation:** Users submit project ideas for AI-powered validation
3. **Project Planning:** Multi-step wizard for platforms, tech stack, budget, timeline
4. **Roadmap Generation:** AI generates detailed project roadmaps with phases, costs, and team composition
5. **Subscription Management:** Tiered access to different roadmap phases

### State Management Architecture

- **authStore.ts:** Authentication state, admin checks, session management
- **formStore.ts:** Multi-step form state for project planning wizard
- **roadmapStore.ts:** Roadmap generation and management state
- **subscriptionStore.ts:** User subscription and plan access state
- **sidebarStore.ts:** UI sidebar state management

### Protected Routes System

Routes are protected via `ProtectedRoute` component:
- Standard authentication required for `/dashboard`, `/validate-idea`, etc.
- Admin-only routes with `requireAdmin={true}` for `/admin/*` paths
- Automatic redirection based on user role

### Supabase Integration

**Database Tables:**
- `idea_validations` - User project idea submissions and AI responses (enhanced with real_citations, market_research_data)
- `project_planning` - Multi-step planning wizard data
- `roadmaps` - Generated project roadmaps with phase details
- `users` - User profiles and subscription data
- `ai_provider_settings` - Admin-configurable AI provider settings
- `competitor_analysis` - Scraped competitor data from Firecrawl
- `market_research_queries` - Search queries and results from Tavily
- `verified_citations` - Verified citations from real web sources

**Edge Functions:**
- `validate-idea` - Standard AI-powered project idea analysis
- `enhanced-idea-validation` - Enhanced validation with real-time market research (Tavily + Firecrawl)
- `generate-roadmap` - AI-powered roadmap generation using OpenAI/Anthropic
- `get-accessible-phases` - Subscription-based phase access control

### Component Organization

- **pages/:** Top-level route components
- **components/ui/:** shadcn/ui base components
- **components/auth/:** Authentication forms and social auth
- **components/planning/:** Project planning wizard tabs
- **components/roadmap/:** Roadmap display and generation components
- **components/validation/:** Idea validation results and citations
- **components/admin/:** Admin dashboard and management

### AI Integration Pattern

**Standard Validation:**
1. Frontend calls `validate-idea` Edge Function
2. Edge function handles AI provider selection (OpenAI/Anthropic)
3. Structured prompts generate consistent JSON responses
4. Results stored in database with proper user association

**Enhanced Validation (NEW):**
1. Frontend calls `enhanced-idea-validation` Edge Function
2. Function performs real-time market research using Tavily API
3. Scrapes competitor websites using Firecrawl API
4. AI processes real data instead of hallucinated content
5. Stores verified citations and market research data in separate tables
6. Significantly more accurate but takes 2-3x longer

### Subscription & Access Control

- **Interactive Phase:** Free tier access
- **POC/MVP/Production Phases:** Require paid subscriptions
- Access controlled via `check_roadmap_access` RPC function
- Subscription metadata stored for payment processor integration