#!/bin/bash

# Deploy Enhanced Idea Validation
# This script deploys the enhanced validation function and applies database migrations

echo "🚀 Deploying Enhanced Idea Validation with Tavily + Firecrawl..."

# Check if Supabase CLI is installed
if ! command -v supabase &> /dev/null; then
    echo "❌ Supabase CLI is not installed. Please install it first:"
    echo "npm install -g supabase"
    exit 1
fi

# Check if project is linked
if [ ! -f .env.local ]; then
    echo "⚠️  .env.local not found. Make sure you have your environment variables set up."
fi

echo "1️⃣ Applying database migrations..."
supabase db push

if [ $? -eq 0 ]; then
    echo "✅ Database migrations applied successfully"
else
    echo "❌ Failed to apply database migrations"
    exit 1
fi

echo "2️⃣ Deploying enhanced-idea-validation function..."
supabase functions deploy enhanced-idea-validation --no-verify-jwt

if [ $? -eq 0 ]; then
    echo "✅ Enhanced validation function deployed successfully"
else
    echo "❌ Failed to deploy enhanced validation function"
    exit 1
fi

echo "3️⃣ Setting environment variables for the function..."

# Set environment variables for the function
supabase secrets set --env-file .env.local

if [ $? -eq 0 ]; then
    echo "✅ Environment variables set successfully"
else
    echo "⚠️  Warning: Failed to set some environment variables. Please check manually."
fi

echo ""
echo "🎉 Enhanced Idea Validation deployed successfully!"
echo ""
echo "📋 What was deployed:"
echo "   - Database schema updates for real citations and market research"
echo "   - Enhanced validation function with Tavily + Firecrawl integration"
echo "   - New tables: competitor_analysis, market_research_queries, verified_citations"
echo ""
echo "🔑 Make sure these environment variables are set:"
echo "   - TAVILY_API_KEY"
echo "   - FIRECRAWL_API_KEY" 
echo "   - OPENAI_API_KEY or ANTHROPIC_API_KEY"
echo ""
echo "🧪 Test the enhanced validation in your application!"