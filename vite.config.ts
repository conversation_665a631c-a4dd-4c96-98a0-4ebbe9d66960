import { defineConfig } from "vite";
import react from "@vitejs/plugin-react-swc";
import path from "path";
import { componentTagger } from "lovable-tagger";

// https://vitejs.dev/config/
export default defineConfig(({ mode }) => ({
  server: {
    host: "::",
    port: 8080,
    headers: {
      'Cache-Control': 'no-cache'
    },
    // Handle large headers (common with authentication tokens)
    middlewareMode: false,
    hmr: {
      clientPort: 8080
    },
    // Increase header size limits
    proxy: {}
  },
  plugins: [
    react(),
    mode === 'development' &&
    componentTagger(),
  ].filter(Boolean),
  resolve: {
    alias: {
      "@": path.resolve(__dirname, "./src"),
    },
  },
  build: {
    chunkSizeWarningLimit: 800, // Increase the warning limit to 800kb
    // Use esbuild to completely exclude unused Mermaid modules
    minify: 'esbuild',
    target: 'es2015',
    cssTarget: 'chrome80',
    sourcemap: false,
    rollupOptions: {
      // Explicitly mark unused Mermaid modules as external
      external: [
        // Exclude all diagram types except sequence and quadrant
        'mermaid/dist/diagrams/flowchart/**',
        'mermaid/dist/diagrams/gantt/**',
        'mermaid/dist/diagrams/pie/**',
        'mermaid/dist/diagrams/class/**',
        'mermaid/dist/diagrams/state/**',
        'mermaid/dist/diagrams/er/**',
        'mermaid/dist/diagrams/journey/**',
        'mermaid/dist/diagrams/requirement/**',
        'mermaid/dist/diagrams/git/**',
        'mermaid/dist/diagrams/c4/**',
        'mermaid/dist/diagrams/mindmap/**',
        'mermaid/dist/diagrams/timeline/**',
        'mermaid/dist/diagrams/xychart/**',
        'mermaid/dist/diagrams/sankey/**',
        'mermaid/dist/diagrams/block/**',
        'mermaid/dist/diagrams/info/**',
        'mermaid/dist/diagrams/architecture/**',
        'mermaid/dist/diagrams/gitGraph/**',
        'mermaid/dist/diagrams/flowDiagram/**',
        'mermaid/dist/diagrams/classDiagram/**',
        'mermaid/dist/diagrams/stateDiagram/**',
        'mermaid/dist/diagrams/erDiagram/**',
        'mermaid/dist/diagrams/journeyDiagram/**',
        'mermaid/dist/diagrams/requirementDiagram/**',
        'mermaid/dist/diagrams/gitGraphDiagram/**',
        'mermaid/dist/diagrams/c4Diagram/**',
        'mermaid/dist/diagrams/mindmapDiagram/**',
        'mermaid/dist/diagrams/timelineDiagram/**',
        'mermaid/dist/diagrams/xychartDiagram/**',
        'mermaid/dist/diagrams/sankeyDiagram/**',
        'mermaid/dist/diagrams/blockDiagram/**',
        'mermaid/dist/diagrams/infoDiagram/**',
        'mermaid/dist/diagrams/architectureDiagram/**',
      ],
      output: {
        manualChunks: (id) => {
          // Group React and related libraries
          if (id.includes('node_modules/react') || 
              id.includes('node_modules/react-dom') || 
              id.includes('node_modules/react-router-dom')) {
            return 'react-vendor';
          }
          
          // Group UI components
          if (id.includes('node_modules/@radix-ui') || 
              id.includes('node_modules/class-variance-authority') ||
              id.includes('node_modules/clsx') ||
              id.includes('node_modules/tailwind')) {
            return 'ui-components';
          }
          
          // Create a single mermaid chunk
          if (id.includes('node_modules/mermaid')) {
            return 'mermaid';
          }
        }
      },
    },
    // Aggressive tree-shaking
    treeshake: {
      moduleSideEffects: false,
      propertyReadSideEffects: false,
    },
  },
  optimizeDeps: {
    include: ['mermaid/dist/mermaid.esm.min.mjs'],
    exclude: [
      'mermaid/dist/diagrams/flowchart',
      'mermaid/dist/diagrams/gantt',
      'mermaid/dist/diagrams/pie',
      'mermaid/dist/diagrams/class',
      'mermaid/dist/diagrams/state',
      'mermaid/dist/diagrams/er',
      'mermaid/dist/diagrams/journey',
      'mermaid/dist/diagrams/requirement',
      'mermaid/dist/diagrams/git',
      'mermaid/dist/diagrams/c4',
      'mermaid/dist/diagrams/mindmap',
      'mermaid/dist/diagrams/timeline',
      'mermaid/dist/diagrams/xychart',
      'mermaid/dist/diagrams/sankey',
      'mermaid/dist/diagrams/block',
      'mermaid/dist/diagrams/info',
      'mermaid/dist/diagrams/architecture',
    ]
  }
}));
