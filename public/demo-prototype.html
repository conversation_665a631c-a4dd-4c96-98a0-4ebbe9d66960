<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI-Powered Meal Planning App - Demo</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: '#3b82f6',
                        secondary: '#10b981',
                    }
                }
            }
        }
    </script>
</head>
<body class="bg-gray-50 font-sans">
    <!-- Header -->
    <header class="bg-white shadow-sm border-b border-primary/20">
        <div class="container mx-auto px-4 py-4">
            <div class="flex items-center justify-between">
                <h1 class="text-2xl font-bold text-primary">AI Meal Planner</h1>
                <div class="flex items-center space-x-4">
                    <button class="text-gray-600 hover:text-primary">Dashboard</button>
                    <button class="text-gray-600 hover:text-primary">Recipes</button>
                    <button class="text-gray-600 hover:text-primary">Meal Plans</button>
                    <button class="bg-primary text-white px-4 py-2 rounded-lg hover:bg-primary/90">
                        Sign Out
                    </button>
                </div>
            </div>
        </div>
    </header>

    <!-- Main Content -->
    <main class="container mx-auto px-4 py-8">
        <!-- Welcome Section -->
        <div class="bg-white rounded-lg shadow-sm p-6 mb-8">
            <div class="flex items-center justify-between mb-6">
                <div>
                    <h2 class="text-3xl font-bold text-gray-900">Welcome back, Demo User!</h2>
                    <p class="text-gray-600 mt-2">Let's plan some delicious meals for this week</p>
                </div>
                <div class="bg-gradient-to-r from-primary to-secondary text-white p-4 rounded-lg">
                    <div class="text-2xl font-bold">7</div>
                    <div class="text-sm">Meals Planned</div>
                </div>
            </div>
        </div>

        <!-- Quick Stats -->
        <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
            <div class="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
                <div class="flex items-center">
                    <div class="bg-primary/10 p-3 rounded-lg">
                        <svg class="w-6 h-6 text-primary" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2"></path>
                        </svg>
                    </div>
                    <div class="ml-4">
                        <h3 class="font-semibold text-gray-900">Saved Recipes</h3>
                        <p class="text-2xl font-bold text-primary">42</p>
                    </div>
                </div>
            </div>

            <div class="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
                <div class="flex items-center">
                    <div class="bg-secondary/10 p-3 rounded-lg">
                        <svg class="w-6 h-6 text-secondary" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                        </svg>
                    </div>
                    <div class="ml-4">
                        <h3 class="font-semibold text-gray-900">This Week</h3>
                        <p class="text-2xl font-bold text-secondary">7 meals</p>
                    </div>
                </div>
            </div>

            <div class="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
                <div class="flex items-center">
                    <div class="bg-orange-100 p-3 rounded-lg">
                        <svg class="w-6 h-6 text-orange-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
                        </svg>
                    </div>
                    <div class="ml-4">
                        <h3 class="font-semibold text-gray-900">Calories Avg</h3>
                        <p class="text-2xl font-bold text-orange-600">1,850</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Recent Recipes -->
        <div class="bg-white rounded-lg shadow-sm p-6 mb-8">
            <h3 class="text-xl font-semibold mb-4">AI Recommended Recipes</h3>
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                <div class="border rounded-lg p-4 hover:shadow-md transition-shadow">
                    <div class="bg-gray-200 h-32 rounded-lg mb-3"></div>
                    <h4 class="font-medium">Mediterranean Quinoa Bowl</h4>
                    <p class="text-sm text-gray-600">Healthy • 25 min • 420 cal</p>
                    <div class="flex items-center mt-2">
                        <div class="flex text-yellow-400">★★★★★</div>
                        <span class="text-sm text-gray-500 ml-2">4.8 (124)</span>
                    </div>
                </div>

                <div class="border rounded-lg p-4 hover:shadow-md transition-shadow">
                    <div class="bg-gray-200 h-32 rounded-lg mb-3"></div>
                    <h4 class="font-medium">Spicy Thai Curry</h4>
                    <p class="text-sm text-gray-600">Spicy • 30 min • 380 cal</p>
                    <div class="flex items-center mt-2">
                        <div class="flex text-yellow-400">★★★★☆</div>
                        <span class="text-sm text-gray-500 ml-2">4.6 (89)</span>
                    </div>
                </div>

                <div class="border rounded-lg p-4 hover:shadow-md transition-shadow">
                    <div class="bg-gray-200 h-32 rounded-lg mb-3"></div>
                    <h4 class="font-medium">Grilled Salmon & Veggies</h4>
                    <p class="text-sm text-gray-600">Protein • 20 min • 450 cal</p>
                    <div class="flex items-center mt-2">
                        <div class="flex text-yellow-400">★★★★★</div>
                        <span class="text-sm text-gray-500 ml-2">4.9 (156)</span>
                    </div>
                </div>
            </div>
        </div>

        <!-- Weekly Meal Plan -->
        <div class="bg-white rounded-lg shadow-sm p-6">
            <div class="flex items-center justify-between mb-4">
                <h3 class="text-xl font-semibold">This Week's Meal Plan</h3>
                <button class="bg-primary text-white px-4 py-2 rounded-lg hover:bg-primary/90">
                    Generate New Plan
                </button>
            </div>
            <div class="grid grid-cols-1 md:grid-cols-7 gap-4">
                <div class="text-center">
                    <div class="font-medium text-gray-900 mb-2">Monday</div>
                    <div class="space-y-2">
                        <div class="bg-blue-50 p-2 rounded text-xs">Oatmeal Bowl</div>
                        <div class="bg-green-50 p-2 rounded text-xs">Caesar Salad</div>
                        <div class="bg-orange-50 p-2 rounded text-xs">Grilled Chicken</div>
                    </div>
                </div>
                <div class="text-center">
                    <div class="font-medium text-gray-900 mb-2">Tuesday</div>
                    <div class="space-y-2">
                        <div class="bg-blue-50 p-2 rounded text-xs">Greek Yogurt</div>
                        <div class="bg-green-50 p-2 rounded text-xs">Quinoa Bowl</div>
                        <div class="bg-orange-50 p-2 rounded text-xs">Thai Curry</div>
                    </div>
                </div>
                <div class="text-center">
                    <div class="font-medium text-gray-900 mb-2">Wednesday</div>
                    <div class="space-y-2">
                        <div class="bg-blue-50 p-2 rounded text-xs">Smoothie Bowl</div>
                        <div class="bg-green-50 p-2 rounded text-xs">Wrap & Soup</div>
                        <div class="bg-orange-50 p-2 rounded text-xs">Salmon & Rice</div>
                    </div>
                </div>
                <div class="text-center">
                    <div class="font-medium text-gray-900 mb-2">Thursday</div>
                    <div class="space-y-2">
                        <div class="bg-blue-50 p-2 rounded text-xs">Avocado Toast</div>
                        <div class="bg-green-50 p-2 rounded text-xs">Buddha Bowl</div>
                        <div class="bg-orange-50 p-2 rounded text-xs">Pasta Primavera</div>
                    </div>
                </div>
                <div class="text-center">
                    <div class="font-medium text-gray-900 mb-2">Friday</div>
                    <div class="space-y-2">
                        <div class="bg-blue-50 p-2 rounded text-xs">Pancakes</div>
                        <div class="bg-green-50 p-2 rounded text-xs">Sushi Bowl</div>
                        <div class="bg-orange-50 p-2 rounded text-xs">Pizza Night</div>
                    </div>
                </div>
                <div class="text-center">
                    <div class="font-medium text-gray-900 mb-2">Saturday</div>
                    <div class="space-y-2">
                        <div class="bg-blue-50 p-2 rounded text-xs">French Toast</div>
                        <div class="bg-green-50 p-2 rounded text-xs">Brunch Special</div>
                        <div class="bg-orange-50 p-2 rounded text-xs">BBQ Ribs</div>
                    </div>
                </div>
                <div class="text-center">
                    <div class="font-medium text-gray-900 mb-2">Sunday</div>
                    <div class="space-y-2">
                        <div class="bg-blue-50 p-2 rounded text-xs">Bagel & Lox</div>
                        <div class="bg-green-50 p-2 rounded text-xs">Family Lunch</div>
                        <div class="bg-orange-50 p-2 rounded text-xs">Comfort Food</div>
                    </div>
                </div>
            </div>
        </div>
    </main>

    <!-- Footer -->
    <footer class="bg-white border-t mt-12">
        <div class="container mx-auto px-4 py-6 text-center text-sm text-gray-600">
            © 2024 AI Meal Planner. Generated prototype by Incepta.
        </div>
    </footer>

    <script>
        // Add some interactivity
        document.addEventListener('DOMContentLoaded', function() {
            // Add click handlers for demo purposes
            const buttons = document.querySelectorAll('button');
            buttons.forEach(button => {
                button.addEventListener('click', function() {
                    if (this.textContent.includes('Generate New Plan')) {
                        alert('🤖 AI is generating a new meal plan based on your preferences!');
                    } else if (this.textContent.includes('Sign Out')) {
                        alert('👋 Thanks for using AI Meal Planner!');
                    }
                });
            });

            // Add hover effects to recipe cards
            const recipeCards = document.querySelectorAll('.hover\\:shadow-md');
            recipeCards.forEach(card => {
                card.addEventListener('click', function() {
                    alert('🍽️ Recipe details would open here in the full app!');
                });
            });
        });
    </script>
</body>
</html>
