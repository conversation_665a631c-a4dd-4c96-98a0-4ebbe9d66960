<!DOCTYPE html>
<html>
<head>
    <title>Test Edge Function</title>
</head>
<body>
    <h1>Edge Function Test</h1>
    <button onclick="testFunction()">Test Edge Function</button>
    <div id="result"></div>

    <script>
        async function testFunction() {
            const resultDiv = document.getElementById('result');
            resultDiv.innerHTML = 'Testing...';
            
            try {
                const response = await fetch('https://hvljvkfpbavbtlxzjkqs.supabase.co/functions/v1/generate-prototype', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': 'Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Imh2bGp2a2ZwYmF2YnRseHpqa3FzIiwicm9sZSI6ImFub24iLCJpYXQiOjE3MzQ5NzE0NzQsImV4cCI6MjA1MDU0NzQ3NH0.Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8'
                    },
                    body: JSON.stringify({
                        prototypeId: 'test-id'
                    })
                });
                
                const data = await response.text();
                resultDiv.innerHTML = `
                    <h3>Response:</h3>
                    <p>Status: ${response.status}</p>
                    <p>Headers: ${JSON.stringify([...response.headers.entries()])}</p>
                    <pre>${data}</pre>
                `;
            } catch (error) {
                resultDiv.innerHTML = `<p style="color: red;">Error: ${error.message}</p>`;
            }
        }
    </script>
</body>
</html>
