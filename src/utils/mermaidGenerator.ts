
export const generateFeaturePrioritizationDiagram = (validation: any) => {
  try {
    console.log("Generating feature prioritization diagram");
    
    if (!validation?.projectConcept?.features) {
      console.warn("No features found in validation data");
      return 'sequenceDiagram\n  participant User\n  Note over User: No feature data available';
    }
    
    const features = validation.projectConcept.features;
    let diagram = 'sequenceDiagram\n';
    
    // Add sequence for core features
    if (features.core && features.core.length > 0) {
      diagram += '  participant Core as Core Features\n';
      features.core.forEach((feature: any, index: number) => {
        const safeDesc = (feature.description || '').replace(/[^\w\s]/gi, '');
        const shortDesc = safeDesc.length > 30 ? safeDesc.substring(0, 27) + '...' : safeDesc;
        diagram += `  Note over Core: ${shortDesc}\n`;
      });
    }
    
    // Add sequence for must-have features
    if (features.mustHave && features.mustHave.length > 0) {
      diagram += '  participant Must as Must-Have\n';
      features.mustHave.forEach((feature: any) => {
        const safeDesc = (feature.description || '').replace(/[^\w\s]/gi, '');
        const shortDesc = safeDesc.length > 30 ? safeDesc.substring(0, 27) + '...' : safeDesc;
        diagram += `  Note over Must: ${shortDesc}\n`;
      });
    }

    // Add sequence for should-have features
    if (features.shouldHave && features.shouldHave.length > 0) {
      diagram += '  participant Should as Should-Have\n';
      features.shouldHave.forEach((feature: any) => {
        const safeDesc = (feature.description || '').replace(/[^\w\s]/gi, '');
        const shortDesc = safeDesc.length > 30 ? safeDesc.substring(0, 27) + '...' : safeDesc;
        diagram += `  Note over Should: ${shortDesc}\n`;
      });
    }
    
    // Add some flow arrows for visual appeal
    if (features.core && features.core.length > 0 && features.mustHave && features.mustHave.length > 0) {
      diagram += '  Core->>Must: Development Flow\n';
    }
    
    if (features.mustHave && features.mustHave.length > 0 && features.shouldHave && features.shouldHave.length > 0) {
      diagram += '  Must->>Should: Development Flow\n';
    }
    
    console.log("Feature prioritization diagram generated successfully");
    return diagram;
  } catch (error) {
    console.error("Error generating feature prioritization diagram:", error);
    return 'sequenceDiagram\n  participant Error\n  Note over Error: Error generating diagram';
  }
};

export const generateMarketOpportunityDiagram = (validation: any) => {
  try {
    console.log("Generating market opportunity diagram");
    
    // Create a default diagram with sample data if no validation data is found
    // This ensures users always see something meaningful
    if (!validation || !validation.opportunities || validation.opportunities.length === 0) {
      console.log("Creating placeholder market opportunity diagram");
      
      // Create a quadrant chart with placeholder data
      let diagram = 'quadrantChart\n';
      diagram += '  title Market Opportunity Analysis\n';
      diagram += '  x-axis Low Market Fit --> High Market Fit\n';
      diagram += '  y-axis Low Effort --> High Effort\n';
      diagram += '  quadrant-1 Quick Wins\n';
      diagram += '  quadrant-2 Strategic Initiatives\n';
      diagram += '  quadrant-3 Reconsider\n';
      diagram += '  quadrant-4 Resource Intensive\n';
      
      // Add placeholder opportunities with sample positions
      diagram += '  "Validate first prototype": [0.2, 0.3]\n';
      diagram += '  "Target early adopters": [0.7, 0.3]\n';
      diagram += '  "Enterprise expansion": [0.9, 0.8]\n';
      diagram += '  "Feature development": [0.5, 0.5]\n';
      diagram += '  "Market research": [0.3, 0.7]\n';
      
      return diagram;
    }
    
    // Process the market data
    const market = validation;
    
    // Create a basic quadrant chart
    let diagram = 'quadrantChart\n';
    diagram += '  title Market Opportunity Analysis\n';
    diagram += '  x-axis Low Market Fit --> High Market Fit\n';
    diagram += '  y-axis Low Effort --> High Effort\n';
    diagram += '  quadrant-1 Quick Wins\n';
    diagram += '  quadrant-2 Strategic Initiatives\n';
    diagram += '  quadrant-3 Reconsider\n';
    diagram += '  quadrant-4 Resource Intensive\n';
    
    // Add opportunities as points (using safe position values)
    const opportunities = Array.isArray(market.opportunities) ? market.opportunities : 
                         (market.opportunities ? [market.opportunities] : []);
    
    if (opportunities.length > 0) {
      opportunities.forEach((opp: string, index: number) => {
        // Generate pseudo-random but consistent positions based on the string content
        const hashCode = (str: string) => {
          let hash = 0;
          for (let i = 0; i < str.length; i++) {
            hash = ((hash << 5) - hash) + str.charCodeAt(i);
            hash |= 0;
          }
          return hash;
        };
        
        const hash = Math.abs(hashCode(opp));
        // Use modulo to get values between 0.1 and 0.9
        const x = 0.1 + (hash % 9) * 0.1;
        const y = 0.1 + ((hash >> 4) % 9) * 0.1;
        
        // Truncate and sanitize the opportunity text
        const sanitized = typeof opp === 'string' ? opp.replace(/[^\w\s]/gi, '') : `Opportunity ${index + 1}`;
        const shortOpp = sanitized.length > 20 ? sanitized.substring(0, 17) + '...' : sanitized;
        
        // Add the point to the diagram
        diagram += `  "${shortOpp}": [${x.toFixed(1)}, ${y.toFixed(1)}]\n`;
      });
    } else if (market.challenges && market.challenges.length > 0) {
      // Use challenges as an alternative if no opportunities exist
      market.challenges.forEach((challenge: string, index: number) => {
        const hashCode = (str: string) => {
          let hash = 0;
          for (let i = 0; i < str.length; i++) {
            hash = ((hash << 5) - hash) + str.charCodeAt(i);
            hash |= 0;
          }
          return hash;
        };
        
        const hash = Math.abs(hashCode(challenge));
        const x = 0.1 + (hash % 9) * 0.1;
        const y = 0.1 + ((hash >> 4) % 9) * 0.1;
        
        const sanitized = typeof challenge === 'string' ? challenge.replace(/[^\w\s]/gi, '') : `Challenge ${index + 1}`;
        const shortChallenge = sanitized.length > 20 ? sanitized.substring(0, 17) + '...' : sanitized;
        
        diagram += `  "${shortChallenge}": [${x.toFixed(1)}, ${y.toFixed(1)}]\n`;
      });
    } else {
      // Add placeholder points if no opportunities or challenges exist
      diagram += '  "Market entry": [0.6, 0.3]\n';
      diagram += '  "Customer acquisition": [0.8, 0.6]\n';
      diagram += '  "Product development": [0.4, 0.7]\n';
    }
    
    console.log("Market opportunity diagram generated successfully");
    return diagram;
  } catch (error) {
    console.error("Error generating market opportunity diagram:", error);
    
    // Return a basic diagram in case of error
    let diagram = 'quadrantChart\n';
    diagram += '  title Market Opportunity\n';
    diagram += '  x-axis Low Opportunity --> High Opportunity\n';
    diagram += '  y-axis Low Effort --> High Effort\n';
    diagram += '  quadrant-1 Quick Wins\n';
    diagram += '  quadrant-2 Strategic\n';
    diagram += '  quadrant-3 Avoid\n';
    diagram += '  quadrant-4 Time-consuming\n';
    diagram += '  "Error generating diagram": [0.5, 0.5]\n';
    
    return diagram;
  }
};

export const generateConsolidatedUserSequenceDiagram = (validationData: any) => {
  try {
    console.log("Generating consolidated user sequence diagram");
    
    if (!validationData?.projectConcept?.features || !validationData?.userRoles) {
      console.warn("No features or user roles found in validation data");
      return 'sequenceDiagram\n  participant User\n  Note over User: No feature data available';
    }
    
    // Extract data
    const features = validationData.projectConcept.features;
    const userRoles = validationData.userRoles;
    
    // Start building the diagram
    let diagram = 'sequenceDiagram\n';
    
    // Add all user roles as participants
    const userRoleMap = new Map();
    userRoles.forEach((role: any, index: number) => {
      const roleId = `Role${index + 1}`;
      userRoleMap.set(role.title, roleId);
      diagram += `  participant ${roleId} as ${role.title}\n`;
    });
    
    // Add System as a participant
    diagram += '  participant System\n\n';
    
    // Helper function to add feature interactions
    const addFeatureInteractions = (featureList: any[], priority: string) => {
      if (!featureList || featureList.length === 0) return;
      
      diagram += `  Note over System: --- ${priority.toUpperCase()} FEATURES ---\n`;
      
      featureList.forEach((feature: any, index: number) => {
        // Skip if no description or user_roles
        if (!feature.description || !feature.user_roles || feature.user_roles.length === 0) return;
        
        const safeDesc = (feature.description || '').replace(/[^\w\s]/gi, '');
        const shortDesc = safeDesc.length > 35 ? safeDesc.substring(0, 32) + '...' : safeDesc;
        
        // Get the first user role that can use this feature
        const primaryRoleTitle = feature.user_roles[0];
        const primaryRole = userRoleMap.get(primaryRoleTitle);
        
        if (!primaryRole) return;
        
        // Add user interaction with system - Use correct arrow syntax
        diagram += `  ${primaryRole}->System: Request "${shortDesc}"\n`;
        
        // System processes the request
        diagram += `  System-->>System: Process request\n`;
        
        // System responds back to the user - Use correct arrow syntax
        diagram += `  System->${primaryRole}: Deliver result\n`;
        
        // If there are other roles who can access this feature, show collaboration
        if (feature.user_roles.length > 1) {
          const secondaryRoleTitle = feature.user_roles[1];
          const secondaryRole = userRoleMap.get(secondaryRoleTitle);
          
          if (secondaryRole) {
            // Use correct arrow syntax
            diagram += `  ${primaryRole}->${secondaryRole}: Collaborate on feature\n`;
          }
        }
        
        // Add a small separator between features
        if (index < featureList.length - 1) {
          diagram += '\n';
        }
      });
    };
    
    // Add interactions for each feature category
    addFeatureInteractions(features.core, 'Core');
    addFeatureInteractions(features.mustHave, 'Must-Have');
    addFeatureInteractions(features.shouldHave, 'Should-Have');
    addFeatureInteractions(features.niceToHave, 'Nice-to-Have');
    addFeatureInteractions(features.future, 'Future');
    
    console.log("Consolidated user sequence diagram generated successfully");
    return diagram;
  } catch (error) {
    console.error("Error generating consolidated user sequence diagram:", error);
    return 'sequenceDiagram\n  participant Error\n  Note over Error: Error generating diagram';
  }
};

export const generateCategorySequenceDiagram = (features: any) => {
  try {
    console.log("Generating category sequence diagram");
    
    if (!features) {
      console.warn("No features found in validation data");
      return 'sequenceDiagram\n  participant User\n  Note over User: No feature data available';
    }
    
    // Start building the diagram
    let diagram = 'sequenceDiagram\n';
    
    // Add all feature categories as participants
    const categories = ['core', 'mustHave', 'shouldHave', 'niceToHave', 'future'];
    
    categories.forEach(category => {
      if (features[category] && features[category].length > 0) {
        const catName = getCategoryTitle(category);
        diagram += `  participant ${category} as "${catName}"\n`;
      }
    });
    
    // Add System as a participant
    diagram += '  participant System\n\n';
    
    // Add interactions between feature categories
    let previousCategory = null;
    
    categories.forEach(category => {
      if (features[category] && features[category].length > 0) {
        // Add a note about the feature count
        diagram += `  Note over ${category}: ${features[category].length} features\n`;
        
        // If there's a previous category, show progression
        if (previousCategory) {
          diagram += `  ${previousCategory}->>${category}: Development Flow\n`;
        }
        
        // Show interaction with system
        diagram += `  ${category}->System: Implementation\n`;
        diagram += `  System-->${category}: Delivery\n\n`;
        
        previousCategory = category;
      }
    });
    
    console.log("Category sequence diagram generated successfully");
    return diagram;
  } catch (error) {
    console.error("Error generating category sequence diagram:", error);
    return 'sequenceDiagram\n  participant Error\n  Note over Error: Error generating diagram';
  }
};

export const getCategoryTitle = (category: string): string => {
  switch (category) {
    case 'core': return 'Core Features';
    case 'mustHave': return 'Must-Have Features';
    case 'shouldHave': return 'Should-Have Features';
    case 'niceToHave': return 'Nice-to-Have Features';
    case 'future': return 'Future Features';
    default: return `${category.charAt(0).toUpperCase() + category.slice(1)} Features`;
  }
};
