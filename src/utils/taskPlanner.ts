import { ProjectConfig, PrototypeTask, TaskDependencyGraph } from "@/types";

export interface TaskTemplate {
  title: string;
  description: string;
  task_type: PrototypeTask['task_type'];
  estimated_duration: number;
  dependencies: string[];
  template_id: string;
  required_for?: string[]; // Features this task is required for
}

// Base task templates for any prototype
const BASE_TASK_TEMPLATES: TaskTemplate[] = [
  {
    template_id: 'setup-project',
    title: 'Initialize Project Structure',
    description: 'Set up React + TypeScript project with Vite, install dependencies, configure Tailwind CSS',
    task_type: 'setup',
    estimated_duration: 10,
    dependencies: []
  },
  {
    template_id: 'setup-routing',
    title: 'Configure Routing',
    description: 'Set up React Router for navigation between pages',
    task_type: 'setup',
    estimated_duration: 5,
    dependencies: ['setup-project']
  },
  {
    template_id: 'setup-theme',
    title: 'Configure Theme System',
    description: 'Set up theme provider with custom colors and typography',
    task_type: 'styling',
    estimated_duration: 8,
    dependencies: ['setup-project']
  },
  {
    template_id: 'create-layout',
    title: 'Create App Layout',
    description: 'Build main layout component with header, navigation, and footer',
    task_type: 'component',
    estimated_duration: 15,
    dependencies: ['setup-routing', 'setup-theme']
  },
  {
    template_id: 'setup-auth-mock',
    title: 'Setup Mock Authentication',
    description: 'Create mock authentication system with login/logout functionality',
    task_type: 'service',
    estimated_duration: 12,
    dependencies: ['setup-project']
  },
  {
    template_id: 'create-auth-pages',
    title: 'Create Authentication Pages',
    description: 'Build login and signup pages with forms',
    task_type: 'page',
    estimated_duration: 20,
    dependencies: ['setup-auth-mock', 'create-layout']
  },
  {
    template_id: 'create-dashboard',
    title: 'Create Dashboard Page',
    description: 'Build main dashboard with navigation and overview',
    task_type: 'page',
    estimated_duration: 15,
    dependencies: ['create-auth-pages']
  },
  {
    template_id: 'setup-mock-api',
    title: 'Setup Mock API Services',
    description: 'Create mock API services using MSW or similar for data operations',
    task_type: 'service',
    estimated_duration: 10,
    dependencies: ['setup-project']
  }
];

// Feature-specific task templates
const FEATURE_TASK_TEMPLATES: Record<string, TaskTemplate[]> = {
  'user-management': [
    {
      template_id: 'create-user-profile',
      title: 'Create User Profile Component',
      description: 'Build user profile display and edit components',
      task_type: 'component',
      estimated_duration: 15,
      dependencies: ['create-layout', 'setup-mock-api']
    },
    {
      template_id: 'create-user-list',
      title: 'Create User List Page',
      description: 'Build page to display and manage users',
      task_type: 'page',
      estimated_duration: 20,
      dependencies: ['create-user-profile']
    }
  ],
  'data-visualization': [
    {
      template_id: 'setup-charts',
      title: 'Setup Chart Library',
      description: 'Install and configure chart library (Recharts)',
      task_type: 'setup',
      estimated_duration: 5,
      dependencies: ['setup-project']
    },
    {
      template_id: 'create-chart-components',
      title: 'Create Chart Components',
      description: 'Build reusable chart components for data visualization',
      task_type: 'component',
      estimated_duration: 25,
      dependencies: ['setup-charts', 'create-layout']
    },
    {
      template_id: 'create-analytics-page',
      title: 'Create Analytics Dashboard',
      description: 'Build analytics page with various charts and metrics',
      task_type: 'page',
      estimated_duration: 30,
      dependencies: ['create-chart-components']
    }
  ],
  'file-upload': [
    {
      template_id: 'create-file-upload',
      title: 'Create File Upload Component',
      description: 'Build drag-and-drop file upload component',
      task_type: 'component',
      estimated_duration: 20,
      dependencies: ['create-layout']
    },
    {
      template_id: 'create-file-manager',
      title: 'Create File Manager Page',
      description: 'Build page to manage uploaded files',
      task_type: 'page',
      estimated_duration: 25,
      dependencies: ['create-file-upload']
    }
  ],
  'messaging': [
    {
      template_id: 'create-chat-component',
      title: 'Create Chat Component',
      description: 'Build real-time chat interface component',
      task_type: 'component',
      estimated_duration: 30,
      dependencies: ['create-layout', 'setup-mock-api']
    },
    {
      template_id: 'create-messaging-page',
      title: 'Create Messaging Page',
      description: 'Build messaging page with chat functionality',
      task_type: 'page',
      estimated_duration: 20,
      dependencies: ['create-chat-component']
    }
  ],
  'e-commerce': [
    {
      template_id: 'create-product-components',
      title: 'Create Product Components',
      description: 'Build product card, list, and detail components',
      task_type: 'component',
      estimated_duration: 25,
      dependencies: ['create-layout', 'setup-mock-api']
    },
    {
      template_id: 'create-shopping-cart',
      title: 'Create Shopping Cart',
      description: 'Build shopping cart functionality and UI',
      task_type: 'component',
      estimated_duration: 20,
      dependencies: ['create-product-components']
    },
    {
      template_id: 'create-checkout-page',
      title: 'Create Checkout Page',
      description: 'Build checkout process with forms',
      task_type: 'page',
      estimated_duration: 30,
      dependencies: ['create-shopping-cart']
    }
  ]
};

// Helper function to generate UUID
function generateUUID(): string {
  return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {
    const r = Math.random() * 16 | 0;
    const v = c == 'x' ? r : (r & 0x3 | 0x8);
    return v.toString(16);
  });
}

export function generateTaskPlan(config: ProjectConfig, validationData?: any): PrototypeTask[] {
  const tasks: PrototypeTask[] = [];
  const taskIdMap = new Map<string, string>();
  let orderIndex = 0;

  // Base setup tasks
  const setupTasks = [
    {
      template_id: 'setup-project',
      title: 'Initialize React + TypeScript Project',
      description: 'Set up Vite project with TypeScript, Tailwind CSS, and essential dependencies',
      task_type: 'setup' as const,
      estimated_duration: 3,
      dependencies: []
    },
    {
      template_id: 'setup-theme',
      title: 'Configure App Theme & Styling',
      description: `Apply custom colors (${config.colors.primary}, ${config.colors.secondary}) and typography`,
      task_type: 'styling' as const,
      estimated_duration: 2,
      dependencies: ['setup-project']
    },
    {
      template_id: 'create-layout',
      title: 'Build Main App Layout',
      description: 'Create responsive header, navigation, and layout components',
      task_type: 'component' as const,
      estimated_duration: 4,
      dependencies: ['setup-theme']
    }
  ];

  // Add setup tasks
  setupTasks.forEach(template => {
    const taskId = generateUUID();
    taskIdMap.set(template.template_id, taskId);
    tasks.push({
      id: taskId,
      prototype_id: '',
      feature_id: '',
      title: template.title,
      description: template.description,
      task_type: template.task_type,
      status: 'pending',
      parent_task_id: undefined,
      dependencies: template.dependencies,
      order_index: orderIndex++,
      estimated_duration: template.estimated_duration,
      generated_files: [],
      created_at: new Date().toISOString()
    });
  });

  // Generate feature-specific tasks from validation data
  if (validationData?.projectConcept?.features) {
    const featureTasks = generateFeatureBasedTasks(validationData, config);
    featureTasks.forEach(task => {
      const taskId = generateUUID();
      tasks.push({
        ...task,
        id: taskId,
        prototype_id: '',
        order_index: orderIndex++,
        dependencies: ['create-layout'], // Depend on layout
        created_at: new Date().toISOString()
      });
    });
  }

  // Add final integration task (no deployment)
  const integrationTaskId = generateUUID();
  tasks.push({
    id: integrationTaskId,
    prototype_id: '',
    feature_id: '',
    title: 'Integrate Features & Add Mock Data',
    description: 'Connect all features, add realistic mock data, and ensure smooth interactions',
    task_type: 'integration',
    status: 'pending',
    parent_task_id: undefined,
    dependencies: tasks.slice(-3).map(t => t.id),
    order_index: orderIndex,
    estimated_duration: 5,
    generated_files: [],
    created_at: new Date().toISOString()
  });

  // Resolve dependencies to UUIDs
  tasks.forEach(task => {
    if (task.dependencies.length > 0) {
      task.dependencies = task.dependencies.map(dep => {
        return taskIdMap.get(dep) || dep;
      });
    }
  });

  return tasks;
}

function generateFeatureBasedTasks(validationData: any, config: ProjectConfig): Partial<PrototypeTask>[] {
  const tasks: Partial<PrototypeTask>[] = [];
  const features = validationData.projectConcept.features;

  // Process core features
  if (features.core) {
    features.core.forEach((feature: any, index: number) => {
      tasks.push({
        feature_id: `core-${index}`,
        title: `Implement ${feature.description || feature.title || 'Core Feature'}`,
        description: `Build interactive ${feature.description} with real functionality and mock data`,
        task_type: 'component',
        status: 'pending',
        estimated_duration: 6
      });
    });
  }

  // Process must-have features
  if (features.mustHave) {
    features.mustHave.forEach((feature: any, index: number) => {
      tasks.push({
        feature_id: `mustHave-${index}`,
        title: `Create ${feature.description || feature.title || 'Essential Feature'}`,
        description: `Develop ${feature.description} with interactive UI and backend simulation`,
        task_type: 'page',
        status: 'pending',
        estimated_duration: 8
      });
    });
  }

  return tasks;
}

function createTaskFromTemplate(template: TaskTemplate, featureId: string, orderIndex: number, taskId: string): PrototypeTask {
  return {
    id: taskId,
    prototype_id: '', // Will be set when creating the prototype
    feature_id: featureId,
    title: template.title,
    description: template.description,
    task_type: template.task_type,
    status: 'pending',
    parent_task_id: undefined,
    dependencies: template.dependencies, // Will be resolved to UUIDs in second pass
    order_index: orderIndex,
    estimated_duration: template.estimated_duration,
    generated_files: [],
    created_at: new Date().toISOString()
  };
}

function mapFeaturesToTaskTemplates(selectedFeatures: string[]): Array<{ featureId: string; templates: TaskTemplate[] }> {
  const mapping: Array<{ featureId: string; templates: TaskTemplate[] }> = [];

  selectedFeatures.forEach(featureId => {
    // Simple keyword matching to determine feature type
    if (featureId.includes('user') || featureId.includes('profile') || featureId.includes('account')) {
      mapping.push({ featureId, templates: FEATURE_TASK_TEMPLATES['user-management'] || [] });
    } else if (featureId.includes('chart') || featureId.includes('analytics') || featureId.includes('report')) {
      mapping.push({ featureId, templates: FEATURE_TASK_TEMPLATES['data-visualization'] || [] });
    } else if (featureId.includes('upload') || featureId.includes('file') || featureId.includes('document')) {
      mapping.push({ featureId, templates: FEATURE_TASK_TEMPLATES['file-upload'] || [] });
    } else if (featureId.includes('chat') || featureId.includes('message') || featureId.includes('communication')) {
      mapping.push({ featureId, templates: FEATURE_TASK_TEMPLATES['messaging'] || [] });
    } else if (featureId.includes('product') || featureId.includes('shop') || featureId.includes('cart') || featureId.includes('payment')) {
      mapping.push({ featureId, templates: FEATURE_TASK_TEMPLATES['e-commerce'] || [] });
    }
  });

  return mapping;
}

export function createDependencyGraph(tasks: PrototypeTask[]): TaskDependencyGraph {
  const nodes = tasks;
  const edges: { from: string; to: string }[] = [];

  // Create edges based on dependencies
  tasks.forEach(task => {
    task.dependencies.forEach(depId => {
      edges.push({ from: depId, to: task.id });
    });
  });

  // Calculate critical path (simplified - longest path through the graph)
  const criticalPath = calculateCriticalPath(tasks);

  return {
    nodes,
    edges,
    criticalPath
  };
}

function calculateCriticalPath(tasks: PrototypeTask[]): string[] {
  // Simplified critical path calculation
  // In a real implementation, you'd use proper graph algorithms
  const taskMap = new Map(tasks.map(t => [t.id, t]));
  const visited = new Set<string>();
  const path: string[] = [];

  function dfs(taskId: string, currentPath: string[]): string[] {
    if (visited.has(taskId)) return currentPath;
    
    visited.add(taskId);
    const task = taskMap.get(taskId);
    if (!task) return currentPath;

    const newPath = [...currentPath, taskId];
    
    // Find tasks that depend on this task
    const dependentTasks = tasks.filter(t => t.dependencies.includes(taskId));
    
    if (dependentTasks.length === 0) {
      return newPath;
    }

    // Return the longest path
    let longestPath = newPath;
    dependentTasks.forEach(depTask => {
      const pathFromDep = dfs(depTask.id, newPath);
      if (pathFromDep.length > longestPath.length) {
        longestPath = pathFromDep;
      }
    });

    return longestPath;
  }

  // Start from tasks with no dependencies
  const rootTasks = tasks.filter(t => t.dependencies.length === 0);
  rootTasks.forEach(task => {
    const pathFromRoot = dfs(task.id, []);
    if (pathFromRoot.length > path.length) {
      path.splice(0, path.length, ...pathFromRoot);
    }
  });

  return path;
}

export function estimateCompletionTime(tasks: PrototypeTask[]): number {
  // Calculate total estimated time considering dependencies
  const criticalPath = calculateCriticalPath(tasks);
  const taskMap = new Map(tasks.map(t => [t.id, t]));
  
  return criticalPath.reduce((total, taskId) => {
    const task = taskMap.get(taskId);
    return total + (task?.estimated_duration || 0);
  }, 0);
}
