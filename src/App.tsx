
import { Toaster } from "@/components/ui/toaster";
import { Toaster as Sonner } from "@/components/ui/sonner";
import { TooltipProvider } from "@/components/ui/tooltip";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { BrowserRouter, Routes, Route, Navigate } from "react-router-dom";
import { useEffect } from "react";
import Index from "./pages/Index";
import Login from "./pages/Login";
import Signup from "./pages/Signup";
import Dashboard from "./pages/Dashboard";
import ValidateIdea from "./pages/ValidateIdea";
import ValidationResults from "./pages/ValidationResults";
import ProjectPlanning from "./pages/ProjectPlanning";
import Roadmap from "./pages/Roadmap";
import UserRoadmaps from "./pages/UserRoadmaps";
import Pricing from "./pages/Pricing";
import Checkout from "./pages/Checkout";
import Payment from "./pages/Payment";
import AdminDashboard from "./pages/AdminDashboard";
import AdminRoadmaps from "./pages/AdminRoadmaps";
import UserManagement from "./pages/UserManagement";
import AISettings from "./pages/AISettings";
import PlanSettings from "./pages/PlanSettings";
import Profile from "./pages/Profile";
import NotFound from "./pages/NotFound";
import ProtectedRoute from "./components/layout/ProtectedRoute";
import { initAuthListener } from "./store/authStore";
import { initSubscriptionListener } from "./store/subscriptionStore";

const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      refetchOnWindowFocus: false,
      staleTime: 30 * 1000, // 30 seconds
    },
  },
});

const App = () => {
  useEffect(() => {
    // Initialize listeners
    const authSubscription = initAuthListener();
    const subscriptionListener = initSubscriptionListener();
    
    // Clean up subscriptions on unmount
    return () => {
      authSubscription.unsubscribe();
      subscriptionListener.unsubscribe();
    };
  }, []);

  return (
    <QueryClientProvider client={queryClient}>
      <TooltipProvider>
        <Toaster />
        <Sonner />
        <BrowserRouter>
          <Routes>
            <Route path="/" element={<Index />} />
            <Route path="/login" element={<Login />} />
            <Route path="/signup" element={<Signup />} />
            <Route
              path="/dashboard"
              element={
                <ProtectedRoute>
                  <Dashboard />
                </ProtectedRoute>
              }
            />
            <Route
              path="/validate-idea"
              element={
                <ProtectedRoute>
                  <ValidateIdea />
                </ProtectedRoute>
              }
            />
            <Route
              path="/validate-idea/results"
              element={
                <ProtectedRoute>
                  <ValidationResults />
                </ProtectedRoute>
              }
            />
            <Route
              path="/validate-idea/results/:id"
              element={
                <ProtectedRoute>
                  <ValidationResults />
                </ProtectedRoute>
              }
            />
            {/* Project planning and roadmap routes */}
            <Route
              path="/validate-idea/results/:id/planning"
              element={
                <ProtectedRoute>
                  <ProjectPlanning />
                </ProtectedRoute>
              }
            />
            <Route
              path="/validate-idea/results/:id/roadmap"
              element={
                <ProtectedRoute>
                  <Roadmap />
                </ProtectedRoute>
              }
            />
            {/* Direct roadmap routes */}
            <Route
              path="/roadmap/:id"
              element={
                <ProtectedRoute>
                  <Roadmap />
                </ProtectedRoute>
              }
            />
            <Route
              path="/roadmaps"
              element={
                <ProtectedRoute>
                  <UserRoadmaps />
                </ProtectedRoute>
              }
            />
            
            <Route
              path="/pricing"
              element={
                <ProtectedRoute>
                  <Pricing />
                </ProtectedRoute>
              }
            />
            
            {/* Add redirect from /roadmap-packages to /pricing */}
            <Route
              path="/roadmap-packages"
              element={
                <ProtectedRoute>
                  <Navigate to="/pricing" replace />
                </ProtectedRoute>
              }
            />
            
            <Route
              path="/checkout/:planId"
              element={
                <ProtectedRoute>
                  <Checkout />
                </ProtectedRoute>
              }
            />
            <Route
              path="/payment/:planId"
              element={
                <ProtectedRoute>
                  <Payment />
                </ProtectedRoute>
              }
            />
            <Route
              path="/user/profile"
              element={
                <ProtectedRoute>
                  <Profile />
                </ProtectedRoute>
              }
            />
            {/* Admin routes */}
            <Route
              path="/admin"
              element={
                <ProtectedRoute requireAdmin={true}>
                  <Navigate to="/admin/dashboard" replace />
                </ProtectedRoute>
              }
            />
            <Route
              path="/admin/dashboard"
              element={
                <ProtectedRoute requireAdmin={true}>
                  <AdminDashboard />
                </ProtectedRoute>
              }
            />
            <Route
              path="/admin/roadmaps"
              element={
                <ProtectedRoute requireAdmin={true}>
                  <AdminRoadmaps />
                </ProtectedRoute>
              }
            />
            <Route
              path="/admin/users"
              element={
                <ProtectedRoute requireAdmin={true}>
                  <UserManagement />
                </ProtectedRoute>
              }
            />
            <Route
              path="/admin/profile"
              element={
                <ProtectedRoute requireAdmin={true}>
                  <Profile />
                </ProtectedRoute>
              }
            />
            <Route
              path="/admin/ai-settings"
              element={
                <ProtectedRoute requireAdmin={true}>
                  <AISettings />
                </ProtectedRoute>
              }
            />
            <Route
              path="/admin/plan-settings"
              element={
                <ProtectedRoute requireAdmin={true}>
                  <PlanSettings />
                </ProtectedRoute>
              }
            />
            {/* Redirect old profile route */}
            <Route
              path="/profile"
              element={
                <ProtectedRoute>
                  {({ isAdmin }) => <Navigate to={isAdmin ? "/admin/profile" : "/user/profile"} replace />}
                </ProtectedRoute>
              }
            />
            <Route path="*" element={<NotFound />} />
          </Routes>
        </BrowserRouter>
      </TooltipProvider>
    </QueryClientProvider>
  );
};

export default App;
