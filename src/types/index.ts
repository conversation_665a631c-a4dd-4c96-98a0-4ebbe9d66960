export interface ProjectPlanning {
  id: string;
  validation_id: string;
  platforms: string[];
  development_phases: string[];
  technology_stack: string[];
  branding_preferences: string[];
  monthly_users: number | null;
  timeline: string | null;
  budget_range: string | null;
  created_at: string;
  updated_at: string;
}

export interface ProjectConcept {
  overview: string;
  features: {
    core: ProjectFeature[];
    mustHave: ProjectFeature[];
    shouldHave: ProjectFeature[];
  };
}

export interface ProjectFeature {
  description: string;
  platforms: string[];
  user_roles: string[];
  acceptance_criteria: string[];
}

export interface RoadmapItem {
  id: string;
  validation_id: string;
  phase: string;
  content: any;
  status: 'draft' | 'pending' | 'in_progress' | 'published';
  created_at: string;
  updated_at: string;
}

export const DEVELOPMENT_PHASES: { [key: string]: { name: string; description: string } } = {
  interactive: {
    name: 'Interactive Prototype',
    description: 'A clickable prototype with core features to validate the concept'
  },
  poc: {
    name: 'Proof of Concept (POC)',
    description: 'A working prototype with basic backend integration to validate technical feasibility'
  },
  mvp: {
    name: 'Minimum Viable Product',
    description: 'A functional product with essential features for early adopters'
  },
  production: {
    name: 'Production Ready',
    description: 'A fully featured product ready for market launch'
  }
};

export interface UserProfile {
  id: string;
  email: string;
  full_name: string;
  avatar_url: string;
  updated_at: string;
}

export interface PricingPlan {
  id: string;
  name: string;
  price: number;
  interval: string;
  features: string[];
  is_active: boolean;
}

export interface UserSubscription {
  id: string;
  user_id: string;
  plan_id: string;
  status: 'active' | 'canceled' | 'expired';
  valid_until: string;
  payment_processor: string;
  payment_id: string;
  created_at: string;
  updated_at: string;
  pack_type: string | null;
}

export interface PaymentDetails {
  cardNumber?: string;
  cardExpiry?: string;
  cardCVC?: string;
  cardName?: string;
  ideaCount?: number;
  packType?: string;
}

// UI component interfaces
export interface Benefit {
  icon: React.FC<any>;
  title: string;
  description: string;
}

export interface Feature {
  title: string;
  description: string; // Changed from desc to description to match usage in FeatureCard
  icon: React.FC<any>;
}

export interface FloatingFeature {
  text: string;
  color: string;
}

export interface ProcessInfo {
  icon: React.FC<any>;
  title: string;
  description: string;
  bgColor: string;
  textColor: string;
}

export interface ProcessWeek {
  week: string;
  title: string;
  items: string[];
  icon: React.FC<any>;
  color: string;
  isLast?: boolean;
}
