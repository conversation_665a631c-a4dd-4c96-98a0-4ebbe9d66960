
// Custom Mermaid entry point that only imports what we need
// We're using dynamic imports to ensure only necessary code is loaded

// This is a lightweight wrapper around Mermaid that only loads the core functionality
// and the specific diagram types we need (sequence and quadrant)
let mermaidInstance = null;
let initialized = false;

// Configuration for Mermaid
const mermaidConfig = {
  startOnLoad: false,
  theme: 'neutral',
  securityLevel: 'loose',
  fontFamily: 'Inter, sans-serif',
  // Only configure the diagram types we actually use
  sequence: {
    useMaxWidth: true,
    diagramMarginX: 50,
    diagramMarginY: 10,
    boxMargin: 10,
    mirrorActors: false, 
    bottomMarginAdj: 1,
    messageMargin: 35,
    boxTextMargin: 5,
    noteMargin: 10,
    messageFont: {
      family: 'Inter, sans-serif',
      size: 14,
      weight: 400
    },
    actorFontFamily: 'Inter, sans-serif',
    actorFontSize: 16,
    actorFontWeight: 400,
    noteFontFamily: 'Inter, sans-serif',
    noteFontSize: 14,
    noteFontWeight: 400,
    noteAlign: 'center',
    wrap: true,
    wrapPadding: 10,
    actorBackground: '#f3f0ff',
    actorBorder: '#d8d2f0',
    actorLineWidth: 1,
    activationBackground: '#f4f4f4',
    activationBorderColor: '#aaa',
    activationBorderWidth: 1
  },
  quadrantChart: {
    useMaxWidth: true,
    chartWidth: 500,
    chartHeight: 500
  },
  // Explicitly disable all other diagram types
  flowchart: { useMaxWidth: false, disable: true },
  gantt: { useMaxWidth: false, disable: true },
  journey: { useMaxWidth: false, disable: true },
  class: { useMaxWidth: false, disable: true },
  state: { useMaxWidth: false, disable: true },
  er: { useMaxWidth: false, disable: true },
  pie: { useMaxWidth: false, disable: true },
  requirement: { useMaxWidth: false, disable: true },
  gitGraph: { useMaxWidth: false, disable: true },
  c4: { useMaxWidth: false, disable: true },
  mindmap: { useMaxWidth: false, disable: true },
  timeline: { useMaxWidth: false, disable: true },
  xychart: { useMaxWidth: false, disable: true },
  sankey: { useMaxWidth: false, disable: true },
  block: { useMaxWidth: false, disable: true },
  themeVariables: {
    // Base colors
    primaryColor: '#f3f0ff', // Light lavender - actor background color
    primaryTextColor: '#1e293b', // Actor text color
    primaryBorderColor: '#d8d2f0', // Actor border
    secondaryColor: '#fffaf3', // Note background color
    tertiaryColor: '#f3f0ff', // Alternative background
    
    // Message colors
    edgeColor: '#333333', // Arrow color (solid)
    lineColor: '#333333', // Line color
    loopLineColor: '#333333',
    
    // Text colors
    textColor: '#334155', // Default text
    actorTextColor: '#334155', // Actor text
    signalTextColor: '#334155', // Message text
    noteBorderColor: '#ffe7bc', // Note borders
    noteTextColor: '#334155' // Note text
  }
};

// Initialize Mermaid with only the diagram types we need
async function initialize() {
  if (initialized) return;
  
  try {
    // Dynamically import only the core Mermaid module
    const { default: mermaid } = await import('mermaid/dist/mermaid.esm.min.mjs');
    mermaidInstance = mermaid;
    
    // Initialize with our configuration
    mermaidInstance.initialize(mermaidConfig);
    initialized = true;
    console.log('Mermaid initialized successfully with optimized configuration');
  } catch (error) {
    console.error('Failed to initialize Mermaid:', error);
    throw error;
  }
}

// Render a diagram
async function render(id, definition) {
  // Ensure Mermaid is initialized
  if (!initialized) {
    await initialize();
  }
  
  try {
    return await mermaidInstance.render(id, definition);
  } catch (error) {
    console.error('Failed to render Mermaid diagram:', error);
    throw error;
  }
}

// Export only the functions we need
export default {
  render,
  initialize
};
