
import { CheckCircle2, Code2, <PERSON>, Users } from "lucide-react";
import { ProcessStep } from "./process/ProcessStep";
import { ProcessInfoItem } from "./process/ProcessInfo";

const weeks = [
  {
    week: "Design",
    title: "Design & Setup",
    items: [
      "Project kickoff meeting",
      "UI/UX design planning",
      "Component architecture",
      "Interactive wireframes"
    ],
    icon: Users,
    color: "bg-gradient-to-br from-purple-500 to-indigo-500"
  },
  {
    week: "Develop",
    title: "Core Development",
    items: [
      "Interactive features",
      "User interface development",
      "Mock data setup",
      "Component integration"
    ],
    icon: Code2,
    color: "bg-gradient-to-br from-blue-500 to-cyan-500"
  },
  {
    week: "Enhance",
    title: "Refinement",
    items: [
      "Feature completion",
      "UI polish and animations",
      "Testing and fixes",
      "Documentation"
    ],
    icon: Rocket,
    color: "bg-gradient-to-br from-green-500 to-emerald-500"
  },
  {
    week: "Deliver",
    title: "Final Handover",
    items: [
      "User testing session",
      "Final adjustments",
      "Source code delivery",
      "Deployment ready"
    ],
    icon: CheckCircle2,
    color: "bg-gradient-to-br from-orange-500 to-yellow-500"
  }
];

const infoItems = [
  {
    icon: Rocket,
    title: "Quick Delivery",
    description: "Quick concept-to-prototype development",
    bgColor: "bg-gradient-to-br from-purple-500 to-indigo-500",
    textColor: "text-white"
  },
  {
    icon: Code2,
    title: "Real Features",
    description: "Interactive features, not just static designs",
    bgColor: "bg-gradient-to-br from-blue-500 to-cyan-500",
    textColor: "text-white"
  },
  {
    icon: Users,
    title: "User Testing",
    description: "Ready for real user feedback and validation",
    bgColor: "bg-gradient-to-br from-green-500 to-emerald-500",
    textColor: "text-white"
  }
];

export const Process = () => {
  return (
    <section className="py-20 relative bg-gradient-to-b from-gray-50 to-gray-100 dark:from-gray-900 dark:to-gray-900/90" id="process">
      <div className="absolute inset-0 bg-grid-pattern opacity-[0.02] dark:opacity-[0.05] pointer-events-none" />
      
      <div className="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center mb-16">
          <h2 className="text-4xl font-bold text-gray-900 dark:text-gray-100 mb-4">
            Our Efficient Prototype Development Workflow
          </h2>
          <p className="text-xl text-gray-600 dark:text-gray-300 max-w-3xl mx-auto">
            A streamlined approach focused on delivering a working prototype that helps validate your idea quickly.
          </p>
        </div>

        <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-8">
          {weeks.map((week, index) => (
            <ProcessStep
              key={index}
              {...week}
              isLast={index === weeks.length - 1}
            />
          ))}
        </div>

        {/* Additional Info */}
        <div className="mt-16 relative">
          <div className="absolute inset-0 bg-gradient-to-br from-indigo-500/5 to-purple-500/5 dark:from-indigo-500/10 dark:to-purple-500/10 rounded-xl" />
          <div className="relative bg-white/10 dark:bg-gray-800/20 backdrop-blur-xl rounded-xl p-8 shadow-xl border border-white/20 dark:border-gray-700/30 hover:border-indigo-200/30 dark:hover:border-indigo-700/40 transition-all duration-300">
            <div className="grid md:grid-cols-3 gap-8">
              {infoItems.map((item, index) => (
                <ProcessInfoItem key={index} {...item} />
              ))}
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};
