
import { useState } from "react";
import { useNavigate } from "react-router-dom";
import { zodResolver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";
import * as z from "zod";
import { toast } from "sonner";
import { supabase } from "@/integrations/supabase/client";
import { Button } from "@/components/ui/button";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { SocialAuthButtons } from "./SocialAuthButtons";
import { useAuthStore } from "@/store/authStore";
import { useUserStore } from "@/store/userStore";
import { useSubscriptionStore } from "@/store/subscriptionStore";

const formSchema = z.object({
  email: z.string().email("Invalid email address"),
  password: z.string().min(6, "Password must be at least 6 characters"),
});

export function LoginForm() {
  const navigate = useNavigate();
  const { checkAuthStatus, logAuthState } = useAuthStore();
  const { fetchProfile } = useUserStore();
  const { fetchCurrentSubscription, fetchFreePlanLimits } = useSubscriptionStore();
  const [isLoading, setIsLoading] = useState(false);
  const [showResendButton, setShowResendButton] = useState(false);

  const form = useForm<z.infer<typeof formSchema>>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      email: "",
      password: "",
    },
  });

  const handleResendConfirmation = async () => {
    try {
      const email = form.getValues("email");
      const { error } = await supabase.auth.resend({
        type: 'signup',
        email: email,
      });
      
      if (error) {
        toast.error(error.message);
        return;
      }
      
      toast.success("Confirmation email resent. Please check your inbox.");
    } catch (error) {
      toast.error("Failed to resend confirmation email");
    }
  };

  const onSubmit = async (values: z.infer<typeof formSchema>) => {
    try {
      setIsLoading(true);
      setShowResendButton(false);

      const { error } = await supabase.auth.signInWithPassword({
        email: values.email,
        password: values.password,
      });

      if (error) {
        if (error.message.includes("Email not confirmed")) {
          toast.error("Please verify your email address before logging in");
          setShowResendButton(true);
          return;
        }
        toast.error(error.message);
        return;
      }

      // Update auth status to get the correct role
      await checkAuthStatus();
      
      // Fetch user profile with Zustand
      await fetchProfile();
      
      // Fetch subscription data
      await fetchCurrentSubscription();
      
      // Fetch free plan limits
      await fetchFreePlanLimits();
      
      // Debug: Log current state after login
      setTimeout(() => {
        logAuthState();
      }, 1000);
      
      toast.success("Logged in successfully");
      
      // Navigation will be handled by Login.tsx based on role
    } catch (error) {
      toast.error("An error occurred while logging in");
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
        <FormField
          control={form.control}
          name="email"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Email</FormLabel>
              <FormControl>
                <Input placeholder="<EMAIL>" {...field} />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
        <FormField
          control={form.control}
          name="password"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Password</FormLabel>
              <FormControl>
                <Input type="password" {...field} />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
        <Button type="submit" className="w-full" disabled={isLoading}>
          {isLoading ? "Signing in..." : "Sign in"}
        </Button>
        {showResendButton && (
          <Button
            type="button"
            variant="outline"
            className="w-full mt-2"
            onClick={handleResendConfirmation}
          >
            Resend confirmation email
          </Button>
        )}
        <SocialAuthButtons show={false} />
        <div className="text-sm text-gray-500 text-center mt-4">
          <span className="opacity-50">Don't have an account?</span>{" "}
          <Button 
            variant="link" 
            className="p-0 opacity-50 cursor-not-allowed" 
            onClick={() => navigate("/signup")}
            disabled={true}
          >
            Sign up
          </Button>
        </div>
      </form>
    </Form>
  );
}
