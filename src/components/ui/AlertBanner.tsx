
import React from 'react'
import { AlertCircle, XCircle, Info, CheckCircle } from 'lucide-react'
import { cva, type VariantProps } from 'class-variance-authority'
import { cn } from '@/lib/utils'

const alertBannerVariants = cva(
  "fixed top-0 left-0 right-0 z-50 flex items-center justify-between p-4 shadow-md transition-transform duration-300 ease-in-out",
  {
    variants: {
      variant: {
        error: "bg-destructive text-destructive-foreground",
        warning: "bg-amber-500 text-white",
        info: "bg-blue-500 text-white",
        success: "bg-green-500 text-white",
      },
    },
    defaultVariants: {
      variant: "error",
    },
  }
)

export interface AlertBannerProps extends React.HTMLAttributes<HTMLDivElement>, VariantProps<typeof alertBannerVariants> {
  message: string;
  onClose?: () => void;
  show: boolean;
}

const alertIcons = {
  error: XCircle,
  warning: AlertCircle,
  info: Info,
  success: CheckCircle,
}

export function AlertBanner({ message, variant, onClose, show, className, ...props }: AlertBannerProps) {
  if (!show) return null;
  
  const IconComponent = alertIcons[variant || 'error'];
  
  return (
    <div 
      className={cn(
        alertBannerVariants({ variant }),
        show ? 'translate-y-0' : '-translate-y-full',
        className
      )}
      {...props}
    >
      <div className="flex items-center space-x-3 flex-1">
        <IconComponent className="h-5 w-5" />
        <p className="font-medium">{message}</p>
      </div>
      {onClose && (
        <button 
          onClick={onClose} 
          className="p-1 rounded-full hover:bg-black/10 transition-colors"
          aria-label="Close alert"
        >
          <XCircle className="h-5 w-5" />
        </button>
      )}
    </div>
  )
}
