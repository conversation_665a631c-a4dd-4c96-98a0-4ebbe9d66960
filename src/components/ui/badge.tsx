
import { cva, type VariantProps } from "class-variance-authority";
import * as React from "react";

import { cn } from "@/lib/utils";

const badgeVariants = cva(
  "inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",
  {
    variants: {
      variant: {
        default:
          "border-transparent bg-primary text-primary-foreground hover:bg-primary/80",
        secondary:
          "border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80",
        destructive:
          "border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80",
        outline: "text-foreground",
        success:
          "border-transparent bg-emerald-500/10 text-emerald-500 dark:bg-emerald-500/20 hover:bg-emerald-500/20 dark:hover:bg-emerald-500/30",
        info: 
          "border-transparent bg-blue-500/10 text-blue-600 dark:bg-blue-500/20 hover:bg-blue-500/20 dark:hover:bg-blue-500/30",
        warning:
          "border-transparent bg-amber-500/10 text-amber-600 dark:bg-amber-500/20 hover:bg-amber-500/20 dark:hover:bg-amber-500/30",
        feature:
          "border-transparent bg-violet-500/10 text-violet-600 dark:bg-violet-500/20 hover:bg-violet-500/20 dark:hover:bg-violet-500/30",
        premium:
          "border-transparent bg-gradient-to-r from-amber-400 to-amber-600 text-white",
      },
    },
    defaultVariants: {
      variant: "default",
    },
  }
);

export interface BadgeProps
  extends React.HTMLAttributes<HTMLDivElement>,
    VariantProps<typeof badgeVariants> {}

function Badge({ className, variant, ...props }: BadgeProps) {
  return (
    <div className={cn(badgeVariants({ variant }), className)} {...props} />
  );
}

export { Badge, badgeVariants };
