
import { useNavigate } from "react-router-dom";
import { Sidebar, SidebarBody, SidebarLink } from "@/components/ui/sidebar";
import { LayoutDashboard, UserCog, LogOut, Settings, User as UserIcon, CreditCard, FileText } from "lucide-react";
import { supabase } from "@/integrations/supabase/client";
import { cn } from "@/lib/utils";
import { toast } from "sonner";

interface AdminSidebarProps {
  open: boolean;
  setOpen: (open: boolean) => void;
}

export function AdminSidebar({ open, setOpen }: AdminSidebarProps) {
  const navigate = useNavigate();

  const handleLogout = async () => {
    try {
      const { error } = await supabase.auth.signOut();
      if (error) throw error;
      
      toast.success("Logged out successfully");
      navigate('/login');
    } catch (error) {
      console.error('Error logging out:', error);
      toast.error("Failed to log out");
    }
  };

  const links = [
    {
      label: "Dashboard",
      href: "/admin/dashboard",
      icon: <LayoutDashboard className="text-blue-500 h-5 w-5" />,
    },
    {
      label: "User Management",
      href: "/admin/users",
      icon: <UserCog className="text-amber-500 h-5 w-5" />,
    },
    {
      label: "Roadmaps",
      href: "/admin/roadmaps",
      icon: <FileText className="text-violet-500 h-5 w-5" />,
    },
    {
      label: "Pricing Plans",
      href: "/admin/plan-settings",
      icon: <CreditCard className="text-emerald-500 h-5 w-5" />,
    },
    {
      label: "AI Settings",
      href: "/admin/ai-settings",
      icon: <Settings className="text-sky-500 h-5 w-5" />,
    },
    {
      label: "Profile",
      href: "/admin/profile",
      icon: <UserIcon className="text-rose-500 h-5 w-5" />,
    },
  ];

  return (
    <Sidebar className="border-r border-primary/10 bg-gradient-to-b from-white to-slate-50 dark:from-gray-900 dark:to-gray-950">
      <SidebarBody className="justify-between gap-10">
        <div className="flex flex-col flex-1 overflow-y-auto overflow-x-hidden">
          <div className="flex items-center justify-center px-4 py-4 border-b border-primary/10">
            <div className={cn("flex justify-center h-8", open ? "w-32" : "w-full")}>
              <img 
                src="/Incepta.png" 
                alt="Incepta Logo" 
                className="h-8 object-contain"
              />
            </div>
          </div>
          <div className="mt-8 flex flex-col gap-4 px-2">
            {links.map((link, idx) => (
              <SidebarLink key={idx} link={link} className="hover-lift rounded-lg" />
            ))}
            <button
              onClick={handleLogout}
              className={cn(
                "flex items-center gap-2 px-3 py-2 mt-2 text-sm text-destructive hover:bg-destructive/10 rounded-lg transition-colors hover-lift",
                !open && "justify-center"
              )}
            >
              <LogOut className="h-5 w-5 text-red-500" />
              <span className={cn(
                "transition-all duration-200",
                open ? "opacity-100" : "opacity-0 w-0 overflow-hidden"
              )}>
                Logout
              </span>
            </button>
          </div>
        </div>
      </SidebarBody>
    </Sidebar>
  );
}

export default AdminSidebar;
