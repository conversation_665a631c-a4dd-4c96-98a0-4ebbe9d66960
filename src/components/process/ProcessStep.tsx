
import { ProcessWeek } from "@/types";

export const ProcessStep = ({ week, title, items, icon: Icon, color, isLast }: ProcessWeek) => {
  return (
    <div className="relative">
      {!isLast && (
        <div className="hidden md:block absolute top-8 left-full w-full h-0.5 bg-gray-200 dark:bg-gray-700 transform translate-x-4" />
      )}
      <div className="relative bg-white dark:bg-gray-800 rounded-xl p-6 shadow-sm hover:shadow-md transition-shadow duration-300">
        <div className={`w-12 h-12 ${color} rounded-lg flex items-center justify-center mb-4`}>
          <Icon className="h-6 w-6 text-white" />
        </div>
        <div className="mb-4">
          <span className="text-sm font-medium text-gray-500 dark:text-gray-400">Week {week}</span>
          <h3 className="text-xl font-bold text-gray-900 dark:text-gray-100 mt-1">{title}</h3>
        </div>
        <ul className="space-y-3">
          {items.map((item, index) => (
            <li key={index} className="flex items-center text-gray-600 dark:text-gray-300">
              <span className={`w-1.5 h-1.5 ${color} rounded-full mr-2`}></span>
              {item}
            </li>
          ))}
        </ul>
      </div>
    </div>
  );
};
