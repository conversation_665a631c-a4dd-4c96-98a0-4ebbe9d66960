
import { ProcessInfo } from "@/types";

export const ProcessInfoItem = ({ icon: Icon, title, description, bgColor, textColor }: ProcessInfo) => {
  return (
    <div className="flex items-start space-x-4">
      <div className={`p-3 rounded-lg ${bgColor}`}>
        <Icon className={`h-6 w-6 ${textColor}`} />
      </div>
      <div>
        <h4 className="font-semibold text-gray-900 dark:text-gray-100">{title}</h4>
        <p className="mt-1 text-gray-600 dark:text-gray-300">{description}</p>
      </div>
    </div>
  );
};
