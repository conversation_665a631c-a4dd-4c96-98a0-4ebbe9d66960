
import { LucideIcon } from "lucide-react";

interface FeatureCardProps {
  icon: LucideIcon;
  title: string;
  description: string;
  iconClassName?: string;
}

const FeatureCard = ({ icon: Icon, title, description, iconClassName }: FeatureCardProps) => {
  return (
    <div className="flex flex-col items-start">
      <div className={`rounded-lg p-2 ${iconClassName || 'bg-primary/10'} transition-colors duration-200 hover:scale-105`}>
        <Icon className={`h-5 w-5 ${iconClassName ? 'text-white' : 'text-primary'}`} />
      </div>
      <h3 className="font-semibold text-gray-900 dark:text-white mt-3">{title}</h3>
      <p className="mt-2 text-sm text-gray-500 dark:text-gray-400">{description}</p>
    </div>
  );
};

export default FeatureCard;
