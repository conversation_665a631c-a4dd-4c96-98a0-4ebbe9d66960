
import React from "react";

interface FloatingFeatureProps {
  label: string;
  color: string;
}

const FloatingFeature = ({ label, color }: FloatingFeatureProps) => {
  return (
    <div className="bg-white/90 dark:bg-gray-800/90 px-4 py-2 rounded-lg shadow-lg backdrop-blur-sm">
      <p className="text-sm font-medium flex items-center gap-2">
        <span className={`w-2 h-2 ${color} rounded-full`}></span>
        {label}
      </p>
    </div>
  );
};

export default FloatingFeature;
