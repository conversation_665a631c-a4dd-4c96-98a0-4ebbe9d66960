
import { Lightbulb, Target, Badge, Users } from "lucide-react";

export const About = () => {
  return (
    <section className="py-20 bg-white dark:bg-gray-900" id="about">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center mb-16">
          <h2 className="text-3xl font-bold text-gray-900 dark:text-gray-100 mb-4">
            About Incepta
          </h2>
          <p className="text-xl text-gray-600 dark:text-gray-400 max-w-3xl mx-auto">
            We transform ideas into validated, production-ready prototypes that help enterprises 
            make informed technology decisions.
          </p>
        </div>
        
        <div className="grid md:grid-cols-2 gap-12 items-center">
          <div>
            <h3 className="text-2xl font-bold text-gray-900 dark:text-gray-100 mb-6">
              Our Vision
            </h3>
            <p className="text-gray-600 dark:text-gray-300 mb-8">
              At Incepta, we believe that every great software product starts with proper validation.
              Our mission is to help businesses build the right solution by providing rapid interactive 
              prototyping services that bridge the gap between concept and production.
            </p>
            
            <div className="space-y-4">
              <div className="flex items-start">
                <div className="mr-4 mt-1 bg-primary/10 p-2 rounded-lg">
                  <Lightbulb className="h-5 w-5 text-primary" />
                </div>
                <div>
                  <h4 className="font-medium text-gray-900 dark:text-gray-100">Rapid Innovation</h4>
                  <p className="text-gray-600 dark:text-gray-300">
                    We turn concepts into interactive prototypes in days, not months
                  </p>
                </div>
              </div>
              
              <div className="flex items-start">
                <div className="mr-4 mt-1 bg-primary/10 p-2 rounded-lg">
                  <Target className="h-5 w-5 text-primary" />
                </div>
                <div>
                  <h4 className="font-medium text-gray-900 dark:text-gray-100">Market Validation</h4>
                  <p className="text-gray-600 dark:text-gray-300">
                    Test with real users before committing to full development
                  </p>
                </div>
              </div>
              
              <div className="flex items-start">
                <div className="mr-4 mt-1 bg-primary/10 p-2 rounded-lg">
                  <Badge className="h-5 w-5 text-primary" />
                </div>
                <div>
                  <h4 className="font-medium text-gray-900 dark:text-gray-100">Enterprise Focus</h4>
                  <p className="text-gray-600 dark:text-gray-300">
                    Specialized in complex enterprise solutions and AI-powered applications
                  </p>
                </div>
              </div>
            </div>
          </div>
          
          <div className="relative">
            <div className="absolute inset-0 bg-gradient-to-tr from-primary/20 to-purple-500/20 rounded-2xl transform rotate-3" />
            <div className="relative bg-white dark:bg-gray-800 p-8 rounded-2xl shadow-lg border border-gray-100 dark:border-gray-700 -rotate-3">
              <h3 className="text-2xl font-bold text-gray-900 dark:text-gray-100 mb-6">
                Our Approach
              </h3>
              <div className="space-y-6">
                <div className="flex items-center gap-4">
                  <div className="w-12 h-12 flex items-center justify-center rounded-full bg-green-100 dark:bg-green-900">
                    <span className="text-xl font-bold text-green-600 dark:text-green-400">1</span>
                  </div>
                  <p className="text-gray-600 dark:text-gray-300">
                    <span className="font-medium">Define</span> - We capture your vision and requirements
                  </p>
                </div>
                
                <div className="flex items-center gap-4">
                  <div className="w-12 h-12 flex items-center justify-center rounded-full bg-blue-100 dark:bg-blue-900">
                    <span className="text-xl font-bold text-blue-600 dark:text-blue-400">2</span>
                  </div>
                  <p className="text-gray-600 dark:text-gray-300">
                    <span className="font-medium">Design</span> - We create interactive wireframes and prototypes
                  </p>
                </div>
                
                <div className="flex items-center gap-4">
                  <div className="w-12 h-12 flex items-center justify-center rounded-full bg-indigo-100 dark:bg-indigo-900">
                    <span className="text-xl font-bold text-indigo-600 dark:text-indigo-400">3</span>
                  </div>
                  <p className="text-gray-600 dark:text-gray-300">
                    <span className="font-medium">Develop</span> - We build functional interactive prototypes
                  </p>
                </div>
                
                <div className="flex items-center gap-4">
                  <div className="w-12 h-12 flex items-center justify-center rounded-full bg-purple-100 dark:bg-purple-900">
                    <span className="text-xl font-bold text-purple-600 dark:text-purple-400">4</span>
                  </div>
                  <p className="text-gray-600 dark:text-gray-300">
                    <span className="font-medium">Deliver</span> - We provide a production-ready foundation
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>
        
        <div className="mt-16 text-center">
          <div className="inline-flex items-center bg-gradient-to-r from-primary/10 to-purple-500/10 px-6 py-3 rounded-lg">
            <Users className="h-6 w-6 text-primary mr-3" />
            <span className="text-lg font-medium text-gray-900 dark:text-gray-100">
              Join over 100+ enterprises who've validated their software ideas with Incepta
            </span>
          </div>
        </div>
      </div>
    </section>
  );
};

export default About;
