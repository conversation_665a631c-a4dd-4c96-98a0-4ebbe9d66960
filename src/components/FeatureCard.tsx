
import React from 'react';
import { Feature } from '@/types';

interface FeatureCardProps {
  feature: Feature;
}

export default function FeatureCard({ feature }: FeatureCardProps) {
  const Icon = feature.icon;

  return (
    <div className="border border-gray-200 dark:border-gray-800 rounded-lg p-6 hover:shadow-md transition-shadow duration-300">
      <div className="relative">
        <Icon className="h-5 w-5 text-indigo-600 dark:text-indigo-400 mb-3" />
        <h3 className="font-semibold text-gray-900 dark:text-gray-100">{feature.title}</h3>
        <p className="mt-2 text-gray-600 dark:text-gray-300 text-sm">{feature.description}</p>
      </div>
    </div>
  );
}
