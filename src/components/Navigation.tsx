
import React, { useState, useEffect } from 'react';
import { Menu, X } from 'lucide-react';
import { useNavigate, useLocation } from 'react-router-dom';
import { toast } from "sonner";
import DesktopMenu from './navbar/DesktopMenu';
import MobileMenu from './navbar/MobileMenu';
import { User } from '@/types/auth';
import { useAuthStore } from '@/store/authStore';

export const Navigation = () => {
  const [isOpen, setIsOpen] = useState(false);
  const [user, setUser] = useState<User | null>(null);
  const navigate = useNavigate();
  const location = useLocation();
  const { isAuthenticated, isAdmin, userId, signOut, isLoading } = useAuthStore();

  const isHomePage = location.pathname === '/';
  const isAdminPage = location.pathname.startsWith('/admin');

  useEffect(() => {
    if (userId) {
      setUser({
        id: userId,
        email: undefined // We don't have the email in the store currently
      });
    } else {
      setUser(null);
    }
  }, [userId]);

  useEffect(() => {
    setIsOpen(false);
  }, [location]);

  const handleStartProject = () => {
    if (isAuthenticated) {
      navigate('/validate-idea');
    } else {
      navigate('/login');
    }
  };

  const handleLogout = async () => {
    try {
      await signOut();
      toast.success("Logged out successfully");
      navigate('/');
    } catch (error) {
      console.error('Error logging out:', error);
      toast.error("Failed to log out");
    }
  };

  const scrollToSection = (sectionId: string) => {
    const element = document.getElementById(sectionId);
    if (element) {
      const offset = 80;
      const elementPosition = element.getBoundingClientRect().top;
      const offsetPosition = elementPosition + window.pageYOffset - offset;

      window.scrollTo({
        top: offsetPosition,
        behavior: 'smooth'
      });
      setIsOpen(false);
    }
  };

  if (isLoading) {
    return null;
  }

  return (
    <nav className="bg-white/80 backdrop-blur-lg dark:bg-gray-900/80 shadow-sm dark:shadow-gray-800/10 sticky top-0 z-50 border-b border-gray-200 dark:border-gray-800">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex justify-between items-center h-16">
          <div className="flex items-center">
            <a href="/" className="flex items-center group">
              <img 
                src="/logo-site.png" 
                alt="Incepta Logo" 
                className="h-8 w-auto transition-transform duration-200 group-hover:scale-105" 
              />
            </a>
          </div>
          
          <DesktopMenu
            isHomePage={isHomePage}
            isAdminPage={isAdminPage}
            isAuthenticated={isAuthenticated}
            isAdmin={isAdmin}
            user={user}
            scrollToSection={scrollToSection}
            handleStartProject={handleStartProject}
            handleLogout={handleLogout}
          />

          <div className="md:hidden flex items-center">
            <button
              onClick={() => setIsOpen(!isOpen)}
              className="text-gray-600 hover:text-gray-900 dark:text-gray-300 dark:hover:text-white p-2 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-800 transition-colors"
            >
              {isOpen ? <X className="h-5 w-5" /> : <Menu className="h-5 w-5" />}
            </button>
          </div>
        </div>
      </div>

      <MobileMenu
        isOpen={isOpen}
        isHomePage={isHomePage}
        isAdminPage={isAdminPage}
        isAuthenticated={isAuthenticated}
        isAdmin={isAdmin}
        user={user}
        scrollToSection={scrollToSection}
        handleStartProject={handleStartProject}
        handleLogout={handleLogout}
      />
    </nav>
  );
};

export default Navigation;
