import React, { useState, useEffect } from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { <PERSON><PERSON>, <PERSON><PERSON>Content, <PERSON><PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { Badge } from '@/components/ui/badge';
import { 
  Code, 
  Eye, 
  Download, 
  ExternalLink,
  FileText,
  Folder,
  Play
} from 'lucide-react';
import { supabase } from '@/lib/supabase';

interface PrototypeFile {
  id: string;
  file_path: string;
  file_content: string;
  file_type: string;
  template_used: string;
  created_at: string;
}

interface PrototypePreviewProps {
  prototypeId: string;
  status: string;
}

export default function PrototypePreview({ prototypeId, status }: PrototypePreviewProps) {
  const [files, setFiles] = useState<PrototypeFile[]>([]);
  const [selectedFile, setSelectedFile] = useState<PrototypeFile | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [previewMode, setPreviewMode] = useState<'code' | 'preview'>('code');

  useEffect(() => {
    if (prototypeId) {
      fetchFiles();
      setupFileSubscription();
    }
  }, [prototypeId]);

  const fetchFiles = async () => {
    try {
      const { data, error } = await supabase
        .from('prototype_files')
        .select('*')
        .eq('prototype_id', prototypeId)
        .order('created_at', { ascending: true });

      if (error) throw error;
      
      setFiles(data || []);
      if (data && data.length > 0 && !selectedFile) {
        setSelectedFile(data[0]);
      }
    } catch (error) {
      console.error('Error fetching files:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const setupFileSubscription = () => {
    const channel = supabase
      .channel('prototype-files')
      .on(
        'postgres_changes',
        {
          event: 'INSERT',
          schema: 'public',
          table: 'prototype_files',
          filter: `prototype_id=eq.${prototypeId}`
        },
        (payload) => {
          console.log('New file generated:', payload.new);
          setFiles(prev => [...prev, payload.new as PrototypeFile]);
        }
      )
      .subscribe();

    return () => {
      supabase.removeChannel(channel);
    };
  };

  const getFileIcon = (fileType: string) => {
    switch (fileType) {
      case 'typescript':
      case 'javascript':
        return <Code className="w-4 h-4 text-blue-500" />;
      case 'css':
        return <FileText className="w-4 h-4 text-green-500" />;
      case 'json':
        return <FileText className="w-4 h-4 text-yellow-500" />;
      default:
        return <FileText className="w-4 h-4 text-gray-500" />;
    }
  };

  const organizeFilesByFolder = (files: PrototypeFile[]) => {
    const folders: { [key: string]: PrototypeFile[] } = {};
    
    files.forEach(file => {
      const pathParts = file.file_path.split('/');
      const folder = pathParts.length > 1 ? pathParts[0] : 'root';
      
      if (!folders[folder]) {
        folders[folder] = [];
      }
      folders[folder].push(file);
    });
    
    return folders;
  };

  const generateLivePreview = () => {
    // Create a simple HTML preview of the React app
    const mainComponent = files.find(f => f.file_path.includes('App.tsx') || f.file_path.includes('main'));
    
    if (!mainComponent) {
      return (
        <div className="flex items-center justify-center h-64 bg-gray-50 rounded-lg">
          <div className="text-center">
            <Play className="w-12 h-12 text-gray-400 mx-auto mb-2" />
            <p className="text-gray-600">No preview available yet</p>
            <p className="text-sm text-gray-500">Generate more components to see a preview</p>
          </div>
        </div>
      );
    }

    return (
      <div className="bg-white border rounded-lg p-4 h-96 overflow-auto">
        <div className="bg-gradient-to-r from-blue-500 to-purple-600 text-white p-6 rounded-lg mb-4">
          <h1 className="text-2xl font-bold">AI Legal Assistant</h1>
          <p className="text-blue-100">Automated contract review and risk analysis</p>
        </div>
        
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          {files.filter(f => f.file_type === 'typescript' && f.file_path.includes('components')).map((file, index) => (
            <div key={file.id} className="bg-gray-50 p-4 rounded-lg border">
              <h3 className="font-semibold text-sm mb-2">
                {file.file_path.split('/').pop()?.replace('.tsx', '')}
              </h3>
              <p className="text-xs text-gray-600">Component {index + 1}</p>
            </div>
          ))}
        </div>
        
        <div className="mt-4 p-4 bg-blue-50 rounded-lg">
          <p className="text-sm text-blue-800">
            ✨ Live preview of your generated React application
          </p>
        </div>
      </div>
    );
  };

  if (isLoading) {
    return (
      <Card>
        <CardContent className="p-6">
          <div className="animate-pulse">
            <div className="h-4 bg-gray-200 rounded w-1/4 mb-4"></div>
            <div className="h-32 bg-gray-200 rounded"></div>
          </div>
        </CardContent>
      </Card>
    );
  }

  const filesByFolder = organizeFilesByFolder(files);

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <CardTitle className="flex items-center gap-2">
              <Folder className="w-5 h-5" />
              Generated Files ({files.length})
            </CardTitle>
            <div className="flex gap-2">
              <Badge variant={status === 'completed' ? 'default' : 'secondary'}>
                {status}
              </Badge>
              {status === 'completed' && (
                <Button size="sm" variant="outline">
                  <Download className="w-4 h-4 mr-2" />
                  Download
                </Button>
              )}
            </div>
          </div>
        </CardHeader>
        <CardContent>
          <Tabs value={previewMode} onValueChange={(value) => setPreviewMode(value as 'code' | 'preview')}>
            <TabsList className="grid w-full grid-cols-2">
              <TabsTrigger value="code" className="flex items-center gap-2">
                <Code className="w-4 h-4" />
                Code View
              </TabsTrigger>
              <TabsTrigger value="preview" className="flex items-center gap-2">
                <Eye className="w-4 h-4" />
                Live Preview
              </TabsTrigger>
            </TabsList>
            
            <TabsContent value="code" className="mt-4">
              <div className="grid grid-cols-1 lg:grid-cols-3 gap-4">
                {/* File Explorer */}
                <div className="lg:col-span-1">
                  <div className="border rounded-lg p-4 h-96 overflow-auto">
                    <h3 className="font-semibold mb-3">Project Structure</h3>
                    {Object.entries(filesByFolder).map(([folder, folderFiles]) => (
                      <div key={folder} className="mb-3">
                        <div className="flex items-center gap-2 mb-2">
                          <Folder className="w-4 h-4 text-blue-500" />
                          <span className="font-medium text-sm">{folder}</span>
                        </div>
                        <div className="ml-6 space-y-1">
                          {folderFiles.map(file => (
                            <button
                              key={file.id}
                              onClick={() => setSelectedFile(file)}
                              className={`flex items-center gap-2 w-full text-left p-2 rounded text-sm hover:bg-gray-100 ${
                                selectedFile?.id === file.id ? 'bg-blue-50 text-blue-700' : ''
                              }`}
                            >
                              {getFileIcon(file.file_type)}
                              {file.file_path.split('/').pop()}
                            </button>
                          ))}
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
                
                {/* Code Editor */}
                <div className="lg:col-span-2">
                  {selectedFile ? (
                    <div className="border rounded-lg">
                      <div className="bg-gray-50 px-4 py-2 border-b flex items-center justify-between">
                        <span className="font-medium text-sm">{selectedFile.file_path}</span>
                        <Badge variant="outline">{selectedFile.file_type}</Badge>
                      </div>
                      <pre className="p-4 text-sm overflow-auto h-80 bg-gray-900 text-green-400">
                        <code>{selectedFile.file_content}</code>
                      </pre>
                    </div>
                  ) : (
                    <div className="border rounded-lg h-96 flex items-center justify-center text-gray-500">
                      Select a file to view its content
                    </div>
                  )}
                </div>
              </div>
            </TabsContent>
            
            <TabsContent value="preview" className="mt-4">
              {generateLivePreview()}
            </TabsContent>
          </Tabs>
        </CardContent>
      </Card>
    </div>
  );
}
