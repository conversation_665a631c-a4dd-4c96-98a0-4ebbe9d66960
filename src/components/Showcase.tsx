
import { ArrowRight } from "lucide-react";
import { useNavigate } from "react-router-dom";
import { ShowcaseItem } from "./showcase/ShowcaseItem";
import type { ShowcaseItemProps } from "./showcase/types";

const showcaseItems: ShowcaseItemProps[] = [
  {
    title: "AI Automation Framework",
    description: "Comprehensive framework for building and deploying AI-powered agents, chatbots, and automated workflows.",
    stats: {
      users: "10,000+",
      timeframe: "2 weeks",
      improvement: "80% faster"
    },
    tags: ["AI/ML", "Automation", "Enterprise"],
    features: [
      "Custom AI agents",
      "Workflow automation",
      "Natural language processing",
      "Integration hub"
    ],
    link: "https://incepta.ai/"
  },
  {
    title: "SAP Implementation Portal",
    description: "Enterprise platform for managing SAP implementations, customizations, and support services.",
    stats: {
      users: "5,000+",
      timeframe: "2 weeks",
      improvement: "65% clearer"
    },
    tags: ["SAP", "Enterprise", "ERP"],
    features: [
      "Implementation tracking",
      "Customization management",
      "Support ticketing",
      "Resource planning"
    ],
    link: "https://ratiocination.co.uk/"
  },
  {
    title: "Interactive Resume Platform",
    description: "Dynamic portfolio and resume showcase with interactive project demonstrations and skill visualization.",
    stats: {
      users: "2,000+",
      timeframe: "1 weeks",
      improvement: "90% engaged"
    },
    tags: ["Portfolio", "Interactive", "React"],
    features: [
      "Project showcase",
      "Skill visualization",
      "Interactive demos",
      "Contact system"
    ],
    link: "https://suren-profile.incepta.ai/"
  }
];

export const Showcase = () => {
  const navigate = useNavigate();

  const handleStartProject = () => {
    navigate("/login");
  };

  return (
    <section className="py-20 bg-gray-50 dark:bg-gray-900" id="showcase">
      <div className="max-w-7xl mx-auto px-4">
        <div className="text-center mb-12">
          <h2 className="text-3xl font-bold text-gray-900 dark:text-gray-100 mb-4">
            Interactive Prototype Showcase
          </h2>
          <p className="text-gray-600 dark:text-gray-300 max-w-2xl mx-auto">
            Real examples of working prototypes we delivered rapidly, helping enterprises validate their ideas efficiently.
          </p>
        </div>

        <div className="grid md:grid-cols-3 gap-8">
          {showcaseItems.map((item, index) => (
            <ShowcaseItem key={index} item={item} />
          ))}
        </div>

        <div className="mt-16 text-center">
          <h3 className="text-2xl font-bold text-gray-900 dark:text-gray-100 mb-4">
            Ready to Build Your Prototype?
          </h3>
          <p className="text-gray-600 dark:text-gray-300 mb-8 max-w-2xl mx-auto">
            Experience quick delivery of fully interactive prototypes. Perfect for validating your enterprise solutions and AI-powered applications.
          </p>
          <button 
            onClick={handleStartProject}
            className="inline-flex items-center px-6 py-3 bg-indigo-600 dark:bg-indigo-500 text-white rounded-lg hover:bg-indigo-700 dark:hover:bg-indigo-600 transition-colors"
          >
            What's your Idea?
            <ArrowRight className="ml-2 h-5 w-5" />
          </button>
        </div>
      </div>
    </section>
  );
};
