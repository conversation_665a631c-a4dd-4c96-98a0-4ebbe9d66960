import React from "react";

const Footer = () => {
  return (
    <footer className="bg-white dark:bg-gray-900">
      <div className="max-w-7xl mx-auto py-12 px-4 sm:px-6 lg:px-8">
        <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
          <div className="col-span-1">
            <div className="flex items-center">
              <a href="/" className="flex items-center">
                <img src="/logo-site.png" alt="Incepta Logo" className="h-8 w-auto mb-4" />
              </a>
            </div>
            <p className="mt-4 text-gray-600 dark:text-gray-400">
              Transforming software ideas into reality through intelligent planning and seamless execution.
            </p>
          </div>
          
          <div>
            <h3 className="text-sm font-semibold text-gray-400 dark:text-gray-500 tracking-wider uppercase">Product</h3>
            <ul className="mt-4 space-y-4">
              <li><a href="#features" className="text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-gray-200">Features</a></li>
              <li><a href="#pricing" className="text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-gray-200">Pricing</a></li>
              <li><a href="#testimonials" className="text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-gray-200">Testimonials</a></li>
            </ul>
          </div>

          <div>
            <h3 className="text-sm font-semibold text-gray-400 dark:text-gray-500 tracking-wider uppercase">Company</h3>
            <ul className="mt-4 space-y-4">
              <li><a href="#about" className="text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-gray-200">About</a></li>
              <li><a href="#blog" className="text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-gray-200">Blog</a></li>
              <li><a href="#careers" className="text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-gray-200">Careers</a></li>
            </ul>
          </div>

          <div>
            <h3 className="text-sm font-semibold text-gray-400 dark:text-gray-500 tracking-wider uppercase">Legal</h3>
            <ul className="mt-4 space-y-4">
              <li><a href="#privacy" className="text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-gray-200">Privacy</a></li>
              <li><a href="#terms" className="text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-gray-200">Terms</a></li>
              <li><a href="#contact" className="text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-gray-200">Contact</a></li>
            </ul>
          </div>
        </div>
        <div className="mt-8 border-t border-gray-200 dark:border-gray-800 pt-8">
          <p className="text-center text-gray-400 dark:text-gray-600">&copy; {new Date().getFullYear()} Incepta. All rights reserved.</p>
        </div>
      </div>
    </footer>
  );
};

export default Footer;
