
import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON>, Calculator, <PERSON>, Target } from "lucide-react";
import { <PERSON> } from "react-router-dom";
import FeatureCard from "./hero/FeatureCard";

export const HeroSection = () => {
  return (
    <div className="relative overflow-hidden bg-background pt-14">
      <div className="py-24 sm:py-32">
        <div className="mx-auto max-w-7xl px-6 lg:px-8">
          <div className="flex flex-col lg:flex-row items-start justify-between gap-12">
            {/* Left Column */}
            <div className="flex-1">
              {/* Badge */}
              <div className="inline-flex items-center rounded-full bg-gradient-to-r from-purple-500/20 to-indigo-500/20 px-4 py-1 mb-8">
                <Brain className="h-4 w-4 text-purple-600 dark:text-purple-400 mr-2" />
                <span className="text-sm font-medium bg-clip-text text-transparent bg-gradient-to-r from-purple-600 to-indigo-600">AI-Powered Project Planning</span>
              </div>

              {/* Heading */}
              <h1 className="text-4xl sm:text-6xl font-bold tracking-tight text-gray-900 dark:text-white">
                Evaluating{" "}
                <span className="bg-clip-text text-transparent bg-gradient-to-r from-primary to-primary-light">
                  Ideas made easy
                </span>
              </h1>

              {/* Description */}
              <p className="mt-6 text-lg leading-8 text-gray-600 dark:text-gray-300">
                Get comprehensive project planning powered by AI. From initial concept to detailed roadmap, validate your software idea with market insights, cost estimates, and interactive prototypes.
              </p>

              {/* CTA Button */}
              <div className="mt-10">
                <Link to="/validate-idea">
                  <Button size="lg" className="gap-2 text-base text-white shadow-lg bg-gradient-to-r from-primary to-primary-light hover:from-primary-600 hover:to-primary-light">
                    Validate Your Idea
                    <Rocket className="w-4 h-4" />
                  </Button>
                </Link>
                <p className="mt-4 text-sm text-gray-500 dark:text-gray-400">
                  AI-powered analysis • Market validation • Cost estimation
                </p>
              </div>

              {/* Feature List */}
              <div className="mt-16 grid grid-cols-1 sm:grid-cols-3 gap-8">
                <FeatureCard
                  icon={Brain}
                  title="AI Analysis"
                  description="Smart project planning with AI insights"
                  iconClassName="bg-gradient-to-br from-purple-500 to-indigo-500"
                />
                <FeatureCard
                  icon={Target}
                  title="Market Validation"
                  description="Data-driven market fit analysis"
                  iconClassName="bg-gradient-to-br from-green-500 to-emerald-600"
                />
                <FeatureCard
                  icon={Calculator}
                  title="Cost Estimation"
                  description="Accurate development forecasts"
                  iconClassName="bg-gradient-to-br from-blue-500 to-cyan-600"
                />
              </div>
            </div>

            {/* Right Column - Hero Image with Animations */}
            <div className="flex-1 relative group">
              {/* Background gradient with rotation */}
              <div 
                className="absolute inset-0 bg-gradient-to-tr from-primary/30 to-transparent rotate-3 rounded-2xl 
                transform transition-transform duration-500"
              />
              
              {/* Image container with counter-rotation */}
              <div className="relative w-full aspect-square lg:aspect-[4/3] -rotate-3 transform 
                transition-all duration-500 hover:rotate-0 hover:scale-[1.01]">
                <img
                  src="/landing-page.avif"
                  alt="AI-Powered Project Planning"
                  className="object-cover rounded-2xl shadow-2xl ring-1 ring-gray-900/10"
                  style={{
                    width: '100%',
                    height: '100%',
                  }}
                />

                {/* Floating Feature Labels */}
                <div className="absolute top-4 -left-4 bg-white/90 dark:bg-gray-800/90 px-4 py-2 rounded-lg shadow-lg backdrop-blur-sm
                  transform transition-all duration-500 group-hover:translate-x-2">
                  <p className="text-sm font-medium flex items-center gap-2">
                    <span className="w-2 h-2 bg-green-500 rounded-full"></span>
                    Interactive Features
                  </p>
                </div>

                <div className="absolute bottom-4 -right-4 bg-white/90 dark:bg-gray-800/90 px-4 py-2 rounded-lg shadow-lg backdrop-blur-sm
                  transform transition-all duration-500 group-hover:-translate-x-2">
                  <p className="text-sm font-medium flex items-center gap-2">
                    <span className="w-2 h-2 bg-blue-500 rounded-full"></span>
                    Real User Testing
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default HeroSection;
