
import { useState, useEffect, useCallback } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { useRoadmapStore } from "@/store/roadmapStore";
import { Loader2, PlusCircle, Lock, Download, CheckCircle, Sparkles, Rocket, Pencil } from "lucide-react";
import { toast } from "sonner";
import { useAuthStore } from "@/store/authStore";
import { GenerateRoadmapDialog } from "@/components/roadmap/GenerateRoadmapDialog";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import RoadmapCard from "./RoadmapCard";
import { RoadmapItem, DEVELOPMENT_PHASES } from "@/types";
import { useNavigate } from "react-router-dom";
import { useSubscriptionStore } from "@/store/subscriptionStore";

interface RoadmapPhaseProps {
  validationId: string;
  phase: string;
  isAdmin?: boolean;
}

export default function RoadmapPhase({ validationId, phase, isAdmin = false }: RoadmapPhaseProps) {
  const { roadmaps, isLoading, requestRoadmap, publishRoadmap, generateAllPhases } = useRoadmapStore();
  const { userId } = useAuthStore();
  const { checkRoadmapAccess, getUserAccessiblePhases } = useSubscriptionStore();
  const navigate = useNavigate();
  const [roadmap, setRoadmap] = useState<RoadmapItem | null>(null);
  const [generating, setGenerating] = useState(false);
  const [publishing, setPublishing] = useState(false);
  const [showGenerateDialog, setShowGenerateDialog] = useState(false);
  const [showEditDialog, setShowEditDialog] = useState(false);
  const [hasAccess, setHasAccess] = useState(false);
  const [accessChecking, setAccessChecking] = useState(true);

  // Check phase access - more robust approach
  const checkDirectAccess = useCallback(async () => {
    if (isAdmin) {
      setHasAccess(true);
      setAccessChecking(false);
      return;
    }
    
    if (phase === 'interactive') {
      setHasAccess(true);
      setAccessChecking(false);
      return;
    }
    
    try {
      setAccessChecking(true);
      
      // First, try to get all accessible phases
      const accessiblePhases = await getUserAccessiblePhases(validationId);
      const hasAccess = accessiblePhases.includes(phase);
      
      setHasAccess(hasAccess);
      
      // If that fails or returns false, do a direct check with the specific phase and project
      if (!hasAccess) {
        const directAccess = await checkRoadmapAccess(phase, validationId);
        setHasAccess(directAccess);
      }
    } catch (err) {
      console.error("Error checking access:", err);
      // Fallback to direct check
      try {
        const fallbackAccess = await checkRoadmapAccess(phase, validationId);
        setHasAccess(fallbackAccess);
      } catch (fallbackErr) {
        console.error("Fallback access check failed:", fallbackErr);
        setHasAccess(false);
      }
    } finally {
      setAccessChecking(false);
    }
  }, [isAdmin, phase, validationId, checkRoadmapAccess, getUserAccessiblePhases]);
  
  // Find the roadmap for this phase
  useEffect(() => {
    const foundRoadmap = roadmaps.find(r => r.phase === phase);
    setRoadmap(foundRoadmap || null);
    
    // Reset access state and perform a fresh check
    setHasAccess(false);
    setAccessChecking(true);
    checkDirectAccess();
  }, [roadmaps, phase, checkDirectAccess]);

  const handleRequestRoadmap = async () => {
    if (!isAdmin && !hasAccess) {
      toast.error(`You don't have access to the ${getPhaseName(phase)} phase`);
      return;
    }
    
    try {
      setGenerating(true);
      await requestRoadmap(validationId, phase);
      toast.success("Roadmap requested successfully");
    } catch (error) {
      toast.error("Failed to request roadmap");
    } finally {
      setGenerating(false);
    }
  };

  const handleGenerationSuccess = () => {
    toast.success("Roadmap generated successfully");
    setShowGenerateDialog(false);
    setShowEditDialog(false);
  };

  const handlePublishRoadmap = async () => {
    if (!roadmap) return;
    
    try {
      setPublishing(true);
      await publishRoadmap(roadmap.id);
      toast.success("Roadmap published successfully");
    } catch (error) {
      toast.error("Failed to publish roadmap");
    } finally {
      setPublishing(false);
    }
  };

  const handleUpgradeClick = () => {
    navigate(`/pricing?project=${validationId}`);
  };

  const handleDownload = () => {
    if (!roadmap || !roadmap.content) {
      toast.error("No roadmap content to download");
      return;
    }

    const dataStr = "data:text/json;charset=utf-8," + encodeURIComponent(JSON.stringify(roadmap.content, null, 2));
    const downloadAnchorNode = document.createElement('a');
    downloadAnchorNode.setAttribute("href", dataStr);
    downloadAnchorNode.setAttribute("download", `roadmap-${roadmap.phase}.json`);
    document.body.appendChild(downloadAnchorNode);
    downloadAnchorNode.click();
    downloadAnchorNode.remove();
  };

  const handleGenerateAllPhases = async () => {
    if (!isAdmin) return;
    
    try {
      setGenerating(true);
      // Get all accessible phases for admins (all phases)
      const phases = ["interactive", "poc", "mvp", "production"];
      await generateAllPhases(validationId, phases);
      toast.success("Generated roadmaps for all phases");
    } catch (error) {
      toast.error("Failed to generate roadmaps for all phases");
    } finally {
      setGenerating(false);
    }
  };

  const getPhaseName = (phaseId: string) => {
    return DEVELOPMENT_PHASES[phaseId]?.name || phaseId;
  };
  
  const getPhaseColors = (phaseId: string) => {
    switch (phaseId) {
      case "interactive":
        return {
          bg: "bg-blue-50 dark:bg-blue-900/20",
          border: "border-blue-200 dark:border-blue-800",
          text: "text-blue-600 dark:text-blue-400",
          gradient: "bg-gradient-to-r from-blue-500 to-blue-600"
        };
      case "poc":
        return {
          bg: "bg-purple-50 dark:bg-purple-900/20",
          border: "border-purple-200 dark:border-purple-800",
          text: "text-purple-600 dark:text-purple-400",
          gradient: "bg-gradient-to-r from-purple-500 to-purple-600"
        };
      case "mvp":
        return {
          bg: "bg-orange-50 dark:bg-orange-900/20",
          border: "border-orange-200 dark:border-orange-800",
          text: "text-orange-600 dark:text-orange-400",
          gradient: "bg-gradient-to-r from-orange-500 to-orange-600"
        };
      case "production":
        return {
          bg: "bg-green-50 dark:bg-green-900/20",
          border: "border-green-200 dark:border-green-800",
          text: "text-green-600 dark:text-green-400",
          gradient: "bg-gradient-to-r from-green-500 to-green-600"
        };
      default:
        return {
          bg: "bg-gray-50 dark:bg-gray-900/20",
          border: "border-gray-200 dark:border-gray-800",
          text: "text-gray-600 dark:text-gray-400",
          gradient: "bg-gradient-to-r from-gray-500 to-gray-600"
        };
    }
  };

  // Helper to render the main content based on roadmap status
  const renderRoadmapContent = () => {
    // If we're still checking access or loading data, show a loader
    if (accessChecking || isLoading) {
      return (
        <div className="flex flex-col items-center justify-center py-12 space-y-4">
          <Loader2 className="h-8 w-8 animate-spin text-primary" />
          <p className="text-muted-foreground">Loading roadmap...</p>
        </div>
      );
    }

    // If no access, show a card with upgrade option (unless admin)
    if (!hasAccess && !isAdmin) {
      return (
        <Card className="border border-dashed shadow-sm hover:shadow-md transition-all duration-300">
          <CardContent className="py-12 text-center">
            <Lock className="h-12 w-12 mx-auto mb-4 text-muted-foreground" />
            <h3 className="text-lg font-medium mb-2">Premium Phase Locked</h3>
            <p className="text-muted-foreground mb-6 max-w-md mx-auto">
              Unlock the {getPhaseName(phase)} phase to get a detailed development roadmap
            </p>
            <Button onClick={handleUpgradeClick} className="gap-2 bg-gradient-to-r from-blue-500 to-indigo-600 hover:from-blue-600 hover:to-indigo-700 shadow-md hover:shadow-lg transition-all duration-300">
              <Sparkles className="h-4 w-4" />
              Upgrade to Unlock
            </Button>
          </CardContent>
        </Card>
      );
    }

    // If no roadmap exists yet, show generation options
    if (!roadmap) {
      return (
        <Card className="border border-dashed shadow-sm hover:shadow-md transition-all duration-300">
          <CardContent className="py-12 text-center">
            <div className="max-w-md mx-auto">
              <h3 className="text-lg font-medium mb-2">No Roadmap Available</h3>
              <p className="text-muted-foreground mb-6">
                Generate a {getPhaseName(phase)} roadmap for this project
              </p>
              <div className="flex flex-col sm:flex-row items-center justify-center gap-2">
                <Button 
                  onClick={() => setShowGenerateDialog(true)} 
                  className="gap-2 bg-gradient-to-r from-blue-500 to-indigo-600 hover:from-blue-600 hover:to-indigo-700 shadow-md hover:shadow-lg transition-all duration-300"
                  disabled={generating}
                >
                  {generating ? (
                    <Loader2 className="h-4 w-4 animate-spin" />
                  ) : (
                    <PlusCircle className="h-4 w-4" />
                  )}
                  Generate AI Roadmap
                </Button>
                {isAdmin && (
                  <>
                    <Button 
                      onClick={handleRequestRoadmap} 
                      variant="outline"
                      className="gap-2"
                      disabled={generating}
                    >
                      Create Manual Roadmap
                    </Button>
                    <Button 
                      onClick={handleGenerateAllPhases} 
                      variant="secondary"
                      className="gap-2"
                      disabled={generating}
                    >
                      <Rocket className="h-4 w-4" />
                      Generate All Phases
                    </Button>
                  </>
                )}
              </div>
            </div>
          </CardContent>
        </Card>
      );
    }

    // If roadmap exists but has no content (pending/in progress)
    if (roadmap && !roadmap.content) {
      const isPending = roadmap.status === 'pending';
      const isInProgress = roadmap.status === 'in_progress';
      
      return (
        <Card className="border border-dashed shadow-sm hover:shadow-md transition-all duration-300">
          <CardContent className="py-10 text-center">
            <div className="max-w-md mx-auto">
              <Loader2 className="h-12 w-12 mx-auto mb-4 animate-spin text-primary/70" />
              <h3 className="text-lg font-medium mb-2">
                {isPending ? "Roadmap Pending Generation" : "Roadmap Being Generated"}
              </h3>
              <p className="text-muted-foreground mb-6">
                {isPending 
                  ? "Your roadmap request has been received and is waiting to be processed." 
                  : "Your roadmap is currently being generated. This may take a few minutes."}
              </p>
              
              {isAdmin && (
                <div className="flex flex-col sm:flex-row justify-center gap-2">
                  <Button 
                    onClick={() => setShowGenerateDialog(true)} 
                    className="gap-2 bg-gradient-to-r from-blue-500 to-indigo-600 hover:from-blue-600 hover:to-indigo-700 shadow-md hover:shadow-lg transition-all duration-300"
                  >
                    <PlusCircle className="h-4 w-4" />
                    Generate Now
                  </Button>
                </div>
              )}
            </div>
          </CardContent>
        </Card>
      );
    }

    // If roadmap exists and has content (draft or published)
    return (
      <div className="space-y-6">
        {roadmap.status === 'draft' && isAdmin && (
          <Alert className={`${getPhaseColors(phase).bg} ${getPhaseColors(phase).border}`}>
            <Pencil className={`h-4 w-4 ${getPhaseColors(phase).text}`} />
            <AlertTitle className={`font-medium ${getPhaseColors(phase).text}`}>Draft Roadmap</AlertTitle>
            <AlertDescription className={getPhaseColors(phase).text}>
              <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-4 pt-2">
                <p>This roadmap is in draft mode and is not visible to users.</p>
                <div className="flex items-center gap-2">
                  <Button
                    onClick={handlePublishRoadmap}
                    size="sm"
                    className={getPhaseColors(phase).gradient}
                    disabled={publishing}
                  >
                    {publishing ? (
                      <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    ) : (
                      <CheckCircle className="mr-2 h-4 w-4" />
                    )}
                    Publish Roadmap
                  </Button>
                </div>
              </div>
            </AlertDescription>
          </Alert>
        )}
        
        <div className="grid grid-cols-1 gap-6">
          {roadmap && roadmap.content && (
            <RoadmapCard 
              roadmap={roadmap}
              isAdmin={isAdmin}
              onEdit={() => setShowEditDialog(true)}
              onDownload={handleDownload}
            />
          )}
        </div>
      </div>
    );
  };

  return (
    <div>
      {renderRoadmapContent()}
      
      {/* Generate Roadmap Dialog */}
      {showGenerateDialog && (
        <GenerateRoadmapDialog 
          validationId={validationId}
          phase={phase}
          externalOpen={showGenerateDialog}
          onOpenChange={setShowGenerateDialog}
          onSuccess={handleGenerationSuccess}
        />
      )}
      
      {/* Edit Roadmap Dialog */}
      {showEditDialog && roadmap && (
        <GenerateRoadmapDialog 
          validationId={validationId}
          phase={phase}
          externalOpen={showEditDialog}
          onOpenChange={setShowEditDialog}
          onSuccess={handleGenerationSuccess}
          existingRoadmapId={roadmap.id}
          existingContent={roadmap.content}
          isEditMode={true}
        />
      )}
    </div>
  );
}
