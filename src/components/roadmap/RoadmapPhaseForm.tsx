
import { <PERSON>, CardContent } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Button } from "@/components/ui/button";
import { Textarea } from "@/components/ui/textarea";
import { PlusCircle, Trash2, Clock, DollarSign, Users, CheckSquare, AlertTriangle } from "lucide-react";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";

interface RoadmapPhaseFormProps {
  phase: any;
  phaseIndex: number;
  handleUpdatePhase: (index: number, key: string, value: any) => void;
  handleRemovePhase: (index: number) => void;
  handleAddTask: (phaseIndex: number) => void;
}

export function RoadmapPhaseForm({
  phase,
  phaseIndex,
  handleUpdatePhase,
  handleRemovePhase,
  handleAddTask
}: RoadmapPhaseFormProps) {
  return (
    <Card className="border rounded-xl shadow-sm overflow-hidden hover:shadow-md transition-all duration-300">
      <div className="bg-gradient-to-r from-blue-50 to-indigo-50 dark:from-blue-950/30 dark:to-indigo-950/30 border-b p-4">
        <h3 className="font-medium text-lg text-gray-800 dark:text-gray-100">Phase {phaseIndex + 1}</h3>
      </div>
      <CardContent className="pt-6 space-y-6">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div className="space-y-1.5">
            <Label htmlFor={`phase-name-${phaseIndex}`} className="text-sm font-medium flex items-center">
              <span className="w-1 h-1 bg-primary rounded-full mr-1.5"></span>
              Phase Name
            </Label>
            <Input 
              id={`phase-name-${phaseIndex}`} 
              value={phase.name || ''}
              onChange={(e) => handleUpdatePhase(phaseIndex, 'name', e.target.value)}
              className="border-gray-200 focus:border-primary"
              placeholder="e.g. Design Phase"
            />
          </div>
          <div className="space-y-1.5">
            <Label htmlFor={`phase-duration-${phaseIndex}`} className="text-sm font-medium flex items-center">
              <Clock className="h-3.5 w-3.5 mr-1.5 text-gray-500" />
              Duration
            </Label>
            <Input 
              id={`phase-duration-${phaseIndex}`} 
              value={phase.duration || ''}
              onChange={(e) => handleUpdatePhase(phaseIndex, 'duration', e.target.value)}
              className="border-gray-200 focus:border-primary"
              placeholder="e.g. 4 weeks"
            />
          </div>
        </div>
        
        <div className="space-y-1.5">
          <Label htmlFor={`phase-costEstimate-${phaseIndex}`} className="text-sm font-medium flex items-center">
            <DollarSign className="h-3.5 w-3.5 mr-1.5 text-gray-500" />
            Cost Estimate ($)
          </Label>
          <Input 
            id={`phase-costEstimate-${phaseIndex}`} 
            type="number"
            value={phase.costEstimate || 0}
            onChange={(e) => handleUpdatePhase(phaseIndex, 'costEstimate', parseFloat(e.target.value) || 0)}
            className="border-gray-200 focus:border-primary"
            placeholder="0.00"
          />
        </div>
        
        <div className="space-y-1.5">
          <Label htmlFor={`phase-deliverables-${phaseIndex}`} className="text-sm font-medium flex items-center">
            <CheckSquare className="h-3.5 w-3.5 mr-1.5 text-gray-500" />
            Deliverables (comma-separated)
          </Label>
          <Textarea 
            id={`phase-deliverables-${phaseIndex}`}
            value={(phase.deliverables || []).join(', ')}
            onChange={(e) => handleUpdatePhase(
              phaseIndex, 
              'deliverables', 
              e.target.value.split(',').map((d: string) => d.trim()).filter(Boolean)
            )}
            className="border-gray-200 focus:border-primary min-h-[100px]"
            placeholder="e.g. UI Design, API Documentation, Database Schema"
          />
        </div>

        <div className="space-y-3">
          <div className="flex items-center justify-between">
            <Label className="text-sm font-medium flex items-center">
              <AlertTriangle className="h-3.5 w-3.5 mr-1.5 text-gray-500" />
              Risks and Mitigations
            </Label>
          </div>
          
          {(phase.risksAndMitigations || []).map((risk: any, riskIndex: number) => (
            <Card key={riskIndex} className="p-4 border rounded-lg shadow-sm bg-white dark:bg-gray-900">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-3">
                <div className="space-y-1.5">
                  <Label className="text-xs font-medium text-gray-600 dark:text-gray-400">Risk</Label>
                  <Input 
                    value={risk.risk || ''}
                    onChange={(e) => {
                      const updatedRisks = [...(phase.risksAndMitigations || [])];
                      updatedRisks[riskIndex] = { ...risk, risk: e.target.value };
                      handleUpdatePhase(phaseIndex, 'risksAndMitigations', updatedRisks);
                    }}
                    className="border-gray-200 focus:border-primary"
                    placeholder="Describe the risk"
                  />
                </div>
                <div className="space-y-1.5">
                  <Label className="text-xs font-medium text-gray-600 dark:text-gray-400">Mitigation</Label>
                  <Input 
                    value={risk.mitigation || ''}
                    onChange={(e) => {
                      const updatedRisks = [...(phase.risksAndMitigations || [])];
                      updatedRisks[riskIndex] = { ...risk, mitigation: e.target.value };
                      handleUpdatePhase(phaseIndex, 'risksAndMitigations', updatedRisks);
                    }}
                    className="border-gray-200 focus:border-primary"
                    placeholder="How to mitigate this risk"
                  />
                </div>
              </div>
              
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-1.5">
                  <Label className="text-xs font-medium text-gray-600 dark:text-gray-400">Probability</Label>
                  <Select 
                    value={risk.probability || 'Medium'}
                    onValueChange={(value) => {
                      const updatedRisks = [...(phase.risksAndMitigations || [])];
                      updatedRisks[riskIndex] = { ...risk, probability: value };
                      handleUpdatePhase(phaseIndex, 'risksAndMitigations', updatedRisks);
                    }}
                  >
                    <SelectTrigger className="border-gray-200 focus:border-primary">
                      <SelectValue placeholder="Select probability" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="Low" className="flex items-center">
                        <span className="h-2 w-2 rounded-full bg-green-500 mr-2"></span>
                        Low
                      </SelectItem>
                      <SelectItem value="Medium" className="flex items-center">
                        <span className="h-2 w-2 rounded-full bg-yellow-500 mr-2"></span>
                        Medium
                      </SelectItem>
                      <SelectItem value="High" className="flex items-center">
                        <span className="h-2 w-2 rounded-full bg-red-500 mr-2"></span>
                        High
                      </SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <div className="space-y-1.5">
                  <Label className="text-xs font-medium text-gray-600 dark:text-gray-400">Impact</Label>
                  <Select 
                    value={risk.impact || 'Medium'}
                    onValueChange={(value) => {
                      const updatedRisks = [...(phase.risksAndMitigations || [])];
                      updatedRisks[riskIndex] = { ...risk, impact: value };
                      handleUpdatePhase(phaseIndex, 'risksAndMitigations', updatedRisks);
                    }}
                  >
                    <SelectTrigger className="border-gray-200 focus:border-primary">
                      <SelectValue placeholder="Select impact" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="Low" className="flex items-center">
                        <span className="h-2 w-2 rounded-full bg-green-500 mr-2"></span>
                        Low
                      </SelectItem>
                      <SelectItem value="Medium" className="flex items-center">
                        <span className="h-2 w-2 rounded-full bg-yellow-500 mr-2"></span>
                        Medium
                      </SelectItem>
                      <SelectItem value="High" className="flex items-center">
                        <span className="h-2 w-2 rounded-full bg-red-500 mr-2"></span>
                        High
                      </SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>
              
              <Button 
                variant="ghost" 
                size="sm" 
                onClick={() => {
                  const updatedRisks = [...(phase.risksAndMitigations || [])];
                  updatedRisks.splice(riskIndex, 1);
                  handleUpdatePhase(phaseIndex, 'risksAndMitigations', updatedRisks);
                }}
                className="mt-3 text-red-500 hover:text-red-600 hover:bg-red-50 dark:hover:bg-red-950/20"
              >
                <Trash2 className="h-4 w-4 mr-1" /> Remove Risk
              </Button>
            </Card>
          ))}
          
          <Button 
            variant="outline" 
            size="sm" 
            onClick={() => {
              const updatedRisks = [...(phase.risksAndMitigations || [])];
              updatedRisks.push({
                risk: "New Risk",
                probability: "Medium",
                impact: "Medium",
                mitigation: "Mitigation strategy"
              });
              handleUpdatePhase(phaseIndex, 'risksAndMitigations', updatedRisks);
            }}
            className="mt-2 border-dashed"
          >
            <PlusCircle className="h-4 w-4 mr-1" /> Add Risk
          </Button>
        </div>
        
        <div className="space-y-4">
          <div className="flex items-center justify-between">
            <Label className="text-sm font-medium flex items-center">
              <Users className="h-3.5 w-3.5 mr-1.5 text-gray-500" />
              Tasks
            </Label>
            <Button 
              variant="outline" 
              size="sm" 
              onClick={() => handleAddTask(phaseIndex)}
              className="flex items-center gap-1 border-primary/30 text-primary hover:border-primary"
            >
              <PlusCircle className="h-4 w-4" /> Add Task
            </Button>
          </div>
          
          {(phase.tasks || []).map((task: any, taskIndex: number) => (
            <TaskForm 
              key={taskIndex}
              task={task}
              phaseIndex={phaseIndex}
              taskIndex={taskIndex}
              handleUpdatePhase={handleUpdatePhase}
              phase={phase}
            />
          ))}
          
          {(!phase.tasks || phase.tasks.length === 0) && (
            <div className="bg-gray-50 dark:bg-gray-800/30 text-center rounded-lg border border-dashed p-6">
              <p className="text-muted-foreground text-sm">
                No tasks added yet. Click "Add Task" to create your first task.
              </p>
            </div>
          )}
        </div>
        
        <div className="pt-4 border-t">
          <Button 
            variant="ghost" 
            onClick={() => handleRemovePhase(phaseIndex)}
            className="text-red-500 hover:text-red-600 hover:bg-red-50 dark:hover:bg-red-950/20"
          >
            <Trash2 className="h-4 w-4 mr-2" /> Remove Phase
          </Button>
        </div>
      </CardContent>
    </Card>
  );
}

interface TaskFormProps {
  task: any;
  phaseIndex: number;
  taskIndex: number;
  phase: any;
  handleUpdatePhase: (index: number, key: string, value: any) => void;
}

function TaskForm({
  task,
  phaseIndex,
  taskIndex,
  handleUpdatePhase,
  phase
}: TaskFormProps) {
  const updateTask = (key: string, value: any) => {
    const updatedTasks = [...(phase.tasks || [])];
    updatedTasks[taskIndex] = {
      ...updatedTasks[taskIndex],
      [key]: value
    };
    handleUpdatePhase(phaseIndex, 'tasks', updatedTasks);
  };
  
  const handleRemoveTask = () => {
    const updatedTasks = [...(phase.tasks || [])];
    updatedTasks.splice(taskIndex, 1);
    handleUpdatePhase(phaseIndex, 'tasks', updatedTasks);
  };
  
  return (
    <Card key={taskIndex} className="border rounded-lg shadow-sm bg-white dark:bg-gray-900 overflow-hidden">
      <div className="bg-blue-50/50 dark:bg-blue-950/30 border-b p-3">
        <h4 className="font-medium text-sm">Task {taskIndex + 1}</h4>
      </div>
      <CardContent className="pt-4 space-y-4">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div className="space-y-1.5">
            <Label className="text-xs font-medium text-gray-600 dark:text-gray-400">Task Name</Label>
            <Input 
              value={task.name || ''}
              onChange={(e) => updateTask('name', e.target.value)}
              className="border-gray-200 focus:border-primary"
              placeholder="e.g. Design User Interface"
            />
          </div>
          <div className="space-y-1.5">
            <Label className="text-xs font-medium text-gray-600 dark:text-gray-400">Duration</Label>
            <Input 
              value={task.duration || ''}
              onChange={(e) => updateTask('duration', e.target.value)}
              className="border-gray-200 focus:border-primary"
              placeholder="e.g. 1 week"
            />
          </div>
        </div>
        
        <div className="space-y-1.5">
          <Label className="text-xs font-medium text-gray-600 dark:text-gray-400">Description</Label>
          <Textarea 
            value={task.description || ''}
            onChange={(e) => updateTask('description', e.target.value)}
            className="border-gray-200 focus:border-primary min-h-[80px]"
            placeholder="Describe the task in detail"
          />
        </div>
        
        <div className="space-y-1.5">
          <Label className="text-xs font-medium text-gray-600 dark:text-gray-400">Cost ($)</Label>
          <Input 
            type="number"
            value={task.cost || 0}
            onChange={(e) => updateTask('cost', parseFloat(e.target.value) || 0)}
            className="border-gray-200 focus:border-primary"
            placeholder="0.00"
          />
        </div>
        
        <div className="space-y-1.5">
          <Label className="text-xs font-medium text-gray-600 dark:text-gray-400">Resources (comma-separated)</Label>
          <Textarea 
            value={Array.isArray(task.resources) 
              ? task.resources.map((r: any) => 
                  typeof r === 'object' 
                    ? `${r.role || ''} (${r.level || ''}, ${r.allocation || 1.0})`
                    : r
                ).join(', ')
              : ''}
            onChange={(e) => updateTask(
              'resources', 
              e.target.value.split(',')
                .map((r: string) => {
                  const trim = r.trim();
                  if (!trim) return null;
                  
                  // Try to parse role, level, allocation from string
                  const match = trim.match(/(.+)\s+\((.+),\s*(.+)\)/);
                  if (match) {
                    return {
                      role: match[1].trim(),
                      level: match[2].trim(),
                      allocation: parseFloat(match[3]) || 1.0
                    };
                  }
                  return trim;
                })
                .filter(Boolean)
            )}
            className="border-gray-200 focus:border-primary"
            placeholder="e.g. Designer (Senior, 0.5), Developer (Mid, 1.0)"
          />
        </div>
        
        <div className="space-y-1.5">
          <Label className="text-xs font-medium text-gray-600 dark:text-gray-400">Deliverables (comma-separated)</Label>
          <Textarea 
            value={Array.isArray(task.deliverables) ? task.deliverables.join(', ') : ''}
            onChange={(e) => updateTask(
              'deliverables', 
              e.target.value.split(',').map((d: string) => d.trim()).filter(Boolean)
            )}
            className="border-gray-200 focus:border-primary"
            placeholder="e.g. UI Mockups, Component Specs, Prototype"
          />
        </div>
        
        <div className="space-y-1.5">
          <Label className="text-xs font-medium text-gray-600 dark:text-gray-400">Risks (comma-separated)</Label>
          <Textarea 
            value={Array.isArray(task.risks) ? task.risks.join(', ') : ''}
            onChange={(e) => updateTask(
              'risks', 
              e.target.value.split(',').map((r: string) => r.trim()).filter(Boolean)
            )}
            className="border-gray-200 focus:border-primary"
            placeholder="e.g. Technical complexity, Resource constraints"
          />
        </div>
        
        <div className="space-y-1.5">
          <Label className="text-xs font-medium text-gray-600 dark:text-gray-400">Dependencies (comma-separated)</Label>
          <Textarea 
            value={Array.isArray(task.dependencies) ? task.dependencies.join(', ') : ''}
            onChange={(e) => updateTask(
              'dependencies', 
              e.target.value.split(',').map((d: string) => d.trim()).filter(Boolean)
            )}
            className="border-gray-200 focus:border-primary"
            placeholder="e.g. Task 1, UX Research"
          />
        </div>
        
        <Button 
          variant="ghost" 
          size="sm" 
          onClick={handleRemoveTask}
          className="text-red-500 hover:text-red-600 hover:bg-red-50 dark:hover:bg-red-950/20"
        >
          <Trash2 className="h-4 w-4 mr-1" /> Remove Task
        </Button>
      </CardContent>
    </Card>
  );
}
