
import { useState, useEffect } from "react";
import { 
  <PERSON><PERSON>, 
  DialogContent, 
  DialogDescription, 
  Di<PERSON>Footer, 
  Di<PERSON>Header, 
  DialogTitle 
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Checkbox } from "@/components/ui/checkbox";
import { Textarea } from "@/components/ui/textarea";
import { 
  AlertCircle, 
  Loader2, 
  Rocket,
  SparklesIcon,
  Code,
  Users,
  Edit
} from "lucide-react";
import { toast } from "sonner";
import { DEVELOPMENT_PHASES } from "@/types";
import { supabase } from "@/integrations/supabase/client";
import { useAuthStore } from "@/store/authStore";
import { Alert, AlertDescription } from "../ui/alert";
import { useSubscriptionStore } from "@/store/subscriptionStore";
import { useRoadmapStore } from "@/store/roadmapStore";

interface GenerateRoadmapDialogProps {
  validationId: string;
  phase?: string;
  externalOpen?: boolean;
  onOpenChange?: (open: boolean) => void;
  onSuccess?: () => void;
  existingRoadmapId?: string;
  existingContent?: any;
  isEditMode?: boolean;
}

export function GenerateRoadmapDialog({
  validationId,
  phase = "interactive",
  externalOpen,
  onOpenChange,
  onSuccess,
  existingRoadmapId,
  existingContent,
  isEditMode = false
}: GenerateRoadmapDialogProps) {
  const { isAdmin } = useAuthStore();
  const { getUserAccessiblePhases } = useSubscriptionStore();
  const { updateRoadmap } = useRoadmapStore();
  
  const [open, setOpen] = useState(false);
  const [loading, setLoading] = useState(false);
  const [selectedPhase, setSelectedPhase] = useState(phase);
  const [accessiblePhases, setAccessiblePhases] = useState<string[]>(['interactive']);
  const [phaseAccessMap, setPhaseAccessMap] = useState<Record<string, boolean>>({
    interactive: true,
    poc: false,
    mvp: false,
    production: false
  });
  const [generateAllPhases, setGenerateAllPhases] = useState(false);
  const [checkingAccess, setCheckingAccess] = useState(true);
  const [editableContent, setEditableContent] = useState<string>("");

  useEffect(() => {
    if (externalOpen !== undefined) {
      setOpen(externalOpen);
    }
  }, [externalOpen]);

  const handleOpenChange = (newOpen: boolean) => {
    setOpen(newOpen);
    if (onOpenChange) onOpenChange(newOpen);
  };

  useEffect(() => {
    if (existingContent && typeof existingContent === 'object' && isEditMode) {
      // Format the JSON content for editing
      setEditableContent(JSON.stringify(existingContent, null, 2));
    }
  }, [existingContent, isEditMode]);

  useEffect(() => {
    const checkAccess = async () => {
      setCheckingAccess(true);
      
      try {
        if (isAdmin) {
          const allPhases = ['interactive', 'poc', 'mvp', 'production'];
          setAccessiblePhases(allPhases);
          
          const accessMap: Record<string, boolean> = {};
          allPhases.forEach(p => { accessMap[p] = true; });
          setPhaseAccessMap(accessMap);
          
          if (!allPhases.includes(selectedPhase)) {
            setSelectedPhase(allPhases[0]);
          }
          
          setCheckingAccess(false);
          return;
        }
        
        const phases = await getUserAccessiblePhases(validationId);
        
        setAccessiblePhases(phases);
        
        const accessMap: Record<string, boolean> = {
          interactive: false,
          poc: false,
          mvp: false,
          production: false
        };
        
        phases.forEach(p => { accessMap[p] = true; });
        setPhaseAccessMap(accessMap);
        
        if (!phases.includes(selectedPhase) && phases.length > 0) {
          setSelectedPhase(phases[0]);
        }
      } catch (error) {
        console.error('Error checking phase access:', error);
        toast.error('Failed to check phase access');
        
        setAccessiblePhases(['interactive']);
        setPhaseAccessMap({
          interactive: true,
          poc: false,
          mvp: false,
          production: false
        });
        
        setSelectedPhase('interactive');
      } finally {
        setCheckingAccess(false);
      }
    };
    
    checkAccess();
  }, [validationId, isAdmin, getUserAccessiblePhases, selectedPhase]);

  const handleSubmit = async () => {
    if (!validationId) return;
    
    try {
      setLoading(true);
      
      // Direct editing mode
      if (isEditMode && existingRoadmapId) {
        try {
          // Parse the edited content
          const updatedContent = JSON.parse(editableContent);
          
          // Update the roadmap with the edited content
          await updateRoadmap(existingRoadmapId, updatedContent);
          
          toast.success('Roadmap content updated successfully');
          
          if (onSuccess) {
            onSuccess();
          }
          
          handleOpenChange(false);
          return;
        } catch (parseError) {
          console.error('Error parsing JSON:', parseError);
          toast.error('Invalid JSON format. Please check your edits.');
          return;
        }
      }
      
      if (generateAllPhases && isAdmin) {
        const phasesToGenerate = Object.keys(phaseAccessMap).filter(p => phaseAccessMap[p]);
        const results = [];
        
        for (const phaseToGenerate of phasesToGenerate) {
          const { data, error } = await supabase.functions.invoke('generate-roadmap', {
            body: { 
              validationId, 
              phase: phaseToGenerate,
              existingRoadmapId: phaseToGenerate === selectedPhase ? existingRoadmapId : undefined
            }
          });
          
          if (error) {
            console.error(`Error generating roadmap for phase ${phaseToGenerate}:`, error);
            results.push({ phase: phaseToGenerate, success: false });
          } else {
            results.push({ phase: phaseToGenerate, success: true });
          }
        }
        
        const successCount = results.filter(r => r.success).length;
        if (successCount > 0) {
          toast.success(`Generated ${successCount} of ${results.length} roadmaps successfully`);
        } else {
          toast.error('Failed to generate any roadmaps');
        }
      } else {
        const { data, error } = await supabase.functions.invoke('generate-roadmap', {
          body: { 
            validationId, 
            phase: selectedPhase,
            existingRoadmapId
          }
        });
        
        if (error) {
          console.error('Error generating roadmap:', error);
          toast.error('Failed to generate roadmap');
          return;
        }
        
        toast.success('Roadmap generated successfully');
      }
      
      if (onSuccess) {
        onSuccess();
      }
      
      handleOpenChange(false);
      
    } catch (error) {
      console.error('Error in handleSubmit:', error);
      toast.error('Failed to generate roadmap');
    } finally {
      setLoading(false);
    }
  };

  const getPhaseIcon = (phaseId: string) => {
    switch (phaseId) {
      case "interactive":
        return <SparklesIcon className="h-4 w-4" />;
      case "poc":
        return <Code className="h-4 w-4" />;
      case "mvp":
        return <Users className="h-4 w-4" />;
      case "production":
        return <Rocket className="h-4 w-4" />;
      default:
        return null;
    }
  };

  return (
    <Dialog open={open} onOpenChange={handleOpenChange}>
      <DialogContent className="sm:max-w-[600px]">
        <DialogHeader>
          <DialogTitle>
            {isEditMode ? "Edit Roadmap Content" : existingRoadmapId ? "Update Roadmap" : "Generate Roadmap"}
          </DialogTitle>
          <DialogDescription>
            {isEditMode 
              ? "Edit the JSON content of this roadmap directly"
              : existingRoadmapId 
                ? "Update the existing roadmap content"
                : "Generate a development roadmap for this project"}
          </DialogDescription>
        </DialogHeader>
        
        {checkingAccess ? (
          <div className="flex items-center justify-center py-4">
            <Loader2 className="h-6 w-6 animate-spin text-primary" />
          </div>
        ) : isEditMode ? (
          <div className="grid gap-4 py-4">
            <div className="grid gap-2">
              <Label htmlFor="content">Roadmap Content (JSON)</Label>
              <Textarea
                id="content"
                value={editableContent}
                onChange={(e) => setEditableContent(e.target.value)}
                className="font-mono text-sm h-80"
                placeholder="Edit the roadmap content in JSON format..."
              />
            </div>
          </div>
        ) : (
          <div className="grid gap-6 py-4">
            <div className="grid gap-2">
              <Label htmlFor="phase">Development Phase</Label>
              <Select 
                value={selectedPhase} 
                onValueChange={setSelectedPhase}
                disabled={loading || generateAllPhases}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Select phase" />
                </SelectTrigger>
                <SelectContent>
                  {Object.entries(DEVELOPMENT_PHASES).map(([id, { name }]) => (
                    <SelectItem 
                      key={id} 
                      value={id}
                      disabled={!phaseAccessMap[id]}
                      className={!phaseAccessMap[id] ? "opacity-50" : ""}
                    >
                      <div className="flex items-center gap-2">
                        {getPhaseIcon(id)}
                        <span>{name}</span>
                        {!phaseAccessMap[id] && (
                          <span className="text-xs text-muted-foreground ml-1">(locked)</span>
                        )}
                      </div>
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
            
            {isAdmin && (
              <div className="flex items-center space-x-2">
                <Checkbox 
                  id="generateAll" 
                  checked={generateAllPhases}
                  onCheckedChange={(checked) => setGenerateAllPhases(checked === true)}
                  disabled={loading}
                />
                <Label htmlFor="generateAll" className="font-normal cursor-pointer">
                  Generate all accessible phases at once
                </Label>
              </div>
            )}
            
            {!isAdmin && !phaseAccessMap[selectedPhase] && (
              <Alert variant="destructive">
                <AlertCircle className="h-4 w-4" />
                <AlertDescription>
                  You don't have access to the {DEVELOPMENT_PHASES[selectedPhase].name} phase.
                  Please upgrade your subscription to access this feature.
                </AlertDescription>
              </Alert>
            )}
          </div>
        )}
        
        <DialogFooter>
          <Button
            variant="outline"
            onClick={() => handleOpenChange(false)}
            disabled={loading}
          >
            Cancel
          </Button>
          <Button
            onClick={handleSubmit}
            disabled={loading || checkingAccess || (!isEditMode && !generateAllPhases && !phaseAccessMap[selectedPhase])}
          >
            {loading ? (
              <>
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                {isEditMode ? "Saving..." : generateAllPhases ? "Generating All Phases..." : "Generating..."}
              </>
            ) : (
              <>
                {isEditMode ? (
                  <Edit className="mr-2 h-4 w-4" />
                ) : (
                  <Rocket className="mr-2 h-4 w-4" />
                )}
                {isEditMode ? "Save Changes" : existingRoadmapId ? "Update Roadmap" : generateAllPhases ? "Generate All Phases" : "Generate Roadmap"}
              </>
            )}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
