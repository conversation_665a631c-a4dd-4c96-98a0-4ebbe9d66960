
import React from 'react';
import { Card, CardContent } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";

interface TeamMember {
  role: string;
  count: number;
  hourlyRate?: number;
  hours?: number;
  skills?: string[];
}

interface RoadmapTeamCompositionProps {
  teamMembers: TeamMember[] | null | undefined;
}

const RoadmapTeamComposition: React.FC<RoadmapTeamCompositionProps> = ({ teamMembers }) => {
  if (!teamMembers || teamMembers.length === 0) {
    return (
      <Card className="bg-muted/40">
        <CardContent className="pt-6">
          <p className="text-muted-foreground text-sm text-center">No team composition available</p>
        </CardContent>
      </Card>
    );
  }

  // Calculate total team cost
  const totalCost = teamMembers.reduce((sum, member) => {
    if (member.hourlyRate && member.hours) {
      return sum + (member.hourlyRate * member.hours * (member.count || 1));
    }
    return sum;
  }, 0);

  return (
    <div className="space-y-4">
      {teamMembers.map((member, index) => (
        <Card key={index} className="overflow-hidden border border-border/50">
          <CardContent className="p-4">
            <div className="flex flex-col md:flex-row md:items-center justify-between gap-3">
              <div>
                <div className="flex items-center gap-2">
                  <h4 className="font-medium">{member.role}</h4>
                  {member.count > 1 && (
                    <Badge variant="outline" className="text-xs">
                      x{member.count}
                    </Badge>
                  )}
                </div>
                {member.skills && member.skills.length > 0 && (
                  <div className="mt-1 flex flex-wrap gap-1">
                    {member.skills.map((skill, skillIndex) => (
                      <Badge key={skillIndex} variant="secondary" className="text-xs">
                        {skill}
                      </Badge>
                    ))}
                  </div>
                )}
              </div>
              <div className="text-right">
                {member.hourlyRate && (
                  <div className="text-sm text-muted-foreground">
                    ${member.hourlyRate}/hour
                  </div>
                )}
                {member.hours && (
                  <div className="text-sm text-muted-foreground">
                    {member.hours} hours
                  </div>
                )}
                {member.hourlyRate && member.hours && (
                  <div className="font-medium">
                    ${(member.hourlyRate * member.hours * (member.count || 1)).toLocaleString()}
                  </div>
                )}
              </div>
            </div>
          </CardContent>
        </Card>
      ))}
      
      {totalCost > 0 && (
        <div className="flex justify-between items-center px-4 pt-2">
          <span className="text-sm font-medium">Total Team Cost:</span>
          <span className="font-semibold">${totalCost.toLocaleString()}</span>
        </div>
      )}
    </div>
  );
};

export default RoadmapTeamComposition;
