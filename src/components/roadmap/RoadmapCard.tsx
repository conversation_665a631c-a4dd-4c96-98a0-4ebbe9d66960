
import React, { useState } from 'react';
import { <PERSON>, <PERSON><PERSON>ontent, CardDescription, Card<PERSON>eader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Accordion, AccordionContent, AccordionItem, AccordionTrigger } from "@/components/ui/accordion";
import { DEVELOPMENT_PHASES } from '@/types';
import RoadmapTeamComposition from './RoadmapTeamComposition';
import { Download, Pencil } from 'lucide-react';
import { Button } from '@/components/ui/button';

// Define all possible roadmap content types to handle variations in AI responses
interface RoadmapCardProps {
  name: string;
  phase: string;
  content: {
    phases?: Array<{
      name: string;
      duration?: string;
      cost?: string | number;
      hoursPerResource?: number | string;
      teamComposition?: Array<{
        role: string;
        count: number;
        hourlyRate?: number;
        hours?: number;
        skills?: string[];
      }>;
      team?: Array<{  // Alternative field name for teamComposition
        role: string;
        count: number;
        hourly_rate?: number; // Alternative field name
        rate?: number; // Alternative field name
        hours?: number;
        skills?: string[];
      }>;
      deliverables?: string[];
      milestones?: string[];
      goals?: string[]; // Alternative field name for milestones
      risks?: Array<{
        description: string;
        mitigation?: string;
      }> | string[];
    }>;
    summary?: {
      totalCost?: number;
      totalDuration?: string;
      keyRisks?: string[];
      recommendations?: string[];
    };
  };
  status?: 'draft' | 'published' | 'pending' | 'in_progress';
  isAdmin?: boolean;
  onEdit?: () => void;
  onDownload?: () => void;
}

// Alternate interface to handle the component being used with a roadmap object from the store
interface RoadmapWithItem {
  roadmap: any; // Using any here to accommodate the store format
  isAdmin?: boolean;
  onEdit?: () => void;
  onDownload?: () => void;
}

// Type guard to check which props interface is being used
function isRoadmapItem(props: RoadmapCardProps | RoadmapWithItem): props is RoadmapWithItem {
  return 'roadmap' in props;
}

const RoadmapCard: React.FC<RoadmapCardProps | RoadmapWithItem> = (props) => {
  const [expandedSection, setExpandedSection] = useState<string | null>("team");
  
  // Normalize props based on which interface was used
  const name = isRoadmapItem(props) ? props.roadmap.content?.name || getPhaseName(props.roadmap.phase) : props.name;
  const phase = isRoadmapItem(props) ? props.roadmap.phase : props.phase;
  const content = isRoadmapItem(props) ? props.roadmap.content : props.content;
  const status = isRoadmapItem(props) ? props.roadmap.status : props.status || 'draft';
  const isAdmin = isRoadmapItem(props) ? props.isAdmin : props.isAdmin;
  const onEdit = isRoadmapItem(props) ? props.onEdit : props.onEdit;
  const onDownload = isRoadmapItem(props) ? props.onDownload : props.onDownload;
  
  // Find the phase display name
  const phaseDisplayName = getPhaseName(phase);
  
  // Get the first phase from the phases array
  const phaseData = content?.phases && content.phases.length > 0 ? content.phases[0] : null;
  
  // Handle different field name variations
  const teamComposition = phaseData?.teamComposition || phaseData?.team || [];
  
  // Normalize team composition data to handle different field name formats
  const normalizedTeamComposition = teamComposition.map(member => ({
    role: member.role,
    count: member.count || 1,
    hourlyRate: member.hourlyRate || member.hourly_rate || member.rate || 0,
    hours: member.hours || 0,
    skills: member.skills || []
  }));
  
  // Handle risks in different formats
  const risks = phaseData?.risks || [];
  const normalizedRisks = risks.map(risk => {
    if (typeof risk === 'string') {
      return { description: risk, mitigation: '' };
    }
    return risk;
  });
  
  // Get stats from the phase data or summary
  const duration = phaseData?.duration || content?.summary?.totalDuration || 'Not specified';
  const cost = phaseData?.cost || content?.summary?.totalCost || 'Not specified';
  
  // Format cost if it's a number
  const formattedCost = typeof cost === 'number' ? `$${cost.toLocaleString()}` : cost;

  function getPhaseName(phaseId: string) {
    return DEVELOPMENT_PHASES[phaseId]?.name || phaseId;
  }
  
  // Get phase color for badges
  const getPhaseColor = () => {
    switch (phase) {
      case "interactive": return "bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300";
      case "poc": return "bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-300";
      case "mvp": return "bg-orange-100 text-orange-800 dark:bg-orange-900 dark:text-orange-300";
      case "production": return "bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300";
      default: return "bg-gray-100 text-gray-800 dark:bg-gray-800 dark:text-gray-300";
    }
  };
  
  return (
    <Card className="shadow-md hover:shadow-lg transition-all duration-300 overflow-hidden border-t-4 border-t-primary">
      <CardHeader className="pb-3 flex flex-row justify-between items-start">
        <div>
          <div className="flex items-center gap-2 mb-1">
            <CardTitle className="text-xl font-semibold">{name}</CardTitle>
            <Badge variant="outline" className={getPhaseColor()}>{phaseDisplayName}</Badge>
            {status && (
              <Badge variant={status === 'published' ? 'default' : 'secondary'}>
                {status.charAt(0).toUpperCase() + status.slice(1)}
              </Badge>
            )}
          </div>
          <CardDescription className="flex flex-col sm:flex-row gap-2 sm:gap-6 mt-1">
            <div className="flex items-center gap-1 text-muted-foreground">
              <span className="font-medium">Duration:</span> {duration}
            </div>
            <div className="flex items-center gap-1 text-muted-foreground">
              <span className="font-medium">Estimated Cost:</span> {formattedCost}
            </div>
            {phaseData?.hoursPerResource && (
              <div className="flex items-center gap-1 text-muted-foreground">
                <span className="font-medium">Hours/Resource:</span> {phaseData.hoursPerResource}
              </div>
            )}
          </CardDescription>
        </div>
        
        <div className="flex gap-2">
          {isAdmin && onEdit && (
            <Button size="sm" variant="outline" onClick={onEdit} className="h-8 px-3 flex items-center gap-1">
              <Pencil className="h-4 w-4" />
              <span>Edit</span>
            </Button>
          )}
          {onDownload && (
            <Button size="sm" variant="outline" onClick={onDownload} className="h-8 px-3 flex items-center gap-1">
              <Download className="h-4 w-4" />
              <span>Download</span>
            </Button>
          )}
        </div>
      </CardHeader>
      
      <CardContent className="pt-0">
        <Accordion 
          type="single" 
          collapsible 
          value={expandedSection || undefined}
          onValueChange={(value) => setExpandedSection(value)}
          className="w-full"
        >
          <AccordionItem value="team" className="border-b-0">
            <AccordionTrigger className="py-3 text-base font-medium hover:no-underline">
              Team Composition
            </AccordionTrigger>
            <AccordionContent className="pb-4">
              <RoadmapTeamComposition teamMembers={normalizedTeamComposition} />
            </AccordionContent>
          </AccordionItem>
          
          {phaseData?.deliverables && phaseData.deliverables.length > 0 && (
            <AccordionItem value="deliverables" className="border-b-0">
              <AccordionTrigger className="py-3 text-base font-medium hover:no-underline">
                Deliverables
              </AccordionTrigger>
              <AccordionContent className="pb-4">
                <ul className="space-y-2 ml-5 list-disc">
                  {phaseData.deliverables.map((deliverable, index) => (
                    <li key={index} className="text-sm">{deliverable}</li>
                  ))}
                </ul>
              </AccordionContent>
            </AccordionItem>
          )}
          
          {(phaseData?.milestones && phaseData.milestones.length > 0) || (phaseData?.goals && phaseData.goals.length > 0) ? (
            <AccordionItem value="milestones" className="border-b-0">
              <AccordionTrigger className="py-3 text-base font-medium hover:no-underline">
                Milestones
              </AccordionTrigger>
              <AccordionContent className="pb-4">
                <ul className="space-y-2 ml-5 list-disc">
                  {(phaseData?.milestones || phaseData?.goals || []).map((milestone, index) => (
                    <li key={index} className="text-sm">{milestone}</li>
                  ))}
                </ul>
              </AccordionContent>
            </AccordionItem>
          ) : null}
          
          {normalizedRisks.length > 0 && (
            <AccordionItem value="risks" className="border-b-0">
              <AccordionTrigger className="py-3 text-base font-medium hover:no-underline">
                Risks & Mitigation
              </AccordionTrigger>
              <AccordionContent className="pb-4">
                <div className="space-y-3">
                  {normalizedRisks.map((risk, index) => (
                    <Card key={index} className="border-border/50 overflow-hidden shadow-sm hover:shadow transition">
                      <CardContent className="p-3">
                        <div className="font-medium text-sm">{risk.description}</div>
                        {risk.mitigation && (
                          <div className="text-sm text-muted-foreground mt-1">
                            <span className="font-medium">Mitigation:</span> {risk.mitigation}
                          </div>
                        )}
                      </CardContent>
                    </Card>
                  ))}
                </div>
              </AccordionContent>
            </AccordionItem>
          )}
          
          {content?.summary?.recommendations && content.summary.recommendations.length > 0 && (
            <AccordionItem value="recommendations" className="border-b-0">
              <AccordionTrigger className="py-3 text-base font-medium hover:no-underline">
                Recommendations
              </AccordionTrigger>
              <AccordionContent className="pb-4">
                <ul className="space-y-2 ml-5 list-disc">
                  {content.summary.recommendations.map((recommendation, index) => (
                    <li key={index} className="text-sm">{recommendation}</li>
                  ))}
                </ul>
              </AccordionContent>
            </AccordionItem>
          )}
        </Accordion>
      </CardContent>
    </Card>
  );
};

export default RoadmapCard;
