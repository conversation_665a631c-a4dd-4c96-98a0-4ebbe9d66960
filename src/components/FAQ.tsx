
import { ChevronDown, ChevronUp } from "lucide-react";
import React from "react";

interface FAQItem {
  question: string;
  answer: string;
}

const faqs: FAQItem[] = [
  {
    question: "How long does it take to build a prototype?",
    answer: "We deliver interactive prototypes on an accelerated timeline based on project complexity. Our agile approach ensures you see progress regularly throughout development."
  },
  {
    question: "How is the prototype cost calculated?",
    answer: "Costs are calculated based on features, complexity, and scope. We provide transparent breakdowns and allow you to adjust features to match your budget."
  },
  {
    question: "What do I get with the prototype?",
    answer: "You receive a fully interactive web prototype, documentation, and source code. Perfect for user testing, investor pitches, and as a foundation for full development."
  },
  {
    question: "Can the prototype be converted into a full product?",
    answer: "Yes! Our prototypes are built with production-quality code, making them an excellent foundation for full product development."
  }
];

export const FAQ = () => {
  const [openIndex, setOpenIndex] = React.useState<number | null>(null);

  return (
    <section className="py-20 bg-gray-50 dark:bg-gray-900" id="faq">
      <div className="max-w-3xl mx-auto px-4">
        <h2 className="text-3xl font-bold text-center mb-12 text-gray-900 dark:text-gray-100">
          Frequently Asked Questions
        </h2>
        <div className="space-y-4">
          {faqs.map((faq, index) => (
            <div
              key={index}
              className="bg-white dark:bg-gray-800 rounded-lg shadow-sm overflow-hidden"
            >
              <button
                className="w-full px-6 py-4 text-left flex justify-between items-center hover:bg-gray-50 dark:hover:bg-gray-700/50"
                onClick={() => setOpenIndex(openIndex === index ? null : index)}
              >
                <span className="font-medium text-gray-900 dark:text-gray-100">{faq.question}</span>
                {openIndex === index ? (
                  <ChevronUp className="h-5 w-5 text-gray-500 dark:text-gray-400" />
                ) : (
                  <ChevronDown className="h-5 w-5 text-gray-500 dark:text-gray-400" />
                )}
              </button>
              {openIndex === index && (
                <div className="px-6 py-4 text-gray-600 dark:text-gray-300 bg-gray-50 dark:bg-gray-700/50">
                  {faq.answer}
                </div>
              )}
            </div>
          ))}
        </div>
      </div>
    </section>
  );
};
