
import { CheckCircle2 } from "lucide-react";

const benefits = [
  "Validate your idea with real users before full investment",
  "Get stakeholder buy-in with interactive demonstrations",
  "Identify potential issues early in development",
  "Save time and resources in the long run"
];

const WhyPrototype = () => {
  return (
    <div className="mt-24 bg-gradient-to-br from-indigo-50 to-purple-50 dark:from-indigo-900/10 dark:to-purple-900/10 rounded-2xl p-8 md:p-12">
      <div className="max-w-3xl mx-auto text-center">
        <h3 className="text-2xl font-bold text-gray-900 dark:text-gray-100 mb-4">
          Why Start with a Prototype?
        </h3>
        <p className="text-gray-600 dark:text-gray-300 mb-8">
          Prototyping is the bridge between your vision and a successful product launch. 
          It helps validate assumptions and reduce development risks.
        </p>
        <div className="grid md:grid-cols-2 gap-4">
          {benefits.map((benefit, index) => (
            <div 
              key={index}
              className="flex items-center bg-white dark:bg-gray-800 rounded-lg p-4"
            >
              <CheckCircle2 className="h-5 w-5 text-green-500 mr-3 flex-shrink-0" />
              <span className="text-gray-700 dark:text-gray-200">{benefit}</span>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
};

export default WhyPrototype;
