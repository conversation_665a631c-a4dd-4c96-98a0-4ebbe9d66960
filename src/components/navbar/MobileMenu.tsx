
import { But<PERSON> } from "@/components/ui/button";
import { Link } from "react-router-dom";
import { cn } from "@/lib/utils";
import { User } from "@/types/auth";
import { Rocket } from "lucide-react";

interface MobileMenuProps {
  isOpen: boolean;
  isHomePage: boolean;
  isAdminPage: boolean;
  isAuthenticated: boolean;
  isAdmin: boolean;
  user: User | null;
  scrollToSection: (sectionId: string) => void;
  handleStartProject: () => void;
  handleLogout: () => void;
}

const MobileMenu = ({
  isOpen,
  isHomePage,
  isAdminPage,
  isAuthenticated,
  isAdmin,
  user,
  scrollToSection,
  handleStartProject,
  handleLogout,
}: MobileMenuProps) => {
  return (
    <div
      className={cn(
        "md:hidden fixed inset-x-0 top-14 bg-white dark:bg-gray-900 shadow-lg transition-transform duration-200 ease-in-out transform",
        isOpen ? "translate-y-0" : "-translate-y-full"
      )}
    >
      <div className="p-4 space-y-3">
        {isHomePage && (
          <>
            <Button 
              variant="ghost"
              className="w-full justify-start"
              onClick={() => scrollToSection('about')}
            >
              About
            </Button>
            <Button 
              variant="ghost"
              className="w-full justify-start"
              onClick={() => scrollToSection('benefits')}
            >
              Benefits
            </Button>
            <Button 
              variant="ghost"
              className="w-full justify-start"
              onClick={() => scrollToSection('how-it-works')}
            >
              How it Works
            </Button>
            <Button 
              variant="ghost"
              className="w-full justify-start"
              onClick={() => scrollToSection('process')}
            >
              Process
            </Button>
            <Button 
              variant="ghost"
              className="w-full justify-start"
              onClick={() => scrollToSection('faq')}
            >
              FAQ
            </Button>
          </>
        )}

        {!isAuthenticated ? (
          <>
            <Button 
              onClick={handleStartProject}
              className="w-full bg-primary text-white hover:bg-primary/90 flex items-center justify-center gap-2"
            >
              <Rocket className="w-4 h-4" />
              Validate Your Idea
            </Button>
          </>
        ) : (
          <>
            <Link to={isAdmin ? "/admin/dashboard" : "/dashboard"} className="block">
              <Button variant="ghost" className="w-full justify-start">
                Dashboard
              </Button>
            </Link>
            {isAdmin && isAdminPage && (
              <>
                <Link to="/admin/roadmaps" className="block">
                  <Button variant="ghost" className="w-full justify-start">
                    Roadmaps
                  </Button>
                </Link>
                <Link to="/admin/users" className="block">
                  <Button variant="ghost" className="w-full justify-start">
                    Manage Users
                  </Button>
                </Link>
                <Link to="/admin/ai-settings" className="block">
                  <Button variant="ghost" className="w-full justify-start">
                    AI Settings
                  </Button>
                </Link>
                <Link to="/admin/plan-settings" className="block">
                  <Button variant="ghost" className="w-full justify-start">
                    Plan Settings
                  </Button>
                </Link>
              </>
            )}
            <Link to={isAdmin ? "/admin/profile" : "/user/profile"} className="block">
              <Button variant="ghost" className="w-full justify-start">
                Profile
              </Button>
            </Link>
            <Button 
              onClick={handleLogout}
              variant="ghost" 
              className="w-full justify-start text-destructive hover:bg-destructive/10"
            >
              Sign out
            </Button>
          </>
        )}
      </div>
    </div>
  );
};

export default MobileMenu;
