
import { But<PERSON> } from "@/components/ui/button";
import { <PERSON> } from "react-router-dom";
import { User } from "@/types/auth";
import { cn } from "@/lib/utils";
import { Rocket } from "lucide-react";

interface DesktopMenuProps {
  isHomePage: boolean;
  isAdminPage: boolean;
  isAuthenticated: boolean;
  isAdmin: boolean;
  user: User | null;
  scrollToSection: (sectionId: string) => void;
  handleStartProject: () => void;
  handleLogout: () => void;
}

const NavButton = ({ 
  children, 
  onClick, 
  variant = "ghost",
  className = ""
}: { 
  children: React.ReactNode; 
  onClick?: () => void;
  variant?: "ghost" | "default";
  className?: string;
}) => (
  <Button
    variant={variant}
    onClick={onClick}
    className={cn(
      "relative px-4 py-2 text-sm font-medium transition-all duration-200",
      "before:absolute before:inset-x-0 before:bottom-0 before:h-0.5",
      "before:origin-left before:scale-x-0 before:bg-primary before:transition-transform",
      "hover:before:scale-x-100",
      className
    )}
  >
    {children}
  </Button>
);

const DesktopMenu = ({
  isHomePage,
  isAdminPage,
  isAuthenticated,
  isAdmin,
  user,
  scrollToSection,
  handleStartProject,
  handleLogout,
}: DesktopMenuProps) => {
  return (
    <div className="hidden md:flex items-center gap-1">
      {isHomePage && !isAdmin && (
        <>
          <NavButton onClick={() => scrollToSection('about')}>
            About
          </NavButton>
          <NavButton onClick={() => scrollToSection('benefits')}>
            Benefits
          </NavButton>
          <NavButton onClick={() => scrollToSection('how-it-works')}>
            How it Works
          </NavButton>
          <NavButton onClick={() => scrollToSection('process')}>
            Process
          </NavButton>
          <NavButton onClick={() => scrollToSection('faq')}>
            FAQ
          </NavButton>
        </>
      )}

      {isAdminPage && isAdmin && (
        <>
          <Link to="/admin/dashboard">
            <NavButton>Dashboard</NavButton>
          </Link>
          <Link to="/admin/roadmaps">
            <NavButton>Roadmaps</NavButton>
          </Link>
        </>
      )}

      {!isAuthenticated ? (
        <>
          {!isAdmin && (
            <Button 
              onClick={handleStartProject}
              className="bg-primary hover:bg-primary/90 text-white ml-2 shadow-md hover:shadow-lg transition-all duration-200"
            >
              <Rocket className="w-4 h-4 mr-2" />
              Validate Your Idea
            </Button>
          )}
        </>
      ) : (
        <>
          {!isAdminPage && (
            <Link to={isAdmin ? "/admin/dashboard" : "/dashboard"}>
              <NavButton>Dashboard</NavButton>
            </Link>
          )}
          <Button 
            onClick={handleLogout}
            variant="ghost"
            className="ml-2"
          >
            Sign out
          </Button>
        </>
      )}
    </div>
  );
};

export default DesktopMenu;
