import React, { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs";
import { 
  ExternalLink, 
  Code, 
  Download, 
  Smartphone, 
  Monitor,
  RefreshCw,
  Settings
} from "lucide-react";
import { Prototype } from "@/types";

interface PrototypePreviewProps {
  prototype: Prototype;
  onCustomize?: () => void;
}

export function PrototypePreview({ prototype, onCustomize }: PrototypePreviewProps) {
  const [viewMode, setViewMode] = useState<'desktop' | 'mobile'>('desktop');
  const [isRefreshing, setIsRefreshing] = useState(false);

  const handleRefresh = async () => {
    setIsRefreshing(true);
    // Simulate refresh delay
    await new Promise(resolve => setTimeout(resolve, 1000));
    setIsRefreshing(false);
  };

  const handleOpenInNewTab = () => {
    if (prototype.preview_url) {
      window.open(prototype.preview_url, '_blank');
    }
  };

  const handleViewSource = () => {
    if (prototype.github_repo_url) {
      window.open(prototype.github_repo_url, '_blank');
    }
  };

  const handleDownload = () => {
    // In a real implementation, this would trigger a download of the source code
    console.log('Download source code for prototype:', prototype.id);
  };

  return (
    <div className="space-y-6">
      {/* Preview Controls */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle className="flex items-center gap-2">
                <Monitor className="h-5 w-5" />
                {prototype.name} Preview
              </CardTitle>
              <CardDescription>
                Interactive preview of your generated prototype
              </CardDescription>
            </div>
            <Badge 
              variant={prototype.status === 'completed' ? 'default' : 'secondary'}
              className="capitalize"
            >
              {prototype.status}
            </Badge>
          </div>
        </CardHeader>
        <CardContent>
          <div className="flex flex-wrap items-center gap-2 mb-4">
            {/* View Mode Toggle */}
            <div className="flex border rounded-lg p-1">
              <Button
                variant={viewMode === 'desktop' ? 'default' : 'ghost'}
                size="sm"
                onClick={() => setViewMode('desktop')}
                className="h-8"
              >
                <Monitor className="h-4 w-4 mr-1" />
                Desktop
              </Button>
              <Button
                variant={viewMode === 'mobile' ? 'default' : 'ghost'}
                size="sm"
                onClick={() => setViewMode('mobile')}
                className="h-8"
              >
                <Smartphone className="h-4 w-4 mr-1" />
                Mobile
              </Button>
            </div>

            {/* Action Buttons */}
            <Button
              variant="outline"
              size="sm"
              onClick={handleRefresh}
              disabled={isRefreshing}
            >
              <RefreshCw className={`h-4 w-4 mr-1 ${isRefreshing ? 'animate-spin' : ''}`} />
              Refresh
            </Button>

            {onCustomize && (
              <Button
                variant="outline"
                size="sm"
                onClick={onCustomize}
              >
                <Settings className="h-4 w-4 mr-1" />
                Customize
              </Button>
            )}

            <Button
              variant="outline"
              size="sm"
              onClick={handleOpenInNewTab}
              disabled={!prototype.preview_url}
            >
              <ExternalLink className="h-4 w-4 mr-1" />
              Open in New Tab
            </Button>

            <Button
              variant="outline"
              size="sm"
              onClick={handleViewSource}
              disabled={!prototype.github_repo_url}
            >
              <Code className="h-4 w-4 mr-1" />
              View Source
            </Button>

            <Button
              variant="outline"
              size="sm"
              onClick={handleDownload}
            >
              <Download className="h-4 w-4 mr-1" />
              Download
            </Button>
          </div>

          {/* Preview Frame */}
          <div className="border rounded-lg overflow-hidden bg-white">
            {/* Browser Chrome */}
            <div className="bg-gray-100 p-2 flex items-center justify-between border-b">
              <div className="flex items-center space-x-2">
                <div className="w-3 h-3 rounded-full bg-red-500"></div>
                <div className="w-3 h-3 rounded-full bg-yellow-500"></div>
                <div className="w-3 h-3 rounded-full bg-green-500"></div>
              </div>
              <div className="flex-1 mx-4">
                <div className="bg-white rounded px-3 py-1 text-sm text-gray-600 text-center">
                  {prototype.preview_url || `${prototype.name.toLowerCase().replace(/\s+/g, '-')}.app`}
                </div>
              </div>
              <div className="w-16"></div>
            </div>

            {/* Preview Content */}
            <div className={`transition-all duration-300 ${
              viewMode === 'mobile' 
                ? 'max-w-sm mx-auto' 
                : 'w-full'
            }`}>
              {prototype.preview_url ? (
                <iframe 
                  src={prototype.preview_url} 
                  className={`border-none ${
                    viewMode === 'mobile' 
                      ? 'w-full h-[600px]' 
                      : 'w-full h-[700px]'
                  }`}
                  title={`${prototype.name} Preview`}
                />
              ) : (
                <div className="flex items-center justify-center h-[600px] bg-gray-50">
                  <div className="text-center">
                    <Monitor className="h-16 w-16 mx-auto mb-4 text-gray-400" />
                    <h3 className="text-lg font-medium text-gray-900 mb-2">
                      Preview Not Available
                    </h3>
                    <p className="text-gray-600 mb-4">
                      The prototype preview will be available once generation is complete.
                    </p>
                    {prototype.status === 'generating' && (
                      <div className="flex items-center justify-center gap-2 text-blue-600">
                        <RefreshCw className="h-4 w-4 animate-spin" />
                        <span>Generating prototype...</span>
                      </div>
                    )}
                  </div>
                </div>
              )}
            </div>
          </div>

          {/* Preview Info */}
          <div className="mt-4 grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
            <div className="flex items-center gap-2">
              <div className="w-2 h-2 bg-green-500 rounded-full"></div>
              <span>React + TypeScript</span>
            </div>
            <div className="flex items-center gap-2">
              <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
              <span>Tailwind CSS</span>
            </div>
            <div className="flex items-center gap-2">
              <div className="w-2 h-2 bg-purple-500 rounded-full"></div>
              <span>Responsive Design</span>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Prototype Details */}
      <Card>
        <CardHeader>
          <CardTitle>Prototype Details</CardTitle>
          <CardDescription>
            Technical information about your generated prototype
          </CardDescription>
        </CardHeader>
        <CardContent>
          <Tabs defaultValue="overview" className="w-full">
            <TabsList className="grid w-full grid-cols-3">
              <TabsTrigger value="overview">Overview</TabsTrigger>
              <TabsTrigger value="features">Features</TabsTrigger>
              <TabsTrigger value="tech">Tech Stack</TabsTrigger>
            </TabsList>
            
            <TabsContent value="overview" className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <h4 className="font-medium mb-2">App Name</h4>
                  <p className="text-sm text-muted-foreground">{prototype.name}</p>
                </div>
                <div>
                  <h4 className="font-medium mb-2">Status</h4>
                  <Badge variant="outline" className="capitalize">
                    {prototype.status}
                  </Badge>
                </div>
                <div>
                  <h4 className="font-medium mb-2">Created</h4>
                  <p className="text-sm text-muted-foreground">
                    {new Date(prototype.created_at).toLocaleDateString()}
                  </p>
                </div>
                <div>
                  <h4 className="font-medium mb-2">Last Updated</h4>
                  <p className="text-sm text-muted-foreground">
                    {new Date(prototype.updated_at).toLocaleDateString()}
                  </p>
                </div>
              </div>
              
              {prototype.description && (
                <div>
                  <h4 className="font-medium mb-2">Description</h4>
                  <p className="text-sm text-muted-foreground">{prototype.description}</p>
                </div>
              )}
            </TabsContent>
            
            <TabsContent value="features" className="space-y-4">
              <div>
                <h4 className="font-medium mb-3">Included Features</h4>
                <div className="space-y-2">
                  {prototype.config.selected_features.core.length > 0 && (
                    <div>
                      <h5 className="text-sm font-medium text-blue-600 mb-1">Core Features</h5>
                      <div className="flex flex-wrap gap-1">
                        {prototype.config.selected_features.core.map((feature, index) => (
                          <Badge key={index} variant="outline" className="text-xs">
                            {feature}
                          </Badge>
                        ))}
                      </div>
                    </div>
                  )}
                  
                  {prototype.config.selected_features.mustHave.length > 0 && (
                    <div>
                      <h5 className="text-sm font-medium text-green-600 mb-1">Must Have Features</h5>
                      <div className="flex flex-wrap gap-1">
                        {prototype.config.selected_features.mustHave.map((feature, index) => (
                          <Badge key={index} variant="outline" className="text-xs">
                            {feature}
                          </Badge>
                        ))}
                      </div>
                    </div>
                  )}
                  
                  {prototype.config.selected_features.shouldHave.length > 0 && (
                    <div>
                      <h5 className="text-sm font-medium text-orange-600 mb-1">Should Have Features</h5>
                      <div className="flex flex-wrap gap-1">
                        {prototype.config.selected_features.shouldHave.map((feature, index) => (
                          <Badge key={index} variant="outline" className="text-xs">
                            {feature}
                          </Badge>
                        ))}
                      </div>
                    </div>
                  )}
                </div>
              </div>
            </TabsContent>
            
            <TabsContent value="tech" className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <h4 className="font-medium mb-2">Frontend</h4>
                  <ul className="text-sm text-muted-foreground space-y-1">
                    <li>• React 18</li>
                    <li>• TypeScript</li>
                    <li>• Vite</li>
                    <li>• React Router</li>
                  </ul>
                </div>
                <div>
                  <h4 className="font-medium mb-2">Styling</h4>
                  <ul className="text-sm text-muted-foreground space-y-1">
                    <li>• Tailwind CSS</li>
                    <li>• Radix UI Components</li>
                    <li>• Custom Theme System</li>
                    <li>• Responsive Design</li>
                  </ul>
                </div>
                <div>
                  <h4 className="font-medium mb-2">Data & State</h4>
                  <ul className="text-sm text-muted-foreground space-y-1">
                    <li>• Mock API Services</li>
                    <li>• Context API</li>
                    <li>• Local Storage</li>
                    <li>• Form Handling</li>
                  </ul>
                </div>
                <div>
                  <h4 className="font-medium mb-2">Development</h4>
                  <ul className="text-sm text-muted-foreground space-y-1">
                    <li>• Hot Module Replacement</li>
                    <li>• TypeScript Checking</li>
                    <li>• ESLint Configuration</li>
                    <li>• Build Optimization</li>
                  </ul>
                </div>
              </div>
            </TabsContent>
          </Tabs>
        </CardContent>
      </Card>
    </div>
  );
}
