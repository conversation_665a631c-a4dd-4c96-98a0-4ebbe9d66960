import { Badge } from "@/components/ui/badge";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs";
import { supabase } from "@/integrations/supabase/client";
import { Prototype } from "@/types";
import {
    Code,
    Download,
    ExternalLink,
    FileText,
    Folder,
    Monitor,
    Play,
    RefreshCw,
    Settings,
    Smartphone
} from "lucide-react";
import { useEffect, useState } from 'react';

interface PrototypeFile {
  id: string;
  file_path: string;
  file_content?: string;
  file_type: string;
  template_used: string;
  storage_path?: string;
  file_size?: number;
  public_url?: string;
  created_at: string;
}

interface PrototypePreviewProps {
  prototype: Prototype;
  onCustomize?: () => void;
  onDownload?: () => void;
}

interface InteractivePrototypeDemoProps {
  prototype: Prototype;
  viewMode: 'desktop' | 'mobile';
}

interface GeneratedPrototypeViewerProps {
  prototype: Prototype;
  viewMode: 'desktop' | 'mobile';
}

function getAppIcon(appName: string): string {
  if (appName.toLowerCase().includes('legal')) return '⚖️';
  if (appName.toLowerCase().includes('meal') || appName.toLowerCase().includes('food')) return '🍽️';
  if (appName.toLowerCase().includes('health')) return '🏥';
  if (appName.toLowerCase().includes('finance')) return '💰';
  if (appName.toLowerCase().includes('education')) return '📚';
  if (appName.toLowerCase().includes('travel')) return '✈️';
  if (appName.toLowerCase().includes('fitness')) return '💪';
  return '🚀';
}

function InteractivePrototypeDemo({ prototype, viewMode }: InteractivePrototypeDemoProps) {
  const [currentView, setCurrentView] = useState<'dashboard' | 'recipes' | 'plans'>('dashboard');
  const [selectedRecipe, setSelectedRecipe] = useState<number | null>(null);
  const [mealPlanGenerated, setMealPlanGenerated] = useState(false);

  const recipes = [
    { id: 1, name: 'Mediterranean Quinoa Bowl', time: '25 min', calories: 420, rating: 4.8, difficulty: 'Easy', image: '🥗' },
    { id: 2, name: 'Spicy Thai Curry', time: '30 min', calories: 380, rating: 4.6, difficulty: 'Medium', image: '🍛' },
    { id: 3, name: 'Grilled Salmon & Veggies', time: '20 min', calories: 450, rating: 4.9, difficulty: 'Easy', image: '🐟' },
    { id: 4, name: 'Chicken Tikka Masala', time: '35 min', calories: 520, rating: 4.7, difficulty: 'Medium', image: '🍗' },
    { id: 5, name: 'Vegetarian Buddha Bowl', time: '15 min', calories: 350, rating: 4.5, difficulty: 'Easy', image: '🥙' },
    { id: 6, name: 'Beef Stir Fry', time: '18 min', calories: 480, rating: 4.6, difficulty: 'Easy', image: '🥩' }
  ];

  const handleRecipeClick = (recipeId: number) => {
    setSelectedRecipe(recipeId);
    setTimeout(() => setSelectedRecipe(null), 2000);
  };

  const handleGeneratePlan = () => {
    setMealPlanGenerated(true);
    setTimeout(() => setMealPlanGenerated(false), 3000);
  };

  return (
    <div className={`bg-gradient-to-br from-blue-50 to-green-50 p-4 rounded-lg ${viewMode === 'mobile' ? 'max-w-sm mx-auto' : ''}`}>
      <div className="bg-white rounded-lg shadow-lg overflow-hidden">
        {/* Header */}
        <div className="bg-white border-b p-4">
          <div className="flex items-center justify-between">
            <h1 className="text-xl font-bold" style={{ color: prototype.config.colors.primary }}>
              {getAppIcon(prototype.config.app_name)} {prototype.config.app_name.split(' ').slice(0, 3).join(' ')}
            </h1>
            <div className="flex space-x-2">
              <button
                onClick={() => setCurrentView('dashboard')}
                className={`px-3 py-1 rounded text-sm transition-all ${currentView === 'dashboard' ? 'text-white' : 'text-gray-700'}`}
                style={{ backgroundColor: currentView === 'dashboard' ? prototype.config.colors.primary : '#f3f4f6' }}
              >
                Dashboard
              </button>
              <button
                onClick={() => setCurrentView('recipes')}
                className={`px-3 py-1 rounded text-sm transition-all ${currentView === 'recipes' ? 'text-white' : 'text-gray-700'}`}
                style={{ backgroundColor: currentView === 'recipes' ? prototype.config.colors.primary : '#f3f4f6' }}
              >
                Recipes
              </button>
              <button
                onClick={() => setCurrentView('plans')}
                className={`px-3 py-1 rounded text-sm transition-all ${currentView === 'plans' ? 'text-white' : 'text-gray-700'}`}
                style={{ backgroundColor: currentView === 'plans' ? prototype.config.colors.primary : '#f3f4f6' }}
              >
                Plans
              </button>
            </div>
          </div>
        </div>

        {/* Content */}
        <div className="p-4 h-96 overflow-y-auto">
          {currentView === 'dashboard' && (
            <div className="space-y-4">
              <div className="grid grid-cols-3 gap-3">
                <div className="bg-blue-50 p-3 rounded-lg text-center">
                  <div className="text-lg font-bold text-blue-600">42</div>
                  <div className="text-xs text-blue-800">Recipes</div>
                </div>
                <div className="bg-green-50 p-3 rounded-lg text-center">
                  <div className="text-lg font-bold text-green-600">7</div>
                  <div className="text-xs text-green-800">This Week</div>
                </div>
                <div className="bg-orange-50 p-3 rounded-lg text-center">
                  <div className="text-lg font-bold text-orange-600">1,850</div>
                  <div className="text-xs text-orange-800">Avg Cal</div>
                </div>
              </div>

              <div className="bg-gray-50 p-4 rounded-lg">
                <h3 className="font-semibold mb-3">🤖 AI Recommendations</h3>
                <div className="space-y-2">
                  {recipes.slice(0, 3).map(recipe => (
                    <div
                      key={recipe.id}
                      onClick={() => handleRecipeClick(recipe.id)}
                      className={`bg-white p-3 rounded cursor-pointer transition-all ${
                        selectedRecipe === recipe.id ? 'ring-2 ring-blue-500 scale-105' : 'hover:shadow-md'
                      }`}
                    >
                      <div className="flex items-center justify-between">
                        <div>
                          <div className="font-medium text-sm">{recipe.image} {recipe.name}</div>
                          <div className="text-xs text-gray-600">{recipe.time} • {recipe.calories} cal</div>
                        </div>
                        <div className="text-yellow-400 text-xs">★ {recipe.rating}</div>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          )}

          {currentView === 'recipes' && (
            <div className="space-y-3">
              <h3 className="font-semibold">All Recipes ({recipes.length})</h3>
              <div className="grid grid-cols-1 gap-3">
                {recipes.map(recipe => (
                  <div
                    key={recipe.id}
                    onClick={() => handleRecipeClick(recipe.id)}
                    className={`bg-gray-50 p-3 rounded-lg cursor-pointer transition-all ${
                      selectedRecipe === recipe.id ? 'ring-2 ring-blue-500 scale-105' : 'hover:bg-gray-100'
                    }`}
                  >
                    <div className="flex items-center justify-between">
                      <div>
                        <div className="font-medium text-sm">{recipe.image} {recipe.name}</div>
                        <div className="text-xs text-gray-600">{recipe.difficulty} • {recipe.time} • {recipe.calories} cal</div>
                      </div>
                      <div className="text-yellow-400 text-sm">★ {recipe.rating}</div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          )}

          {currentView === 'plans' && (
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <h3 className="font-semibold">Weekly Meal Plan</h3>
                <button
                  onClick={handleGeneratePlan}
                  className="px-3 py-1 text-white rounded text-sm transition-all hover:scale-105"
                  style={{ backgroundColor: prototype.config.colors.secondary }}
                >
                  {mealPlanGenerated ? '✨ Generated!' : '🎯 Generate'}
                </button>
              </div>

              <div className="space-y-2">
                {['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday', 'Sunday'].map((day, index) => (
                  <div
                    key={day}
                    className={`bg-gray-50 p-3 rounded-lg transition-all ${
                      mealPlanGenerated ? 'animate-pulse bg-green-50' : ''
                    }`}
                  >
                    <div className="font-medium text-sm mb-1">{day}</div>
                    <div className="text-xs text-gray-600 space-y-1">
                      <div>🌅 {recipes[index % recipes.length].name}</div>
                      <div>☀️ {recipes[(index + 1) % recipes.length].name}</div>
                      <div>🌙 {recipes[(index + 2) % recipes.length].name}</div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          )}
        </div>

        {/* Footer */}
        <div className="bg-gray-50 p-3 text-center">
          <div className="text-xs text-gray-500">
            ✨ Generated by Incepta • Powered by AI
          </div>
        </div>
      </div>
    </div>
  );
}

function GeneratedPrototypeViewer({ prototype, viewMode }: GeneratedPrototypeViewerProps) {
  const [generatedFiles, setGeneratedFiles] = useState<any[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [activeView, setActiveView] = useState('app');

  useEffect(() => {
    fetchGeneratedFiles();
  }, [prototype.id]);

  const fetchGeneratedFiles = async () => {
    try {
      if (!supabase) {
        console.error('Supabase client not available');
        return;
      }

      const { data, error } = await supabase
        .from('prototype_files')
        .select('*')
        .eq('prototype_id', prototype.id)
        .order('created_at');

      if (error) throw error;
      setGeneratedFiles(data || []);
    } catch (error) {
      console.error('Error fetching generated files:', error);
    } finally {
      setIsLoading(false);
    }
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-96">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600">Loading generated prototype...</p>
        </div>
      </div>
    );
  }

  // Show a working demo even if files aren't generated yet
  if (generatedFiles.length === 0) {
    return (
      <FeatureBasedPrototypeDemo
        prototype={prototype}
        viewMode={viewMode}
        message="Generating files in progress - showing feature-based preview"
      />
    );
  }

  // Find the main app component or create a demo based on features
  const mainComponent = generatedFiles.find(f =>
    f.file_path.includes('App.tsx') ||
    f.file_path.includes('Dashboard.tsx') ||
    f.file_path.includes('Main')
  );

  return (
    <div className={`bg-gradient-to-br from-blue-50 to-green-50 p-4 rounded-lg ${viewMode === 'mobile' ? 'max-w-sm mx-auto' : ''}`}>
      <div className="bg-white rounded-lg shadow-lg overflow-hidden">
        {/* Header */}
        <div className="bg-white border-b p-4">
          <div className="flex items-center justify-between">
            <h1 className="text-xl font-bold" style={{ color: prototype.config.colors.primary }}>
              {getAppIcon(prototype.config.app_name)} {prototype.name.split(' ').slice(0, 3).join(' ')}
            </h1>
            <div className="flex space-x-2">
              <button
                onClick={() => setActiveView('app')}
                className={`px-3 py-1 rounded text-sm transition-all ${activeView === 'app' ? 'text-white' : 'text-gray-700'}`}
                style={{ backgroundColor: activeView === 'app' ? prototype.config.colors.primary : '#f3f4f6' }}
              >
                Live App
              </button>
              <button
                onClick={() => setActiveView('code')}
                className={`px-3 py-1 rounded text-sm transition-all ${activeView === 'code' ? 'text-white' : 'text-gray-700'}`}
                style={{ backgroundColor: activeView === 'code' ? prototype.config.colors.primary : '#f3f4f6' }}
              >
                Generated Code
              </button>
            </div>
          </div>
        </div>

        {/* Content */}
        <div className="p-4 h-96 overflow-y-auto">
          {activeView === 'app' ? (
            <RealPrototypeRenderer
              prototype={prototype}
              generatedFiles={generatedFiles}
              viewMode={viewMode}
            />
          ) : (
            <CodeViewer generatedFiles={generatedFiles} />
          )}
        </div>

        {/* Footer */}
        <div className="bg-gray-50 p-3 text-center">
          <div className="text-xs text-gray-500">
            ✨ Generated by Claude AI • {generatedFiles.length} files created
          </div>
        </div>
      </div>
    </div>
  );
}

function RealPrototypeRenderer({ prototype, generatedFiles, viewMode }: any) {
  // This would render the actual generated React components
  // For now, let's create a realistic demo based on the actual features
  const features = prototype.config.selected_features;

  return (
    <div className="space-y-4">
      <div className="bg-blue-50 p-4 rounded-lg">
        <h3 className="font-semibold text-blue-800 mb-2">🤖 AI-Generated Prototype</h3>
        <p className="text-sm text-blue-700">
          This prototype was generated using Claude AI based on your validated features.
          {generatedFiles.length} files were created including components, services, and styling.
        </p>
      </div>

      <div className="grid grid-cols-1 gap-3">
        <div className="bg-white p-4 rounded-lg border">
          <h4 className="font-medium mb-2">Generated Features:</h4>
          <div className="space-y-2 text-sm">
            {generatedFiles.slice(0, 5).map((file: any, index: number) => (
              <div key={index} className="flex items-center gap-2">
                <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                <span>{file.file_path.split('/').pop()}</span>
                <span className="text-gray-500">({file.file_type})</span>
              </div>
            ))}
            {generatedFiles.length > 5 && (
              <div className="text-gray-500">+ {generatedFiles.length - 5} more files</div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
}

function CodeViewer({ generatedFiles }: any) {
  const [selectedFile, setSelectedFile] = useState(0);

  return (
    <div className="space-y-4">
      <div className="flex flex-wrap gap-2">
        {generatedFiles.slice(0, 6).map((file: any, index: number) => (
          <button
            key={index}
            onClick={() => setSelectedFile(index)}
            className={`px-3 py-1 rounded text-xs ${
              selectedFile === index
                ? 'bg-blue-600 text-white'
                : 'bg-gray-200 text-gray-700 hover:bg-gray-300'
            }`}
          >
            {file.file_path.split('/').pop()}
          </button>
        ))}
      </div>

      {generatedFiles[selectedFile] && (
        <div className="bg-gray-900 text-green-400 p-4 rounded-lg text-xs font-mono overflow-auto max-h-64">
          <div className="text-gray-400 mb-2">
            {generatedFiles[selectedFile].file_path}
          </div>
          <pre className="whitespace-pre-wrap">
            {generatedFiles[selectedFile].file_content || '// Generated code will appear here'}
          </pre>
        </div>
      )}
    </div>
  );
}

function FeatureBasedPrototypeDemo({ prototype, viewMode, message }: any) {
  return (
    <div className={`bg-gradient-to-br from-blue-50 to-green-50 p-4 rounded-lg ${viewMode === 'mobile' ? 'max-w-sm mx-auto' : ''}`}>
      <div className="bg-white rounded-lg shadow-lg overflow-hidden">
        {/* Header */}
        <div className="bg-white border-b p-4">
          <div className="flex items-center justify-between">
            <h1 className="text-xl font-bold" style={{ color: prototype.config.colors.primary }}>
              {getAppIcon(prototype.config.app_name)} {prototype.name.split(' ').slice(0, 3).join(' ')}
            </h1>
            <div className="text-xs text-gray-500">
              Live Preview
            </div>
          </div>
        </div>

        {/* Content */}
        <div className="p-4 h-96 overflow-y-auto">
          <div className="space-y-4">
            {message && (
              <div className="bg-blue-50 p-3 rounded-lg">
                <div className="text-sm text-blue-700">{message}</div>
              </div>
            )}

            <InteractivePrototypeDemo prototype={prototype} viewMode={viewMode} />
          </div>
        </div>

        {/* Footer */}
        <div className="bg-gray-50 p-3 text-center">
          <div className="text-xs text-gray-500">
            ✨ Feature-based prototype preview
          </div>
        </div>
      </div>
    </div>
  );
}

export function PrototypePreview({ prototype, onCustomize, onDownload }: PrototypePreviewProps) {
  const [viewMode, setViewMode] = useState<'desktop' | 'mobile'>('desktop');
  const [isRefreshing, setIsRefreshing] = useState(false);
  const [files, setFiles] = useState<PrototypeFile[]>([]);
  const [selectedFile, setSelectedFile] = useState<PrototypeFile | null>(null);
  const [activeTab, setActiveTab] = useState('preview');

  useEffect(() => {
    if (prototype?.id) {
      fetchFiles();
      setupFileSubscription();
    }
  }, [prototype?.id]);

  const fetchFiles = async () => {
    try {
      const { data, error } = await supabase
        .from('prototype_files')
        .select('*')
        .eq('prototype_id', prototype.id)
        .order('created_at', { ascending: true });

      if (error) throw error;

      setFiles(data || []);
      if (data && data.length > 0 && !selectedFile) {
        // Load content for the first file
        const firstFile = data[0];
        await loadFileContent(firstFile);
      }
    } catch (error) {
      console.error('Error fetching files:', error);
    }
  };

  const loadFileContent = async (file: PrototypeFile) => {
    if (file.file_content || !file.storage_path) {
      setSelectedFile(file);
      return;
    }

    try {
      // Download file content from storage
      const { data: fileData, error } = await supabase.storage
        .from('prototype-files')
        .download(file.storage_path);

      if (error) {
        console.error('Error loading file content:', error);
        setSelectedFile({ ...file, file_content: '// Error loading file content' });
        return;
      }

      const content = await fileData.text();
      const fileWithContent = { ...file, file_content: content };
      setSelectedFile(fileWithContent);

      // Update the files array with the loaded content
      setFiles(prev => prev.map(f => f.id === file.id ? fileWithContent : f));

    } catch (error) {
      console.error('Error loading file content:', error);
      setSelectedFile({ ...file, file_content: '// Error loading file content' });
    }
  };

  const setupFileSubscription = () => {
    const channel = supabase
      .channel('prototype-files')
      .on(
        'postgres_changes',
        {
          event: 'INSERT',
          schema: 'public',
          table: 'prototype_files',
          filter: `prototype_id=eq.${prototype.id}`
        },
        (payload) => {
          console.log('New file generated:', payload.new);
          setFiles(prev => [...prev, payload.new as PrototypeFile]);
        }
      )
      .subscribe();

    return () => {
      supabase.removeChannel(channel);
    };
  };

  const handleRefresh = async () => {
    setIsRefreshing(true);
    await fetchFiles();
    setIsRefreshing(false);
  };

  const handleOpenInNewTab = () => {
    if (prototype.preview_url) {
      window.open(prototype.preview_url, '_blank');
    }
  };

  const handleViewSource = () => {
    if (prototype.github_repo_url) {
      window.open(prototype.github_repo_url, '_blank');
    }
  };

  const handleDownload = () => {
    if (onDownload) {
      onDownload();
    } else {
      console.log('Download source code for prototype:', prototype.id);
    }
  };

  return (
    <div className="space-y-6">
      {/* Preview Controls */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle className="flex items-center gap-2">
                <Monitor className="h-5 w-5" />
                {prototype.name} Preview
              </CardTitle>
              <CardDescription>
                Interactive preview of your generated prototype
              </CardDescription>
            </div>
            <Badge 
              variant={prototype.status === 'completed' ? 'default' : 'secondary'}
              className="capitalize"
            >
              {prototype.status}
            </Badge>
          </div>
        </CardHeader>
        <CardContent>
          <div className="flex flex-wrap items-center gap-2 mb-4">
            {/* View Mode Toggle */}
            <div className="flex border rounded-lg p-1">
              <Button
                variant={viewMode === 'desktop' ? 'default' : 'ghost'}
                size="sm"
                onClick={() => setViewMode('desktop')}
                className="h-8"
              >
                <Monitor className="h-4 w-4 mr-1" />
                Desktop
              </Button>
              <Button
                variant={viewMode === 'mobile' ? 'default' : 'ghost'}
                size="sm"
                onClick={() => setViewMode('mobile')}
                className="h-8"
              >
                <Smartphone className="h-4 w-4 mr-1" />
                Mobile
              </Button>
            </div>

            {/* Action Buttons */}
            <Button
              variant="outline"
              size="sm"
              onClick={handleRefresh}
              disabled={isRefreshing}
            >
              <RefreshCw className={`h-4 w-4 mr-1 ${isRefreshing ? 'animate-spin' : ''}`} />
              Refresh
            </Button>

            {onCustomize && (
              <Button
                variant="outline"
                size="sm"
                onClick={onCustomize}
              >
                <Settings className="h-4 w-4 mr-1" />
                Customize
              </Button>
            )}

            <Button
              variant="outline"
              size="sm"
              onClick={handleOpenInNewTab}
              disabled={!prototype.preview_url}
            >
              <ExternalLink className="h-4 w-4 mr-1" />
              Open in New Tab
            </Button>

            <Button
              variant="outline"
              size="sm"
              onClick={handleViewSource}
              disabled={!prototype.github_repo_url}
            >
              <Code className="h-4 w-4 mr-1" />
              View Source
            </Button>

            <Button
              variant="outline"
              size="sm"
              onClick={handleDownload}
            >
              <Download className="h-4 w-4 mr-1" />
              Download
            </Button>
          </div>

          {/* Preview Frame */}
          <div className="border rounded-lg overflow-hidden bg-white">
            {/* Browser Chrome */}
            <div className="bg-gray-100 p-2 flex items-center justify-between border-b">
              <div className="flex items-center space-x-2">
                <div className="w-3 h-3 rounded-full bg-red-500"></div>
                <div className="w-3 h-3 rounded-full bg-yellow-500"></div>
                <div className="w-3 h-3 rounded-full bg-green-500"></div>
              </div>
              <div className="flex-1 mx-4">
                <div className="bg-white rounded px-3 py-1 text-sm text-gray-600 text-center">
                  {prototype.preview_url || `${prototype.name.toLowerCase().replace(/\s+/g, '-')}.app`}
                </div>
              </div>
              <div className="w-16"></div>
            </div>

            {/* Preview Content */}
            <div className={`transition-all duration-300 ${
              viewMode === 'mobile' 
                ? 'max-w-sm mx-auto' 
                : 'w-full'
            }`}>
              {prototype.status === 'completed' ? (
                <GeneratedPrototypeViewer prototype={prototype} viewMode={viewMode} />
              ) : (
                <div className="flex items-center justify-center h-[600px] bg-gray-50">
                  <div className="text-center">
                    <Monitor className="h-16 w-16 mx-auto mb-4 text-gray-400" />
                    <h3 className="text-lg font-medium text-gray-900 mb-2">
                      Preview Not Available
                    </h3>
                    <p className="text-gray-600 mb-4">
                      The prototype preview will be available once generation is complete.
                    </p>
                    {prototype.status === 'generating' && (
                      <div className="flex items-center justify-center gap-2 text-blue-600">
                        <RefreshCw className="h-4 w-4 animate-spin" />
                        <span>Generating prototype...</span>
                      </div>
                    )}
                  </div>
                </div>
              )}
            </div>
          </div>

          {/* Preview Info */}
          <div className="mt-4 grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
            <div className="flex items-center gap-2">
              <div className="w-2 h-2 bg-green-500 rounded-full"></div>
              <span>React + TypeScript</span>
            </div>
            <div className="flex items-center gap-2">
              <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
              <span>Tailwind CSS</span>
            </div>
            <div className="flex items-center gap-2">
              <div className="w-2 h-2 bg-purple-500 rounded-full"></div>
              <span>Responsive Design</span>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Code and Preview Tabs */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Code className="h-5 w-5" />
            Generated Code ({files.length} files)
          </CardTitle>
          <CardDescription>
            View the generated source code and files
          </CardDescription>
        </CardHeader>
        <CardContent>
          <Tabs value={activeTab} onValueChange={setActiveTab}>
            <TabsList className="grid w-full grid-cols-2">
              <TabsTrigger value="preview">Live Preview</TabsTrigger>
              <TabsTrigger value="code">Source Code</TabsTrigger>
            </TabsList>

            <TabsContent value="preview" className="mt-4">
              <div className="border rounded-lg p-4 h-96 overflow-auto bg-gray-50">
                {files.length > 0 ? (
                  <div className="bg-white rounded-lg p-6">
                    <div className="bg-gradient-to-r from-blue-500 to-purple-600 text-white p-6 rounded-lg mb-4">
                      <h1 className="text-2xl font-bold">{prototype.name}</h1>
                      <p className="text-blue-100">{prototype.description}</p>
                    </div>

                    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                      {files.filter(f => f.file_type === 'typescript' && f.file_path.includes('components')).map((file, index) => (
                        <div key={file.id} className="bg-gray-50 p-4 rounded-lg border">
                          <h3 className="font-semibold text-sm mb-2">
                            {file.file_path.split('/').pop()?.replace('.tsx', '')}
                          </h3>
                          <p className="text-xs text-gray-600">Component {index + 1}</p>
                        </div>
                      ))}
                    </div>

                    <div className="mt-4 p-4 bg-blue-50 rounded-lg">
                      <p className="text-sm text-blue-800">
                        ✨ Live preview of your generated React application
                      </p>
                    </div>
                  </div>
                ) : (
                  <div className="flex items-center justify-center h-64">
                    <div className="text-center">
                      <Play className="w-12 h-12 text-gray-400 mx-auto mb-2" />
                      <p className="text-gray-600">No preview available yet</p>
                      <p className="text-sm text-gray-500">Generate more components to see a preview</p>
                    </div>
                  </div>
                )}
              </div>
            </TabsContent>

            <TabsContent value="code" className="mt-4">
              <div className="grid grid-cols-1 lg:grid-cols-3 gap-4">
                {/* File Explorer */}
                <div className="lg:col-span-1">
                  <div className="border rounded-lg p-4 h-96 overflow-auto">
                    <h3 className="font-semibold mb-3 flex items-center gap-2">
                      <Folder className="w-4 h-4" />
                      Project Structure
                    </h3>
                    {files.length > 0 ? (
                      <div className="space-y-1">
                        {files.map(file => (
                          <button
                            key={file.id}
                            onClick={() => loadFileContent(file)}
                            className={`flex items-center gap-2 w-full text-left p-2 rounded text-sm hover:bg-gray-100 ${
                              selectedFile?.id === file.id ? 'bg-blue-50 text-blue-700' : ''
                            }`}
                          >
                            <FileText className="w-4 h-4" />
                            {file.file_path}
                          </button>
                        ))}
                      </div>
                    ) : (
                      <p className="text-gray-500 text-sm">No files generated yet</p>
                    )}
                  </div>
                </div>

                {/* Code Editor */}
                <div className="lg:col-span-2">
                  {selectedFile ? (
                    <div className="border rounded-lg">
                      <div className="bg-gray-50 px-4 py-2 border-b flex items-center justify-between">
                        <span className="font-medium text-sm">{selectedFile.file_path}</span>
                        <Badge variant="outline">{selectedFile.file_type}</Badge>
                      </div>
                      <pre className="p-4 text-sm overflow-auto h-80 bg-gray-900 text-green-400">
                        <code>{selectedFile.file_content}</code>
                      </pre>
                    </div>
                  ) : (
                    <div className="border rounded-lg h-96 flex items-center justify-center text-gray-500">
                      Select a file to view its content
                    </div>
                  )}
                </div>
              </div>
            </TabsContent>
          </Tabs>
        </CardContent>
      </Card>

      {/* Prototype Details */}
      <Card>
        <CardHeader>
          <CardTitle>Prototype Details</CardTitle>
          <CardDescription>
            Technical information about your generated prototype
          </CardDescription>
        </CardHeader>
        <CardContent>
          <Tabs defaultValue="overview" className="w-full">
            <TabsList className="grid w-full grid-cols-3">
              <TabsTrigger value="overview">Overview</TabsTrigger>
              <TabsTrigger value="features">Features</TabsTrigger>
              <TabsTrigger value="tech">Tech Stack</TabsTrigger>
            </TabsList>
            
            <TabsContent value="overview" className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <h4 className="font-medium mb-2">App Name</h4>
                  <p className="text-sm text-muted-foreground">{prototype.name}</p>
                </div>
                <div>
                  <h4 className="font-medium mb-2">Status</h4>
                  <Badge variant="outline" className="capitalize">
                    {prototype.status}
                  </Badge>
                </div>
                <div>
                  <h4 className="font-medium mb-2">Created</h4>
                  <p className="text-sm text-muted-foreground">
                    {new Date(prototype.created_at).toLocaleDateString()}
                  </p>
                </div>
                <div>
                  <h4 className="font-medium mb-2">Last Updated</h4>
                  <p className="text-sm text-muted-foreground">
                    {new Date(prototype.updated_at).toLocaleDateString()}
                  </p>
                </div>
              </div>
              
              {prototype.description && (
                <div>
                  <h4 className="font-medium mb-2">Description</h4>
                  <p className="text-sm text-muted-foreground">{prototype.description}</p>
                </div>
              )}
            </TabsContent>
            
            <TabsContent value="features" className="space-y-4">
              <div>
                <h4 className="font-medium mb-3">Included Features</h4>
                <div className="space-y-2">
                  {prototype.config.selected_features.core.length > 0 && (
                    <div>
                      <h5 className="text-sm font-medium text-blue-600 mb-1">Core Features</h5>
                      <div className="flex flex-wrap gap-1">
                        {prototype.config.selected_features.core.map((feature, index) => (
                          <Badge key={index} variant="outline" className="text-xs">
                            {feature}
                          </Badge>
                        ))}
                      </div>
                    </div>
                  )}
                  
                  {prototype.config.selected_features.mustHave.length > 0 && (
                    <div>
                      <h5 className="text-sm font-medium text-green-600 mb-1">Must Have Features</h5>
                      <div className="flex flex-wrap gap-1">
                        {prototype.config.selected_features.mustHave.map((feature, index) => (
                          <Badge key={index} variant="outline" className="text-xs">
                            {feature}
                          </Badge>
                        ))}
                      </div>
                    </div>
                  )}
                  
                  {prototype.config.selected_features.shouldHave.length > 0 && (
                    <div>
                      <h5 className="text-sm font-medium text-orange-600 mb-1">Should Have Features</h5>
                      <div className="flex flex-wrap gap-1">
                        {prototype.config.selected_features.shouldHave.map((feature, index) => (
                          <Badge key={index} variant="outline" className="text-xs">
                            {feature}
                          </Badge>
                        ))}
                      </div>
                    </div>
                  )}
                </div>
              </div>
            </TabsContent>
            
            <TabsContent value="tech" className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <h4 className="font-medium mb-2">Frontend</h4>
                  <ul className="text-sm text-muted-foreground space-y-1">
                    <li>• React 18</li>
                    <li>• TypeScript</li>
                    <li>• Vite</li>
                    <li>• React Router</li>
                  </ul>
                </div>
                <div>
                  <h4 className="font-medium mb-2">Styling</h4>
                  <ul className="text-sm text-muted-foreground space-y-1">
                    <li>• Tailwind CSS</li>
                    <li>• Radix UI Components</li>
                    <li>• Custom Theme System</li>
                    <li>• Responsive Design</li>
                  </ul>
                </div>
                <div>
                  <h4 className="font-medium mb-2">Data & State</h4>
                  <ul className="text-sm text-muted-foreground space-y-1">
                    <li>• Mock API Services</li>
                    <li>• Context API</li>
                    <li>• Local Storage</li>
                    <li>• Form Handling</li>
                  </ul>
                </div>
                <div>
                  <h4 className="font-medium mb-2">Development</h4>
                  <ul className="text-sm text-muted-foreground space-y-1">
                    <li>• Hot Module Replacement</li>
                    <li>• TypeScript Checking</li>
                    <li>• ESLint Configuration</li>
                    <li>• Build Optimization</li>
                  </ul>
                </div>
              </div>
            </TabsContent>
          </Tabs>
        </CardContent>
      </Card>
    </div>
  );
}
