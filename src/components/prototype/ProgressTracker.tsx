import React, { useEffect, useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Progress } from "@/components/ui/progress";
import { Badge } from "@/components/ui/badge";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { 
  Clock, 
  CheckCircle, 
  AlertCircle, 
  Play, 
  Pause, 
  ExternalLink,
  Code,
  Zap,
  Settings,
  Globe
} from "lucide-react";
import { PrototypeTask, GenerationProgress } from "@/types";
import { supabase } from "@/integrations/supabase/client";

interface ProgressTrackerProps {
  prototypeId: string;
  onTaskUpdate?: (task: PrototypeTask) => void;
}

const TASK_TYPE_ICONS = {
  setup: Settings,
  component: Code,
  page: Globe,
  service: Zap,
  integration: Play,
  styling: Pause,
  deployment: ExternalLink
};

const STATUS_COLORS = {
  pending: 'bg-gray-200 text-gray-700',
  in_progress: 'bg-blue-200 text-blue-700',
  completed: 'bg-green-200 text-green-700',
  failed: 'bg-red-200 text-red-700',
  skipped: 'bg-yellow-200 text-yellow-700'
};

export function ProgressTracker({ prototypeId, onTaskUpdate }: ProgressTrackerProps) {
  const [tasks, setTasks] = useState<PrototypeTask[]>([]);
  const [progress, setProgress] = useState<GenerationProgress | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    fetchTasks();
    
    // Set up real-time subscription for task updates
    const subscription = supabase
      .from('prototype_tasks')
      .on('UPDATE', (payload) => {
        const updatedTask = payload.new as PrototypeTask;
        setTasks(prev => prev.map(task => 
          task.id === updatedTask.id ? updatedTask : task
        ));
        onTaskUpdate?.(updatedTask);
        calculateProgress();
      })
      .on('INSERT', (payload) => {
        const newTask = payload.new as PrototypeTask;
        setTasks(prev => [...prev, newTask]);
        calculateProgress();
      })
      .subscribe();

    return () => {
      supabase.removeSubscription(subscription);
    };
  }, [prototypeId]);

  const fetchTasks = async () => {
    try {
      const { data, error } = await supabase
        .from('prototype_tasks')
        .select('*')
        .eq('prototype_id', prototypeId)
        .order('order_index');

      if (error) throw error;
      
      setTasks(data || []);
      calculateProgress(data || []);
    } catch (error) {
      console.error('Error fetching tasks:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const calculateProgress = (taskList = tasks) => {
    const totalTasks = taskList.length;
    const completedTasks = taskList.filter(t => t.status === 'completed').length;
    const failedTasks = taskList.filter(t => t.status === 'failed').length;
    const currentTask = taskList.find(t => t.status === 'in_progress');
    
    const progressPercentage = totalTasks > 0 ? Math.round((completedTasks / totalTasks) * 100) : 0;
    
    // Estimate completion time based on remaining tasks
    const remainingTasks = taskList.filter(t => ['pending', 'in_progress'].includes(t.status));
    const estimatedMinutes = remainingTasks.reduce((sum, task) => sum + task.estimated_duration, 0);
    const estimatedCompletion = new Date(Date.now() + estimatedMinutes * 60000).toISOString();

    setProgress({
      prototype_id: prototypeId,
      total_tasks: totalTasks,
      completed_tasks: completedTasks,
      failed_tasks: failedTasks,
      current_task: currentTask,
      progress_percentage: progressPercentage,
      estimated_completion: estimatedCompletion
    });
  };

  const formatDuration = (minutes: number) => {
    if (minutes < 60) return `${minutes}m`;
    const hours = Math.floor(minutes / 60);
    const mins = minutes % 60;
    return `${hours}h ${mins}m`;
  };

  const formatTimeRemaining = (isoString: string) => {
    const now = new Date();
    const target = new Date(isoString);
    const diffMs = target.getTime() - now.getTime();
    const diffMins = Math.max(0, Math.floor(diffMs / (1000 * 60)));
    return formatDuration(diffMins);
  };

  if (isLoading) {
    return (
      <Card>
        <CardContent className="p-6">
          <div className="animate-pulse space-y-4">
            <div className="h-4 bg-gray-200 rounded w-3/4"></div>
            <div className="h-2 bg-gray-200 rounded"></div>
            <div className="space-y-2">
              {[1, 2, 3].map(i => (
                <div key={i} className="h-12 bg-gray-200 rounded"></div>
              ))}
            </div>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-6">
      {/* Overall Progress */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center justify-between">
            <span>Generation Progress</span>
            {progress && (
              <Badge variant="outline">
                {progress.completed_tasks}/{progress.total_tasks} tasks
              </Badge>
            )}
          </CardTitle>
          <CardDescription>
            Track the progress of your prototype generation
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          {progress && (
            <>
              <div className="space-y-2">
                <div className="flex justify-between text-sm">
                  <span>Overall Progress</span>
                  <span>{progress.progress_percentage}%</span>
                </div>
                <Progress value={progress.progress_percentage} className="h-2" />
              </div>

              <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
                <div className="flex items-center gap-2">
                  <CheckCircle className="h-4 w-4 text-green-500" />
                  <span>{progress.completed_tasks} Completed</span>
                </div>
                <div className="flex items-center gap-2">
                  <AlertCircle className="h-4 w-4 text-red-500" />
                  <span>{progress.failed_tasks} Failed</span>
                </div>
                <div className="flex items-center gap-2">
                  <Clock className="h-4 w-4 text-blue-500" />
                  <span>{formatTimeRemaining(progress.estimated_completion)} remaining</span>
                </div>
              </div>

              {progress.current_task && (
                <div className="p-3 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
                  <div className="flex items-center gap-2 mb-1">
                    <div className="w-2 h-2 bg-blue-500 rounded-full animate-pulse"></div>
                    <span className="font-medium text-sm">Currently Working On:</span>
                  </div>
                  <p className="text-sm text-muted-foreground">
                    {progress.current_task.title}
                  </p>
                </div>
              )}
            </>
          )}
        </CardContent>
      </Card>

      {/* Task List */}
      <Card>
        <CardHeader>
          <CardTitle>Task Details</CardTitle>
          <CardDescription>
            Detailed breakdown of all generation tasks
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-3">
            {tasks.map((task, index) => {
              const IconComponent = TASK_TYPE_ICONS[task.task_type] || Settings;
              const isActive = task.status === 'in_progress';
              const isCompleted = task.status === 'completed';
              const isFailed = task.status === 'failed';

              return (
                <div
                  key={task.id}
                  className={`flex items-center gap-3 p-3 rounded-lg border transition-all ${
                    isActive ? 'border-blue-200 bg-blue-50 dark:bg-blue-900/20' :
                    isCompleted ? 'border-green-200 bg-green-50 dark:bg-green-900/20' :
                    isFailed ? 'border-red-200 bg-red-50 dark:bg-red-900/20' :
                    'border-gray-200'
                  }`}
                >
                  <div className="flex-shrink-0">
                    <div className={`w-8 h-8 rounded-full flex items-center justify-center ${
                      isCompleted ? 'bg-green-500 text-white' :
                      isActive ? 'bg-blue-500 text-white' :
                      isFailed ? 'bg-red-500 text-white' :
                      'bg-gray-200 text-gray-600'
                    }`}>
                      {isCompleted ? (
                        <CheckCircle className="h-4 w-4" />
                      ) : isActive ? (
                        <div className="w-2 h-2 bg-white rounded-full animate-pulse" />
                      ) : isFailed ? (
                        <AlertCircle className="h-4 w-4" />
                      ) : (
                        <span className="text-xs font-medium">{index + 1}</span>
                      )}
                    </div>
                  </div>

                  <div className="flex-1 min-w-0">
                    <div className="flex items-center gap-2 mb-1">
                      <IconComponent className="h-4 w-4 text-muted-foreground" />
                      <h4 className="font-medium text-sm truncate">{task.title}</h4>
                      <Badge 
                        variant="outline" 
                        className={`text-xs ${STATUS_COLORS[task.status]}`}
                      >
                        {task.status.replace('_', ' ')}
                      </Badge>
                    </div>
                    
                    {task.description && (
                      <p className="text-xs text-muted-foreground mb-2">
                        {task.description}
                      </p>
                    )}

                    <div className="flex items-center gap-4 text-xs text-muted-foreground">
                      <span>Est. {formatDuration(task.estimated_duration)}</span>
                      {task.actual_duration && (
                        <span>Actual: {formatDuration(task.actual_duration)}</span>
                      )}
                      {task.feature_id && (
                        <Badge variant="outline" className="text-xs">
                          {task.feature_id}
                        </Badge>
                      )}
                    </div>

                    {task.error_message && (
                      <div className="mt-2 p-2 bg-red-100 dark:bg-red-900/20 rounded text-xs text-red-700 dark:text-red-300">
                        {task.error_message}
                      </div>
                    )}
                  </div>

                  {task.generated_files.length > 0 && (
                    <div className="flex-shrink-0">
                      <Badge variant="outline" className="text-xs">
                        {task.generated_files.length} files
                      </Badge>
                    </div>
                  )}
                </div>
              );
            })}
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
