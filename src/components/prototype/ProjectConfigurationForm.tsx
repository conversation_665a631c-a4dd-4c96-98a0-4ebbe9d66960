import { Badge } from "@/components/ui/badge";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { ProjectConfig } from "@/types";
import { Palette, Settings, Type, Zap } from "lucide-react";
import React, { useState } from 'react';

interface ProjectConfigurationFormProps {
  validationData: any;
  onSubmit: (config: ProjectConfig) => void;
  isLoading?: boolean;
}

const FONT_OPTIONS = [
  { name: 'Inter', value: 'Inter' },
  { name: 'Robot<PERSON>', value: 'Roboto' },
  { name: 'Open Sans', value: 'Open Sans' },
  { name: 'Poppins', value: 'Pop<PERSON><PERSON>' },
  { name: '<PERSON><PERSON><PERSON>', value: '<PERSON><PERSON><PERSON>' },
  { name: 'La<PERSON>', value: 'Lato' }
];

const COLOR_PRESETS = [
  { name: 'Modern Indigo', primary: '#6366f1', secondary: '#10b981' },
  { name: 'Tech Blue', primary: '#3b82f6', secondary: '#06b6d4' },
  { name: 'Startup Purple', primary: '#8b5cf6', secondary: '#f59e0b' },
  { name: 'Growth Green', primary: '#10b981', secondary: '#6366f1' },
  { name: 'Energy Orange', primary: '#f97316', secondary: '#ef4444' },
  { name: 'Professional Slate', primary: '#475569', secondary: '#0ea5e9' }
];

export function ProjectConfigurationForm({ validationData, onSubmit, isLoading }: ProjectConfigurationFormProps) {
  // Auto-select all features from validation data
  const getAllFeatures = () => {
    const features = { core: [], mustHave: [], shouldHave: [] };
    if (validationData?.projectConcept?.features) {
      Object.entries(validationData.projectConcept.features).forEach(([category, featureList]) => {
        if (category === 'core' || category === 'mustHave') {
          featureList.forEach((feature: any, index: number) => {
            features[category as keyof typeof features].push(`${category}-${index}`);
          });
        }
      });
    }
    return features;
  };

  const [config, setConfig] = useState<ProjectConfig>({
    app_name: validationData?.projectConcept?.title || validationData?.title || 'My App',
    colors: {
      primary: '#6366f1', // Modern indigo
      secondary: '#10b981', // Emerald green
      background: '#ffffff',
      text: '#1f2937'
    },
    typography: {
      fontFamily: 'Inter',
      headingFont: 'Inter'
    },
    selected_features: getAllFeatures()
  });

  const handleColorChange = (colorType: keyof ProjectConfig['colors'], value: string) => {
    setConfig(prev => ({
      ...prev,
      colors: {
        ...prev.colors,
        [colorType]: value
      }
    }));
  };

  const handleColorPreset = (preset: typeof COLOR_PRESETS[0]) => {
    setConfig(prev => ({
      ...prev,
      colors: {
        ...prev.colors,
        primary: preset.primary,
        secondary: preset.secondary
      }
    }));
  };

  const handleFeatureToggle = (category: keyof ProjectConfig['selected_features'], featureId: string) => {
    setConfig(prev => {
      const currentFeatures = prev.selected_features[category] || [];
      return {
        ...prev,
        selected_features: {
          ...prev.selected_features,
          [category]: currentFeatures.includes(featureId)
            ? currentFeatures.filter(id => id !== featureId)
            : [...currentFeatures, featureId]
        }
      };
    });
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    onSubmit(config);
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-6">
      {/* App Basic Info */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Settings className="h-5 w-5" />
            App Configuration
          </CardTitle>
          <CardDescription>
            Configure your app's basic information and branding
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div>
            <Label htmlFor="app_name">App Name</Label>
            <Input
              id="app_name"
              value={config.app_name}
              onChange={(e) => setConfig(prev => ({ ...prev, app_name: e.target.value }))}
              placeholder="Enter your app name"
              required
            />
          </div>

          <div>
            <Label htmlFor="description">Description (Optional)</Label>
            <Textarea
              id="description"
              value={validationData?.projectConcept?.overview || ''}
              placeholder="Brief description of your app"
              rows={3}
              readOnly
              className="bg-muted"
            />
          </div>


        </CardContent>
      </Card>

      {/* Color Scheme */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Palette className="h-5 w-5" />
            Color Scheme
          </CardTitle>
          <CardDescription>
            Choose colors that represent your brand
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div>
            <Label className="text-sm font-medium mb-3 block">Color Presets</Label>
            <div className="grid grid-cols-2 md:grid-cols-3 gap-3">
              {COLOR_PRESETS.map((preset) => (
                <button
                  key={preset.name}
                  type="button"
                  onClick={() => handleColorPreset(preset)}
                  className="p-3 border rounded-lg hover:border-primary transition-colors"
                >
                  <div className="flex items-center gap-2 mb-1">
                    <div 
                      className="w-4 h-4 rounded-full" 
                      style={{ backgroundColor: preset.primary }}
                    />
                    <div 
                      className="w-4 h-4 rounded-full" 
                      style={{ backgroundColor: preset.secondary }}
                    />
                  </div>
                  <p className="text-xs font-medium">{preset.name}</p>
                </button>
              ))}
            </div>
          </div>

          <div className="grid grid-cols-2 gap-4">
            <div>
              <Label htmlFor="primary_color">Primary Color</Label>
              <div className="flex gap-2">
                <Input
                  id="primary_color"
                  type="color"
                  value={config.colors.primary}
                  onChange={(e) => handleColorChange('primary', e.target.value)}
                  className="w-16 h-10 p-1"
                />
                <Input
                  value={config.colors.primary}
                  onChange={(e) => handleColorChange('primary', e.target.value)}
                  placeholder="#3b82f6"
                  className="flex-1"
                />
              </div>
            </div>

            <div>
              <Label htmlFor="secondary_color">Secondary Color</Label>
              <div className="flex gap-2">
                <Input
                  id="secondary_color"
                  type="color"
                  value={config.colors.secondary}
                  onChange={(e) => handleColorChange('secondary', e.target.value)}
                  className="w-16 h-10 p-1"
                />
                <Input
                  value={config.colors.secondary}
                  onChange={(e) => handleColorChange('secondary', e.target.value)}
                  placeholder="#10b981"
                  className="flex-1"
                />
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Typography */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Type className="h-5 w-5" />
            Typography
          </CardTitle>
          <CardDescription>
            Select fonts for your application
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <Label htmlFor="font_family">Body Font</Label>
              <select
                id="font_family"
                value={config.typography.fontFamily}
                onChange={(e) => setConfig(prev => ({
                  ...prev,
                  typography: { ...prev.typography, fontFamily: e.target.value }
                }))}
                className="w-full p-2 border rounded-md"
              >
                {FONT_OPTIONS.map(font => (
                  <option key={font.value} value={font.value}>
                    {font.name}
                  </option>
                ))}
              </select>
            </div>

            <div>
              <Label htmlFor="heading_font">Heading Font</Label>
              <select
                id="heading_font"
                value={config.typography.headingFont}
                onChange={(e) => setConfig(prev => ({
                  ...prev,
                  typography: { ...prev.typography, headingFont: e.target.value }
                }))}
                className="w-full p-2 border rounded-md"
              >
                {FONT_OPTIONS.map(font => (
                  <option key={font.value} value={font.value}>
                    {font.name}
                  </option>
                ))}
              </select>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Feature Selection */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Zap className="h-5 w-5" />
            Feature Selection
          </CardTitle>
          <CardDescription>
            Choose which features to include in your prototype
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          <div className="bg-green-50 dark:bg-green-900/20 p-4 rounded-lg">
            <div className="flex items-center gap-2 mb-2">
              <div className="w-2 h-2 bg-green-500 rounded-full"></div>
              <span className="font-medium text-sm text-green-800 dark:text-green-200">All Features Selected</span>
            </div>
            <p className="text-sm text-green-700 dark:text-green-300">
              All validated features from your idea will be included in the prototype.
              The AI will automatically implement the most important features first.
            </p>
          </div>

          {validationData?.projectConcept?.features && Object.entries(validationData.projectConcept.features)
            .filter(([category]) => category === 'core' || category === 'mustHave')
            .map(([category, features]) => (
            <div key={category}>
              <h4 className="font-medium mb-3 capitalize flex items-center gap-2">
                <span className="w-2 h-2 bg-blue-500 rounded-full"></span>
                {category === 'mustHave' ? 'Must Have' : 'Core'} Features ({features.length})
              </h4>
              <div className="space-y-2">
                {features.slice(0, 5).map((feature: any, index: number) => (
                  <div key={index} className="flex items-start space-x-3 p-3 bg-gray-50 dark:bg-gray-800 rounded-lg">
                    <div className="w-2 h-2 bg-green-500 rounded-full mt-2"></div>
                    <div className="flex-1">
                      <div className="text-sm font-medium">
                        {feature.description || feature.title || `Feature ${index + 1}`}
                      </div>
                      {feature.platforms && (
                        <div className="flex gap-1 mt-1">
                          {Object.entries(feature.platforms).map(([platform, enabled]) =>
                            enabled && (
                              <Badge key={platform} variant="outline" className="text-xs">
                                {platform}
                              </Badge>
                            )
                          )}
                        </div>
                      )}
                    </div>
                  </div>
                ))}
                {features.length > 5 && (
                  <div className="text-sm text-gray-500 text-center py-2">
                    + {features.length - 5} more features will be included
                  </div>
                )}
              </div>
            </div>
          ))}
        </CardContent>
      </Card>

      {/* Submit Button */}
      <div className="flex justify-end">
        <Button type="submit" size="lg" disabled={isLoading}>
          {isLoading ? 'Generating Prototype...' : 'Generate Prototype'}
        </Button>
      </div>
    </form>
  );
}
