import React, { useState } from 'react';
import { <PERSON>, Card<PERSON>ontent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Badge } from "@/components/ui/badge";
import { Checkbox } from "@/components/ui/checkbox";
import { Upload, Palette, Type, Zap, Settings } from "lucide-react";
import { ProjectConfig } from "@/types";

interface ProjectConfigurationFormProps {
  validationData: any;
  onSubmit: (config: ProjectConfig) => void;
  isLoading?: boolean;
}

const FONT_OPTIONS = [
  { name: 'Inter', value: 'Inter' },
  { name: 'Roboto', value: 'Roboto' },
  { name: 'Open Sans', value: 'Open Sans' },
  { name: '<PERSON><PERSON><PERSON>', value: '<PERSON><PERSON><PERSON>' },
  { name: '<PERSON><PERSON><PERSON>', value: '<PERSON><PERSON><PERSON>' },
  { name: '<PERSON><PERSON>', value: 'La<PERSON>' }
];

const COLOR_PRESETS = [
  { name: 'Blue Ocean', primary: '#3b82f6', secondary: '#10b981' },
  { name: 'Purple Sunset', primary: '#8b5cf6', secondary: '#f59e0b' },
  { name: 'Green Forest', primary: '#10b981', secondary: '#3b82f6' },
  { name: 'Red Fire', primary: '#ef4444', secondary: '#f97316' },
  { name: 'Pink Blossom', primary: '#ec4899', secondary: '#8b5cf6' },
  { name: 'Teal Wave', primary: '#14b8a6', secondary: '#06b6d4' }
];

export function ProjectConfigurationForm({ validationData, onSubmit, isLoading }: ProjectConfigurationFormProps) {
  const [config, setConfig] = useState<ProjectConfig>({
    app_name: validationData?.title || 'My App',
    colors: {
      primary: '#3b82f6',
      secondary: '#10b981',
      background: '#ffffff',
      text: '#1f2937'
    },
    typography: {
      fontFamily: 'Inter',
      headingFont: 'Inter'
    },
    selected_features: {
      core: [],
      mustHave: [],
      shouldHave: []
    }
  });

  const handleColorChange = (colorType: keyof ProjectConfig['colors'], value: string) => {
    setConfig(prev => ({
      ...prev,
      colors: {
        ...prev.colors,
        [colorType]: value
      }
    }));
  };

  const handleColorPreset = (preset: typeof COLOR_PRESETS[0]) => {
    setConfig(prev => ({
      ...prev,
      colors: {
        ...prev.colors,
        primary: preset.primary,
        secondary: preset.secondary
      }
    }));
  };

  const handleFeatureToggle = (category: keyof ProjectConfig['selected_features'], featureId: string) => {
    setConfig(prev => ({
      ...prev,
      selected_features: {
        ...prev.selected_features,
        [category]: prev.selected_features[category].includes(featureId)
          ? prev.selected_features[category].filter(id => id !== featureId)
          : [...prev.selected_features[category], featureId]
      }
    }));
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    onSubmit(config);
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-6">
      {/* App Basic Info */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Settings className="h-5 w-5" />
            App Configuration
          </CardTitle>
          <CardDescription>
            Configure your app's basic information and branding
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div>
            <Label htmlFor="app_name">App Name</Label>
            <Input
              id="app_name"
              value={config.app_name}
              onChange={(e) => setConfig(prev => ({ ...prev, app_name: e.target.value }))}
              placeholder="Enter your app name"
              required
            />
          </div>

          <div>
            <Label htmlFor="description">Description (Optional)</Label>
            <Textarea
              id="description"
              value={validationData?.projectConcept?.overview || ''}
              placeholder="Brief description of your app"
              rows={3}
              readOnly
              className="bg-muted"
            />
          </div>

          <div>
            <Label>Logo Upload</Label>
            <div className="border-2 border-dashed border-muted-foreground/25 rounded-lg p-6 text-center">
              <Upload className="h-8 w-8 mx-auto mb-2 text-muted-foreground" />
              <p className="text-sm text-muted-foreground">
                Upload your logo or we'll generate one for you
              </p>
              <Button type="button" variant="outline" size="sm" className="mt-2">
                Choose File
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Color Scheme */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Palette className="h-5 w-5" />
            Color Scheme
          </CardTitle>
          <CardDescription>
            Choose colors that represent your brand
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div>
            <Label className="text-sm font-medium mb-3 block">Color Presets</Label>
            <div className="grid grid-cols-2 md:grid-cols-3 gap-3">
              {COLOR_PRESETS.map((preset) => (
                <button
                  key={preset.name}
                  type="button"
                  onClick={() => handleColorPreset(preset)}
                  className="p-3 border rounded-lg hover:border-primary transition-colors"
                >
                  <div className="flex items-center gap-2 mb-1">
                    <div 
                      className="w-4 h-4 rounded-full" 
                      style={{ backgroundColor: preset.primary }}
                    />
                    <div 
                      className="w-4 h-4 rounded-full" 
                      style={{ backgroundColor: preset.secondary }}
                    />
                  </div>
                  <p className="text-xs font-medium">{preset.name}</p>
                </button>
              ))}
            </div>
          </div>

          <div className="grid grid-cols-2 gap-4">
            <div>
              <Label htmlFor="primary_color">Primary Color</Label>
              <div className="flex gap-2">
                <Input
                  id="primary_color"
                  type="color"
                  value={config.colors.primary}
                  onChange={(e) => handleColorChange('primary', e.target.value)}
                  className="w-16 h-10 p-1"
                />
                <Input
                  value={config.colors.primary}
                  onChange={(e) => handleColorChange('primary', e.target.value)}
                  placeholder="#3b82f6"
                  className="flex-1"
                />
              </div>
            </div>

            <div>
              <Label htmlFor="secondary_color">Secondary Color</Label>
              <div className="flex gap-2">
                <Input
                  id="secondary_color"
                  type="color"
                  value={config.colors.secondary}
                  onChange={(e) => handleColorChange('secondary', e.target.value)}
                  className="w-16 h-10 p-1"
                />
                <Input
                  value={config.colors.secondary}
                  onChange={(e) => handleColorChange('secondary', e.target.value)}
                  placeholder="#10b981"
                  className="flex-1"
                />
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Typography */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Type className="h-5 w-5" />
            Typography
          </CardTitle>
          <CardDescription>
            Select fonts for your application
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <Label htmlFor="font_family">Body Font</Label>
              <select
                id="font_family"
                value={config.typography.fontFamily}
                onChange={(e) => setConfig(prev => ({
                  ...prev,
                  typography: { ...prev.typography, fontFamily: e.target.value }
                }))}
                className="w-full p-2 border rounded-md"
              >
                {FONT_OPTIONS.map(font => (
                  <option key={font.value} value={font.value}>
                    {font.name}
                  </option>
                ))}
              </select>
            </div>

            <div>
              <Label htmlFor="heading_font">Heading Font</Label>
              <select
                id="heading_font"
                value={config.typography.headingFont}
                onChange={(e) => setConfig(prev => ({
                  ...prev,
                  typography: { ...prev.typography, headingFont: e.target.value }
                }))}
                className="w-full p-2 border rounded-md"
              >
                {FONT_OPTIONS.map(font => (
                  <option key={font.value} value={font.value}>
                    {font.name}
                  </option>
                ))}
              </select>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Feature Selection */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Zap className="h-5 w-5" />
            Feature Selection
          </CardTitle>
          <CardDescription>
            Choose which features to include in your prototype
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          {validationData?.projectConcept?.features && Object.entries(validationData.projectConcept.features).map(([category, features]) => (
            <div key={category}>
              <h4 className="font-medium mb-3 capitalize">
                {category === 'mustHave' ? 'Must Have' : 
                 category === 'shouldHave' ? 'Should Have' : 
                 category === 'niceToHave' ? 'Nice to Have' : category} Features
              </h4>
              <div className="space-y-2">
                {features.map((feature: any, index: number) => {
                  const featureId = `${category}-${index}`;
                  const isSelected = config.selected_features[category as keyof ProjectConfig['selected_features']]?.includes(featureId);
                  
                  return (
                    <div key={featureId} className="flex items-start space-x-3 p-3 border rounded-lg">
                      <Checkbox
                        id={featureId}
                        checked={isSelected}
                        onCheckedChange={() => handleFeatureToggle(category as keyof ProjectConfig['selected_features'], featureId)}
                      />
                      <div className="flex-1">
                        <label htmlFor={featureId} className="text-sm font-medium cursor-pointer">
                          {feature.description}
                        </label>
                        {feature.platforms && (
                          <div className="flex gap-1 mt-1">
                            {Object.entries(feature.platforms).map(([platform, enabled]) => 
                              enabled && (
                                <Badge key={platform} variant="outline" className="text-xs">
                                  {platform}
                                </Badge>
                              )
                            )}
                          </div>
                        )}
                      </div>
                    </div>
                  );
                })}
              </div>
            </div>
          ))}
        </CardContent>
      </Card>

      {/* Submit Button */}
      <div className="flex justify-end">
        <Button type="submit" size="lg" disabled={isLoading}>
          {isLoading ? 'Generating Prototype...' : 'Generate Prototype'}
        </Button>
      </div>
    </form>
  );
}
