
import { Card, CardContent } from "@/components/ui/card";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { Label } from "@/components/ui/label";

interface UserOption {
  id: string;
  name: string;
  description: string;
}

const userOptions: UserOption[] = [
  {
    id: "small",
    name: "< 1,000 monthly users",
    description: "Early-stage project or niche application with limited user base."
  },
  {
    id: "medium",
    name: "1,000 - 10,000 monthly users",
    description: "Growing application with moderate traffic and user engagement."
  },
  {
    id: "large",
    name: "10,000 - 100,000 monthly users",
    description: "Established application with significant traffic and scaling needs."
  },
  {
    id: "xlarge",
    name: "100,000 - 1M monthly users",
    description: "Popular application requiring robust architecture and performance optimization."
  },
  {
    id: "enterprise",
    name: "> 1M monthly users",
    description: "Mass-market application with enterprise-grade infrastructure requirements."
  }
];

export default function UsersTab({ 
  selected = '', 
  onSelect 
}: { 
  selected: string;
  onSelect: (selected: string) => void;
}) {
  return (
    <div className="space-y-4">
      <h2 className="text-2xl font-bold">Expected Monthly Users</h2>
      <p className="text-muted-foreground mb-6">
        Select the expected scale of your user base.
      </p>
      
      <RadioGroup value={selected} onValueChange={onSelect}>
        <div className="grid grid-cols-1 gap-4">
          {userOptions.map(option => (
            <Card key={option.id} className={`border-2 cursor-pointer transition-colors ${
              selected === option.id ? "border-primary bg-primary/5" : "border-border"
            }`} onClick={() => onSelect(option.id)}>
              <CardContent className="p-6">
                <div className="flex items-start space-x-3">
                  <RadioGroupItem 
                    id={`users-${option.id}`}
                    value={option.id}
                    className="mt-1"
                  />
                  <div>
                    <Label 
                      htmlFor={`users-${option.id}`}
                      className="text-base font-medium cursor-pointer"
                    >
                      {option.name}
                    </Label>
                    <p className="text-sm text-muted-foreground mt-1">
                      {option.description}
                    </p>
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      </RadioGroup>
    </div>
  );
}
