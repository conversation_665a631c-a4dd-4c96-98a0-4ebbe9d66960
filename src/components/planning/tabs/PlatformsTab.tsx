
import { useState } from "react";
import { Check, Globe, Smartphone, Laptop, Building } from "lucide-react";

interface PlatformOption {
  id: string;
  name: string;
  description: string;
  icon: React.ReactNode;
  color: string;
}

const platforms: PlatformOption[] = [
  {
    id: "web",
    name: "Web Application",
    description: "Accessible through web browsers on any device",
    icon: <Globe strokeWidth={1.5} />,
    color: "gradient-blue"
  },
  {
    id: "mobile",
    name: "Mobile Application",
    description: "Native mobile app for iOS and Android",
    icon: <Smartphone strokeWidth={1.5} />,
    color: "gradient-purple"
  },
  {
    id: "desktop",
    name: "Desktop Application",
    description: "Native application for Windows, Mac, or Linux",
    icon: <Laptop strokeWidth={1.5} />,
    color: "gradient-green"
  },
  {
    id: "erp",
    name: "ERP System",
    description: "Enterprise-grade solution deployable on-premises or cloud infrastructure",
    icon: <Building strokeWidth={1.5} />,
    color: "gradient-amber"
  }
];

export default function PlatformsTab({ 
  selected = [], 
  onSelect 
}: { 
  selected: string[];
  onSelect: (selected: string[]) => void;
}) {
  const handleToggle = (id: string) => {
    if (selected.includes(id)) {
      onSelect(selected.filter(item => item !== id));
    } else {
      onSelect([...selected, id]);
    }
  };

  return (
    <div className="space-y-6">
      <div className="space-y-2">
        <h2 className="text-3xl font-bold gradient-text">Platform Selection</h2>
        <p className="text-muted-foreground text-lg">
          Select the platforms where your application will be available.
        </p>
      </div>
      
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        {platforms.map(platform => (
          <div 
            key={platform.id} 
            className={`relative p-6 rounded-lg cursor-pointer transition-all hover-lift ${
              selected.includes(platform.id) 
                ? "border-primary bg-primary/5 shadow-md" 
                : "border border-border"
            }`}
            onClick={() => handleToggle(platform.id)}
          >
            {selected.includes(platform.id) && (
              <div className="absolute right-4 top-4">
                <div className="h-5 w-5 flex items-center justify-center rounded-sm bg-primary">
                  <Check className="h-3.5 w-3.5 text-white" />
                </div>
              </div>
            )}
            <div className="flex items-start gap-4">
              <div className={`icon-bg ${platform.color} text-white`}>
                {platform.icon}
              </div>
              <div>
                <h3 className="text-lg font-medium">{platform.name}</h3>
                <p className="text-muted-foreground text-sm mt-1">
                  {platform.description}
                </p>
              </div>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
}
