
import { Card, CardContent } from "@/components/ui/card";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { Label } from "@/components/ui/label";
import { Gem, DollarSign, WalletCards } from "lucide-react";

interface BudgetOption {
  id: string;
  name: string;
  description: string;
  icon: React.ReactNode;
}

const budgetOptions: BudgetOption[] = [
  {
    id: "5k-25k",
    name: "$5,000 - $25,000",
    description: "Starter budget for basic MVPs and simple applications.",
    icon: <DollarSign className="w-10 h-10 text-green-500" />
  },
  {
    id: "25k-50k",
    name: "$25,000 - $50,000",
    description: "Mid-range budget for more feature-rich applications.",
    icon: <WalletCards className="w-10 h-10 text-blue-500" />
  },
  {
    id: "50k-100k",
    name: "$50,000 - $100,000",
    description: "Professional budget for comprehensive, high-quality applications.",
    icon: <Gem className="w-10 h-10 text-amber-500" />
  },
  {
    id: "100k-plus",
    name: "$100,000+",
    description: "Enterprise budget for large-scale, complex applications with advanced features.",
    icon: <Gem className="w-10 h-10 text-purple-500" />
  }
];

export default function BudgetTab({ 
  selected = '', 
  onSelect 
}: { 
  selected: string;
  onSelect: (selected: string) => void;
}) {
  return (
    <div className="space-y-4">
      <h2 className="text-2xl font-bold">Budget Range</h2>
      <p className="text-muted-foreground mb-6">
        Select your estimated budget range for the project.
      </p>
      
      <RadioGroup value={selected} onValueChange={onSelect}>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {budgetOptions.map(option => (
            <Card key={option.id} className={`border-2 cursor-pointer transition-colors ${
              selected === option.id ? "border-primary bg-primary/5" : "border-border"
            }`} onClick={() => onSelect(option.id)}>
              <CardContent className="p-6">
                <div className="flex items-center space-x-4">
                  <div className="flex-shrink-0">
                    {option.icon}
                  </div>
                  <div className="flex-1">
                    <RadioGroupItem 
                      id={`budget-${option.id}`}
                      value={option.id}
                      className="sr-only"
                    />
                    <Label 
                      htmlFor={`budget-${option.id}`}
                      className="text-base font-medium cursor-pointer block mb-1"
                    >
                      {option.name}
                    </Label>
                    <p className="text-sm text-muted-foreground">
                      {option.description}
                    </p>
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      </RadioGroup>
    </div>
  );
}
