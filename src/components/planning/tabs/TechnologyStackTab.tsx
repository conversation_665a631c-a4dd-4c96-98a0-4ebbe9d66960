
import { useState } from "react";
import { Check } from "lucide-react";
import { Badge } from "@/components/ui/badge";

type TechCategory = "Frontend" | "Backend" | "Mobile" | "Database" | "Cloud & DevOps";

interface TechOption {
  id: string;
  name: string;
  description: string;
  category: TechCategory;
}

const techOptions: TechOption[] = [
  // Frontend
  {
    id: "react",
    name: "React.js",
    description: "Modern UI development",
    category: "Frontend"
  },
  {
    id: "vue",
    name: "Vue.js",
    description: "Progressive framework",
    category: "Frontend"
  },
  {
    id: "angular",
    name: "Angular",
    description: "Enterprise-ready framework",
    category: "Frontend"
  },
  {
    id: "svelte",
    name: "Svelte",
    description: "Compiled framework",
    category: "Frontend"
  },
  {
    id: "nextjs",
    name: "Next.js",
    description: "React framework for production",
    category: "Frontend"
  },
  {
    id: "tailwind",
    name: "Tailwind CSS",
    description: "Utility-first CSS",
    category: "Frontend"
  },
  {
    id: "materialui",
    name: "Material-UI",
    description: "React UI library",
    category: "Frontend"
  },
  {
    id: "bootstrap",
    name: "Bootstrap",
    description: "CSS framework",
    category: "Frontend"
  },
  {
    id: "typescript",
    name: "TypeScript",
    description: "Typed JavaScript",
    category: "Frontend"
  },

  // Backend
  {
    id: "nodejs",
    name: "Node.js",
    description: "JavaScript runtime",
    category: "Backend"
  },
  {
    id: "express",
    name: "Express.js",
    description: "Web framework for Node.js",
    category: "Backend"
  },
  {
    id: "django",
    name: "Django",
    description: "Python web framework",
    category: "Backend"
  },
  {
    id: "flask",
    name: "Flask",
    description: "Lightweight Python framework",
    category: "Backend"
  },
  {
    id: "springboot",
    name: "Spring Boot",
    description: "Java framework",
    category: "Backend"
  },
  {
    id: "fastapi",
    name: "FastAPI",
    description: "Modern Python framework",
    category: "Backend"
  },
  {
    id: "nestjs",
    name: "NestJS",
    description: "TypeScript backend framework",
    category: "Backend"
  },

  // Mobile
  {
    id: "swift",
    name: "iOS Swift",
    description: "Native iOS development",
    category: "Mobile"
  },
  {
    id: "kotlin",
    name: "Android Kotlin",
    description: "Native Android development",
    category: "Mobile"
  },
  {
    id: "reactnative",
    name: "React Native",
    description: "Cross-platform mobile",
    category: "Mobile"
  },
  {
    id: "flutter",
    name: "Flutter",
    description: "Google's UI toolkit",
    category: "Mobile"
  },
  {
    id: "ionic",
    name: "Ionic",
    description: "Hybrid mobile framework",
    category: "Mobile"
  },

  // Database
  {
    id: "postgresql",
    name: "PostgreSQL",
    description: "Advanced SQL database",
    category: "Database"
  },
  {
    id: "mongodb",
    name: "MongoDB",
    description: "NoSQL document database",
    category: "Database"
  },
  {
    id: "mysql",
    name: "MySQL",
    description: "Popular SQL database",
    category: "Database"
  },
  {
    id: "oracle",
    name: "Oracle DB",
    description: "Enterprise database",
    category: "Database"
  },

  // Cloud & DevOps
  {
    id: "aws",
    name: "AWS",
    description: "Amazon Web Services",
    category: "Cloud & DevOps"
  },
  {
    id: "azure",
    name: "Azure",
    description: "Microsoft Cloud",
    category: "Cloud & DevOps"
  },
  {
    id: "gcp",
    name: "Google Cloud",
    description: "Google Cloud Platform",
    category: "Cloud & DevOps"
  },
  {
    id: "docker",
    name: "Docker",
    description: "Containerization",
    category: "Cloud & DevOps"
  },
  {
    id: "kubernetes",
    name: "Kubernetes",
    description: "Container orchestration",
    category: "Cloud & DevOps"
  }
];

export default function TechnologyStackTab({ 
  selected = [], 
  onSelect 
}: { 
  selected: string[];
  onSelect: (selected: string[]) => void;
}) {
  const [activeCategory, setActiveCategory] = useState<TechCategory>("Frontend");
  const categories: TechCategory[] = ["Frontend", "Backend", "Mobile", "Database", "Cloud & DevOps"];
  
  const selectedCountsByCategory = categories.reduce((acc, category) => {
    const count = selected.filter(id => 
      techOptions.find(tech => tech.id === id)?.category === category
    ).length;
    return { ...acc, [category]: count };
  }, {} as Record<TechCategory, number>);

  const handleToggle = (id: string) => {
    if (selected.includes(id)) {
      onSelect(selected.filter(item => item !== id));
    } else {
      onSelect([...selected, id]);
    }
  };

  const filteredOptions = techOptions.filter(option => option.category === activeCategory);

  return (
    <div className="space-y-6">
      <div className="space-y-2">
        <h2 className="text-3xl font-bold">Technology Stack</h2>
        <p className="text-muted-foreground text-lg">
          Select your preferred technologies. Don't worry if you're unsure - we'll help you choose the best stack for your needs.
        </p>
      </div>
      
      <div className="flex flex-wrap gap-2">
        {categories.map(category => (
          <button
            key={category}
            className={`flex items-center gap-2 px-4 py-2 rounded-lg border transition-colors ${
              activeCategory === category 
                ? "bg-primary text-white border-primary" 
                : "bg-background border-border hover:border-primary/50"
            }`}
            onClick={() => setActiveCategory(category)}
          >
            <span>{category}</span>
            {selectedCountsByCategory[category] > 0 && (
              <Badge variant="secondary" className={`${activeCategory === category ? "bg-white text-primary" : "bg-primary/10 text-primary"}`}>
                {selectedCountsByCategory[category]}
              </Badge>
            )}
          </button>
        ))}
      </div>

      <div className="grid grid-cols-1 md:grid-cols-3 gap-3">
        {filteredOptions.map(tech => (
          <div 
            key={tech.id} 
            className={`relative p-4 border rounded-lg cursor-pointer transition-all hover:border-primary/50 ${
              selected.includes(tech.id) ? "border-primary bg-primary/5" : "border-border"
            }`}
            onClick={() => handleToggle(tech.id)}
          >
            {selected.includes(tech.id) && (
              <div className="absolute right-3 top-3">
                <div className="h-5 w-5 flex items-center justify-center rounded-sm bg-primary">
                  <Check className="h-3.5 w-3.5 text-white" />
                </div>
              </div>
            )}
            <div>
              <h3 className="font-medium">{tech.name}</h3>
              <p className="text-muted-foreground text-sm mt-1">
                {tech.description}
              </p>
            </div>
          </div>
        ))}
      </div>

      <div className="bg-blue-50 border border-blue-200 p-4 rounded-lg text-blue-800 mt-6">
        <p>
          Our team will help you finalize the technology stack during the consultation, ensuring it matches your project's
          requirements and scalability needs.
        </p>
      </div>
    </div>
  );
}
