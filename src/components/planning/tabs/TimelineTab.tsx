
import { Card, CardContent } from "@/components/ui/card";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { Label } from "@/components/ui/label";
import { Clock, CalendarDays, CalendarCheck, CalendarRange } from "lucide-react";

interface TimelineOption {
  id: string;
  name: string;
  description: string;
  icon: React.ReactNode;
}

const timelineOptions: TimelineOption[] = [
  {
    id: "1-3-months",
    name: "1-3 Months",
    description: "Quick turnaround for simple MVPs or proof of concepts.",
    icon: <Clock className="w-10 h-10 text-blue-500" />
  },
  {
    id: "3-6-months",
    name: "3-6 Months",
    description: "Balanced timeline for developing more comprehensive MVPs or small applications.",
    icon: <CalendarDays className="w-10 h-10 text-green-500" />
  },
  {
    id: "6-12-months",
    name: "6-12 Months",
    description: "Full development cycle for complex applications with multiple features.",
    icon: <CalendarCheck className="w-10 h-10 text-amber-500" />
  },
  {
    id: "12-plus-months",
    name: "12+ Months",
    description: "Enterprise-grade development for large-scale, feature-rich applications.",
    icon: <CalendarRange className="w-10 h-10 text-rose-500" />
  }
];

export default function TimelineTab({ 
  selected = '', 
  onSelect 
}: { 
  selected: string;
  onSelect: (selected: string) => void;
}) {
  return (
    <div className="space-y-4">
      <h2 className="text-2xl font-bold">Project Timeline</h2>
      <p className="text-muted-foreground mb-6">
        Select your preferred development timeline for the project.
      </p>
      
      <RadioGroup value={selected} onValueChange={onSelect}>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {timelineOptions.map(option => (
            <Card key={option.id} className={`border-2 cursor-pointer transition-colors ${
              selected === option.id ? "border-primary bg-primary/5" : "border-border"
            }`} onClick={() => onSelect(option.id)}>
              <CardContent className="p-6">
                <div className="flex items-center space-x-4">
                  <div className="flex-shrink-0">
                    {option.icon}
                  </div>
                  <div className="flex-1">
                    <RadioGroupItem 
                      id={`timeline-${option.id}`}
                      value={option.id}
                      className="sr-only"
                    />
                    <Label 
                      htmlFor={`timeline-${option.id}`}
                      className="text-base font-medium cursor-pointer block mb-1"
                    >
                      {option.name}
                    </Label>
                    <p className="text-sm text-muted-foreground">
                      {option.description}
                    </p>
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      </RadioGroup>
    </div>
  );
}
