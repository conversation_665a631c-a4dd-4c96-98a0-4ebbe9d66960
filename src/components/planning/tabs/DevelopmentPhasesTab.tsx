
import { <PERSON>, <PERSON>, <PERSON>rkles } from "lucide-react";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { RocketIcon, CodeIcon, BarChartIcon, BoxesIcon } from "lucide-react";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { useNavigate, useParams } from "react-router-dom";
import { useSubscriptionStore } from "@/store/subscriptionStore";
import { useEffect, useState } from "react";

interface PhaseOption {
  id: string;
  name: string;
  description: string;
  icon: React.ReactNode;
  features: string[];
  isPremium: boolean;
}

const phases: PhaseOption[] = [
  {
    id: "interactive",
    name: "Interactive Prototype",
    description: "A clickable prototype with core features to validate the concept",
    icon: <RocketIcon className="h-5 w-5 text-primary" />,
    features: [
      "Core user flows",
      "Basic functionality",
      "User testing ready",
      "Quick iterations"
    ],
    isPremium: false
  },
  {
    id: "poc",
    name: "Proof of Concept (POC)",
    description: "A working prototype with basic backend integration to validate technical feasibility",
    icon: <CodeIcon className="h-5 w-5 text-primary" />,
    features: [
      "Basic database integration",
      "Core API endpoints",
      "Authentication flow",
      "Technical validation"
    ],
    isPremium: true
  },
  {
    id: "mvp",
    name: "Minimum Viable Product",
    description: "A functional product with essential features for early adopters",
    icon: <BarChartIcon className="h-5 w-5 text-primary" />,
    features: [
      "Essential features",
      "Full backend integration",
      "User authentication",
      "Data persistence"
    ],
    isPremium: true
  },
  {
    id: "production",
    name: "Production Ready",
    description: "A fully featured product ready for market launch",
    icon: <BoxesIcon className="h-5 w-5 text-primary" />,
    features: [
      "Complete feature set",
      "Scalable architecture",
      "Advanced security",
      "Production optimization"
    ],
    isPremium: true
  }
];

export default function DevelopmentPhasesTab({ 
  selected = [], 
  onSelect 
}: { 
  selected: string[];
  onSelect: (selected: string[]) => void;
}) {
  const navigate = useNavigate();
  const { id: validationId } = useParams();
  const { getUserAccessiblePhases } = useSubscriptionStore();
  const [accessiblePhases, setAccessiblePhases] = useState<string[]>(["interactive"]);
  const [isLoading, setIsLoading] = useState(true);

  // Use an effect to load the accessible phases just once when component mounts
  useEffect(() => {
    const loadAccessiblePhases = async () => {
      if (!validationId) {
        console.error("No validation ID available for access check");
        setAccessiblePhases(["interactive"]);
        setIsLoading(false);
        return;
      }
      
      try {
        setIsLoading(true);
        console.log("Checking accessible phases for project:", validationId);
        
        // Load phases once
        const phases = await getUserAccessiblePhases(validationId);
        console.log("Received accessible phases:", phases);
        
        setAccessiblePhases(phases);
      } catch (error) {
        console.error("Error loading accessible phases:", error);
        // Default to just interactive if there's an error
        setAccessiblePhases(["interactive"]);
      } finally {
        setIsLoading(false);
      }
    };
    
    // Load phases once on mount
    loadAccessiblePhases();
  }, [validationId, getUserAccessiblePhases]);

  const handleToggle = (id: string) => {
    // Allow toggling all phases regardless of accessibility
    if (selected.includes(id)) {
      onSelect(selected.filter(item => item !== id));
    } else {
      onSelect([...selected, id]);
    }
  };

  const handleViewPackages = () => {
    // Pass the current validation ID to the pricing page
    navigate(`/pricing?project=${validationId}`);
  };

  return (
    <div className="space-y-6">
      <div className="space-y-2">
        <h2 className="text-3xl font-bold">Development Phases</h2>
        <p className="text-muted-foreground text-lg">
          Select the development phases for your project.
        </p>
      </div>

      <Alert className="bg-blue-50 border-blue-200 text-blue-800">
        <AlertDescription className="text-blue-800">
          <p className="mb-3">
            You can select all development phases for planning purposes. Admin will be able to generate roadmaps for all phases at once.
            However, you'll only be able to view roadmaps for phases you have access to.
          </p>
          <p>
            We recommend starting with an Interactive Prototype, followed by a Proof of Concept (POC) to validate technical
            feasibility, before proceeding with full development.
          </p>
        </AlertDescription>
      </Alert>

      <Alert variant="default" className="bg-amber-50 border-amber-200">
        <Sparkles className="h-4 w-4 text-amber-500" />
        <AlertTitle className="font-medium text-amber-700">Subscription Information</AlertTitle>
        <AlertDescription className="text-amber-700">
          <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
            <p>Interactive Prototype is available on the free plan. Unlock additional phases by upgrading your subscription.</p>
            <Button 
              onClick={handleViewPackages}
              variant="default"
              size="sm"
              className="whitespace-nowrap"
            >
              <Sparkles className="mr-2 h-3 w-3" />
              View Pricing Plans
            </Button>
          </div>
        </AlertDescription>
      </Alert>
      
      {isLoading ? (
        <div className="flex justify-center py-8">
          <div className="inline-block h-8 w-8 animate-spin rounded-full border-4 border-solid border-current border-r-transparent align-[-0.125em] motion-reduce:animate-[spin_1.5s_linear_infinite]" />
        </div>
      ) : (
        <div className="grid grid-cols-1 gap-4">
          {phases.map(phase => {
            const isAccessible = accessiblePhases.includes(phase.id);
            console.log(`Phase ${phase.id} is accessible: ${isAccessible}`);
            
            return (
              <div 
                key={phase.id} 
                className={`relative p-6 border rounded-lg cursor-pointer hover:border-primary/50 transition-all ${
                  selected.includes(phase.id) ? "border-primary bg-primary/5" : "border-border"
                }`}
                onClick={() => handleToggle(phase.id)}
              >
                {selected.includes(phase.id) && (
                  <div className="absolute right-4 top-4">
                    <div className="h-5 w-5 flex items-center justify-center rounded-sm bg-primary">
                      <Check className="h-3.5 w-3.5 text-white" />
                    </div>
                  </div>
                )}
                <div>
                  <div className="flex items-center gap-2 mb-2">
                    <div className="h-8 w-8 rounded-full bg-primary/10 flex items-center justify-center">
                      {phase.icon}
                    </div>
                    <h3 className="text-lg font-medium">{phase.name}</h3>
                    {phase.isPremium && !isAccessible && (
                      <Badge variant="secondary" className="ml-2 border-amber-500 text-amber-600">
                        <Lock className="h-3 w-3 mr-1" /> 
                        Premium
                      </Badge>
                    )}
                    {!phase.isPremium && (
                      <Badge variant="default" className="ml-2 bg-green-500">
                        <Sparkles className="h-3 w-3 mr-1" /> 
                        Free
                      </Badge>
                    )}
                    {phase.isPremium && isAccessible && (
                      <Badge variant="default" className="ml-2 bg-green-500">
                        <Sparkles className="h-3 w-3 mr-1" /> 
                        Unlocked
                      </Badge>
                    )}
                  </div>
                  <p className="text-muted-foreground text-sm mb-4">
                    {phase.description}
                  </p>
                  <div className="grid grid-cols-2 gap-2">
                    {phase.features.map((feature, index) => (
                      <div key={index} className="flex items-center gap-2">
                        <div className="h-2 w-2 rounded-full bg-primary"></div>
                        <span className="text-sm">{feature}</span>
                      </div>
                    ))}
                  </div>
                </div>
              </div>
            );
          })}
        </div>
      )}
    </div>
  );
}
