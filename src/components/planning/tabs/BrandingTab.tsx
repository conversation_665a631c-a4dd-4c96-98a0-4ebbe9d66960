
import { useState } from "react";
import { <PERSON>, <PERSON>rk<PERSON>, <PERSON><PERSON>, PanelLeft } from "lucide-react";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Separator } from "@/components/ui/separator";

interface VisualStyle {
  id: string;
  name: string;
  description: string;
}

const visualStyles: VisualStyle[] = [
  {
    id: "minimal",
    name: "Minimal",
    description: "Clean, simple, and modern design"
  },
  {
    id: "modern",
    name: "Modern",
    description: "Contemporary and innovative look"
  },
  {
    id: "classic",
    name: "Classic",
    description: "Traditional and timeless design"
  },
  {
    id: "playful",
    name: "Playful",
    description: "Fun and engaging interface"
  },
  {
    id: "corporate",
    name: "Corporate",
    description: "Professional and business-focused"
  }
];

const suggestedKeywords = [
  "Innovative", "Trustworthy", "Friendly", "Elegant", "Bold", 
  "Sophisticated", "Playful", "Minimalist", "Luxurious", 
  "Technical", "Organic", "Energetic"
];

export default function BrandingTab({ 
  selected = [], 
  onSelect 
}: { 
  selected: string[];
  onSelect: (selected: string[]) => void;
}) {
  const [primaryColor, setPrimaryColor] = useState("#1B25AC");
  const [brandKeyword, setBrandKeyword] = useState("");
  const [brandKeywords, setBrandKeywords] = useState<string[]>([]);

  const handleStyleToggle = (id: string) => {
    if (selected.includes(id)) {
      onSelect(selected.filter(item => item !== id));
    } else {
      onSelect([...selected, id]);
    }
  };

  const addKeyword = () => {
    if (brandKeyword && !brandKeywords.includes(brandKeyword)) {
      setBrandKeywords([...brandKeywords, brandKeyword]);
      setBrandKeyword("");
    }
  };

  const removeKeyword = (keyword: string) => {
    setBrandKeywords(brandKeywords.filter(k => k !== keyword));
  };

  const addSuggestedKeyword = (keyword: string) => {
    if (!brandKeywords.includes(keyword)) {
      setBrandKeywords([...brandKeywords, keyword]);
    }
  };

  return (
    <div className="space-y-8">
      <div className="space-y-2">
        <h2 className="text-3xl font-bold">Branding Preferences</h2>
        <p className="text-muted-foreground text-lg">
          Define your brand's visual style and personality.
        </p>
      </div>

      <div className="space-y-4">
        <div className="flex items-center gap-2">
          <Palette className="h-5 w-5 text-primary" />
          <h3 className="text-xl font-semibold">Brand Colors</h3>
        </div>
        
        <div className="space-y-2">
          <p className="text-sm text-muted-foreground">Primary Color</p>
          <div className="flex items-center gap-3">
            <div className="w-12 h-12 rounded border">
              <div 
                className="w-full h-full rounded" 
                style={{ backgroundColor: primaryColor }}
              ></div>
            </div>
            <div 
              className="w-16 h-16 rounded border" 
              style={{ backgroundColor: primaryColor }}
            ></div>
            <Input 
              type="text" 
              value={primaryColor}
              onChange={(e) => setPrimaryColor(e.target.value)}
              className="w-28"
            />
          </div>
        </div>
      </div>

      <Separator />

      <div className="space-y-4">
        <div className="flex items-center gap-2">
          <PanelLeft className="h-5 w-5 text-primary" />
          <h3 className="text-xl font-semibold">Visual Style</h3>
        </div>
        
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-3">
          {visualStyles.map(style => (
            <div 
              key={style.id} 
              className={`relative p-4 border rounded-lg cursor-pointer transition-all hover:border-primary/50 ${
                selected.includes(style.id) ? "border-primary bg-primary/5" : "border-border"
              }`}
              onClick={() => handleStyleToggle(style.id)}
            >
              {selected.includes(style.id) && (
                <div className="absolute right-3 top-3">
                  <div className="h-5 w-5 flex items-center justify-center rounded-sm bg-primary">
                    <Check className="h-3.5 w-3.5 text-white" />
                  </div>
                </div>
              )}
              <div>
                <h3 className="font-medium">{style.name}</h3>
                <p className="text-muted-foreground text-sm mt-1">
                  {style.description}
                </p>
              </div>
            </div>
          ))}
        </div>
      </div>

      <Separator />

      <div className="space-y-4">
        <div className="flex items-center gap-2">
          <Sparkles className="h-5 w-5 text-primary" />
          <h3 className="text-xl font-semibold">Brand Personality</h3>
        </div>
        
        <div className="flex items-center gap-2">
          <Input 
            type="text" 
            placeholder="Enter a mood keyword" 
            value={brandKeyword}
            onChange={(e) => setBrandKeyword(e.target.value)}
            className="w-full md:w-80"
          />
          <Button onClick={addKeyword}>Add</Button>
        </div>

        {brandKeywords.length > 0 && (
          <div className="flex flex-wrap gap-2 mt-2">
            {brandKeywords.map(keyword => (
              <Badge 
                key={keyword} 
                variant="secondary" 
                className="px-3 py-1.5 bg-primary/10 text-sm"
                onClick={() => removeKeyword(keyword)}
              >
                {keyword} <span className="ml-1.5 cursor-pointer">×</span>
              </Badge>
            ))}
          </div>
        )}

        <div className="mt-4">
          <p className="text-sm text-muted-foreground mb-2">Suggested keywords:</p>
          <div className="flex flex-wrap gap-2">
            {suggestedKeywords.map(keyword => (
              <Badge 
                key={keyword} 
                variant="outline"
                className="px-3 py-1 cursor-pointer hover:bg-primary/5"
                onClick={() => addSuggestedKeyword(keyword)}
              >
                {keyword}
              </Badge>
            ))}
          </div>
        </div>
      </div>
    </div>
  );
}
