import { useState, useEffect } from "react";
import { useNavigate } from "react-router-dom";
import { toast } from "sonner";
import { useFormStore } from "@/store/formStore";
import { supabase } from "@/integrations/supabase/client";
import { Tabs, <PERSON><PERSON><PERSON>ontent, Ta<PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Separator } from "@/components/ui/separator";
import { Progress } from "@/components/ui/progress";
import { AlertBanner } from "@/components/ui/AlertBanner";
import { useAuthStore } from "@/store/authStore";
import { ChevronLeft, ChevronRight, Save } from "lucide-react";
import PlatformsTab from "./tabs/PlatformsTab";
import DevelopmentPhasesTab from "./tabs/DevelopmentPhasesTab";
import TechnologyStackTab from "./tabs/TechnologyStackTab";
import BrandingTab from "./tabs/BrandingTab";
import UsersTab from "./tabs/UsersTab";
import TimelineTab from "./tabs/TimelineTab";
import BudgetTab from "./tabs/BudgetTab";

export default function PlanningWizard({ validationId }: { validationId: string }) {
  const [currentTab, setCurrentTab] = useState("platforms");
  const [saving, setSaving] = useState(false);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState({ show: false, message: "" });
  const navigate = useNavigate();
  const { isAuthenticated } = useAuthStore();
  const { planningForm, setPlanningField, setValidationId, resetPlanningForm } = useFormStore();

  useEffect(() => {
    if (validationId) {
      resetPlanningForm();
      
      const loadExistingData = async () => {
        setLoading(true);
        try {
          const { data, error } = await supabase
            .from('project_planning')
            .select('*')
            .eq('validation_id', validationId)
            .maybeSingle();
          
          if (error) throw error;
          
          if (data) {
            console.log("Loading existing planning data:", data);
            
            const mapJsonToStringArray = (jsonArray: any): string[] => {
              if (!Array.isArray(jsonArray)) return [];
              return jsonArray.map(item => typeof item === 'string' ? item : String(item));
            };
            
            setPlanningField('platforms', mapJsonToStringArray(data.platforms));
            setPlanningField('developmentPhases', mapJsonToStringArray(data.development_phases));
            setPlanningField('technologyStack', mapJsonToStringArray(data.technology_stack));
            setPlanningField('brandingPreferences', mapJsonToStringArray(data.branding_preferences));
            
            setPlanningField('monthlyUsers', typeof data.monthly_users === 'string' ? data.monthly_users : '');
            setPlanningField('timeline', typeof data.timeline === 'string' ? data.timeline : '');
            setPlanningField('budgetRange', typeof data.budget_range === 'string' ? data.budget_range : '');
          }
          
          setValidationId(validationId);
        } catch (err) {
          console.error("Error loading planning data:", err);
          setError({
            show: true,
            message: "Failed to load planning data. Please try again."
          });
        } finally {
          setLoading(false);
        }
      };
      
      loadExistingData();
    }
  }, [validationId, setValidationId, resetPlanningForm, setPlanningField]);

  const tabs = [
    "platforms",
    "development",
    "technology",
    "branding",
    "users",
    "timeline",
    "budget"
  ];

  const tabsWithLabels = [
    { id: "platforms", label: "Platforms" },
    { id: "development", label: "Phases" },
    { id: "technology", label: "Tech Stack" },
    { id: "branding", label: "Branding" },
    { id: "users", label: "Users" },
    { id: "timeline", label: "Timeline" },
    { id: "budget", label: "Budget" }
  ];

  const currentTabIndex = tabs.indexOf(currentTab);
  const progressPercent = ((currentTabIndex + 1) / tabs.length) * 100;

  const nextTab = () => {
    const currentIndex = tabs.indexOf(currentTab);
    if (currentIndex < tabs.length - 1) {
      setCurrentTab(tabs[currentIndex + 1]);
    } else {
      handleSubmitRoadmap();
    }
  };

  const prevTab = () => {
    const currentIndex = tabs.indexOf(currentTab);
    if (currentIndex > 0) {
      setCurrentTab(tabs[currentIndex - 1]);
    } else {
      navigate(`/validate-idea/results/${validationId}`);
    }
  };

  const getStepNumber = () => {
    return tabs.indexOf(currentTab) + 1;
  };

  const handleSubmitRoadmap = async () => {
    if (!isAuthenticated) {
      setError({
        show: true,
        message: "You must be logged in to save your planning data",
      });
      return;
    }

    setSaving(true);
    try {
      const { data: existingData, error: fetchError } = await supabase
        .from('project_planning')
        .select('id')
        .eq('validation_id', validationId)
        .maybeSingle();

      if (fetchError) throw fetchError;

      const planningData = {
        validation_id: validationId,
        platforms: planningForm.platforms,
        development_phases: planningForm.developmentPhases,
        technology_stack: planningForm.technologyStack,
        branding_preferences: planningForm.brandingPreferences,
        monthly_users: planningForm.monthlyUsers,
        timeline: planningForm.timeline,
        budget_range: planningForm.budgetRange
      };

      let error;
      
      if (existingData) {
        const { error: updateError } = await supabase
          .from('project_planning')
          .update(planningData)
          .eq('id', existingData.id);
        error = updateError;
      } else {
        const { error: insertError } = await supabase
          .from('project_planning')
          .insert([planningData]);
        error = insertError;
      }

      if (error) throw error;

      toast.success("Planning data saved successfully!");
      navigate(`/validate-idea/results/${validationId}`);
    } catch (err: any) {
      console.error("Error saving planning data:", err);
      setError({
        show: true,
        message: `Failed to save planning data: ${err.message}`,
      });
    } finally {
      setSaving(false);
    }
  };

  if (loading) {
    return (
      <Card className="border-primary/10">
        <CardHeader className="pb-4">
          <CardTitle>Planning Wizard</CardTitle>
        </CardHeader>
        <CardContent className="flex justify-center items-center py-12">
          <div className="text-center">
            <div className="inline-block h-8 w-8 animate-spin rounded-full border-4 border-solid border-current border-r-transparent align-[-0.125em] motion-reduce:animate-[spin_1.5s_linear_infinite]" />
            <p className="mt-4 text-muted-foreground">Loading planning data...</p>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <>
      <AlertBanner
        variant="error"
        message={error.message}
        show={error.show}
        onClose={() => setError({ ...error, show: false })}
      />

      <div className="space-y-2 mb-6">
        <div className="flex items-center justify-between">
          <h1 className="text-3xl font-bold">Project Planning</h1>
          <div className="text-sm font-medium">
            Step {getStepNumber()} of {tabs.length}
          </div>
        </div>
        <Progress value={progressPercent} className="h-2 w-full bg-gray-200" />
      </div>

      <Card className="border-primary/10">
        <CardHeader className="pb-4">
          <CardTitle>Planning Wizard</CardTitle>
        </CardHeader>
        <CardContent>
          <Tabs value={currentTab} onValueChange={setCurrentTab}>
            <TabsList className="grid grid-cols-7 mb-8">
              {tabsWithLabels.map((tab) => (
                <TabsTrigger key={tab.id} value={tab.id}>
                  {tab.label}
                </TabsTrigger>
              ))}
            </TabsList>

            <TabsContent value="platforms">
              <PlatformsTab 
                selected={planningForm.platforms} 
                onSelect={(platforms) => setPlanningField('platforms', platforms)} 
              />
            </TabsContent>

            <TabsContent value="development">
              <DevelopmentPhasesTab 
                selected={planningForm.developmentPhases} 
                onSelect={(phases) => setPlanningField('developmentPhases', phases)} 
              />
            </TabsContent>

            <TabsContent value="technology">
              <TechnologyStackTab 
                selected={planningForm.technologyStack} 
                onSelect={(stack) => setPlanningField('technologyStack', stack)} 
              />
            </TabsContent>

            <TabsContent value="branding">
              <BrandingTab 
                selected={planningForm.brandingPreferences} 
                onSelect={(prefs) => setPlanningField('brandingPreferences', prefs)} 
              />
            </TabsContent>

            <TabsContent value="users">
              <UsersTab 
                selected={planningForm.monthlyUsers} 
                onSelect={(users) => setPlanningField('monthlyUsers', users)} 
              />
            </TabsContent>

            <TabsContent value="timeline">
              <TimelineTab 
                selected={planningForm.timeline} 
                onSelect={(timeline) => setPlanningField('timeline', timeline)} 
              />
            </TabsContent>

            <TabsContent value="budget">
              <BudgetTab 
                selected={planningForm.budgetRange} 
                onSelect={(budget) => setPlanningField('budgetRange', budget)} 
              />
            </TabsContent>
          </Tabs>

          <Separator className="my-6" />

          <div className="flex justify-between mt-8">
            <Button variant="outline" onClick={prevTab}>
              <ChevronLeft className="mr-2 h-4 w-4" />
              {currentTab === "platforms" ? "Back to Results" : "Previous Step"}
            </Button>

            <Button onClick={nextTab} disabled={saving}>
              {saving ? (
                "Saving..."
              ) : currentTab === "budget" ? (
                <>
                  Save Planning
                  <Save className="ml-2 h-4 w-4" />
                </>
              ) : (
                <>
                  Next Step
                  <ChevronRight className="ml-2 h-4 w-4" />
                </>
              )}
            </Button>
          </div>
        </CardContent>
      </Card>
    </>
  );
}
