
import { FloatingFeature as FloatingFeatureType } from "@/types";

interface FloatingFeatureProps {
  feature: FloatingFeatureType;
}

export const FloatingFeature = ({ feature }: FloatingFeatureProps) => {
  return (
    <div className="bg-white/80 dark:bg-gray-800/80 p-4 rounded-lg shadow-xl backdrop-blur-sm border border-gray-200/50 dark:border-gray-700/50 hover:scale-105 transition-transform duration-200">
      <div className="flex items-center space-x-2">
        <div className={`w-2 h-2 ${feature.color} rounded-full animate-pulse`}></div>
        <span className="text-sm font-medium text-gray-800 dark:text-gray-200">{feature.text}</span>
      </div>
    </div>
  );
};
