
import { useEffect } from "react";
import { useNavigate, useLocation } from "react-router-dom";
import { Loader2 } from "lucide-react";
import { toast } from "sonner";
import { useAuthStore } from "@/store/authStore";
import { useUserStore } from "@/store/userStore";
import { useSubscriptionStore } from "@/store/subscriptionStore";

interface ProtectedRouteProps {
  children: React.ReactNode | ((props: { isAdmin: boolean }) => React.ReactNode);
  requireAdmin?: boolean;
}

export default function ProtectedRoute({ children, requireAdmin = false }: ProtectedRouteProps) {
  const navigate = useNavigate();
  const location = useLocation();
  const { isLoading, isAuthenticated, isAdmin, checkAuthStatus } = useAuthStore();
  const { profile, fetchProfile } = useUserStore();
  const { fetchCurrentSubscription, fetchFreePlanLimits } = useSubscriptionStore();

  useEffect(() => {
    // Force a check of auth status when the protected route mounts
    const loadInitialData = async () => {
      await checkAuthStatus();
      
      if (isAuthenticated) {
        // If authenticated but no profile data, fetch it
        if (!profile) {
          await fetchProfile();
        }
        
        // Fetch subscription data
        await fetchCurrentSubscription();
        
        // Fetch free plan limits
        await fetchFreePlanLimits();
      }
    };
    
    loadInitialData();
  }, [checkAuthStatus, isAuthenticated, profile, fetchProfile, fetchCurrentSubscription, fetchFreePlanLimits]);

  useEffect(() => {
    if (!isLoading) {
      // Not authenticated, redirect to login
      if (!isAuthenticated) {
        toast.error("Please log in to access this page");
        navigate("/login");
        return;
      }

      // If the route requires admin access and user is not admin, redirect to dashboard
      if (requireAdmin && !isAdmin) {
        toast.error("Access denied. Admin privileges required.");
        navigate("/dashboard");
        return;
      }

      // If user is not admin but trying to access admin routes, redirect to dashboard
      if (!isAdmin && location.pathname.startsWith('/admin')) {
        toast.error("Access denied. Admin privileges required.");
        navigate("/dashboard");
        return;
      }

      // If user is admin but they're trying to access regular dashboard, redirect to admin dashboard
      if (isAdmin && location.pathname === "/dashboard") {
        navigate("/admin/dashboard");
        return;
      }
      
      // Allow admins to view any validation results or roadmaps, regardless of ownership
      // Don't need to do any further checks for admins on these paths
      if (isAdmin && 
         (location.pathname.includes('/validate-idea/results') || 
          location.pathname.includes('/roadmap'))) {
        return;
      }
    }
  }, [isLoading, isAuthenticated, isAdmin, navigate, requireAdmin, location.pathname]);

  if (isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <Loader2 className="h-8 w-8 animate-spin text-primary" />
      </div>
    );
  }

  return <>{typeof children === "function" ? children({ isAdmin }) : children}</>;
}
