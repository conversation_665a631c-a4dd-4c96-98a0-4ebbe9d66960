
import { But<PERSON> } from "@/components/ui/button";
import { ArrowRight } from "lucide-react";

interface CTAButtonProps {
  children: React.ReactNode;
  onClick?: () => void;
}

export const CTAButton = ({ children, onClick }: CTAButtonProps) => {
  return (
    <Button
      onClick={onClick}
      className="bg-slate-900 text-white hover:bg-slate-800 transition-all duration-300 group"
    >
      {children}
      <ArrowRight className="ml-2 h-4 w-4 transform group-hover:translate-x-1 transition-transform" />
    </Button>
  );
};
