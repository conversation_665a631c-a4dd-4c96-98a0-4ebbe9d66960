
import { ArrowUpRight } from "lucide-react";
import type { ShowcaseItemProps } from "./types";

interface Props {
  item: ShowcaseItemProps;
}

export const ShowcaseItem = ({ item }: Props) => {
  return (
    <div className="group relative bg-white dark:bg-gray-800 rounded-xl overflow-hidden shadow-sm hover:shadow-xl transition-all duration-300">
      <div className="p-6">
        <div className="flex justify-between items-start mb-4">
          <h3 className="text-xl font-bold text-gray-900 dark:text-gray-100">
            {item.title}
          </h3>
          <a
            href={item.link}
            target="_blank"
            rel="noopener noreferrer"
            className="p-2 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors"
          >
            <ArrowUpRight className="h-5 w-5 text-gray-500 dark:text-gray-400" />
          </a>
        </div>
        
        <p className="text-gray-600 dark:text-gray-300 mb-4">
          {item.description}
        </p>

        <div className="grid grid-cols-3 gap-4 mb-6">
          <div>
            <p className="text-sm text-gray-500 dark:text-gray-400">Users</p>
            <p className="font-semibold text-gray-900 dark:text-gray-100">{item.stats.users}</p>
          </div>
          <div>
            <p className="text-sm text-gray-500 dark:text-gray-400">Timeline</p>
            <p className="font-semibold text-gray-900 dark:text-gray-100">{item.stats.timeframe}</p>
          </div>
          <div>
            <p className="text-sm text-gray-500 dark:text-gray-400">Impact</p>
            <p className="font-semibold text-gray-900 dark:text-gray-100">{item.stats.improvement}</p>
          </div>
        </div>

        <div className="flex flex-wrap gap-2 mb-6">
          {item.tags.map((tag, index) => (
            <span
              key={index}
              className="px-3 py-1 text-sm bg-indigo-50 dark:bg-indigo-900/30 text-indigo-600 dark:text-indigo-400 rounded-full"
            >
              {tag}
            </span>
          ))}
        </div>

        <ul className="space-y-2">
          {item.features.map((feature, index) => (
            <li key={index} className="flex items-center text-gray-600 dark:text-gray-300">
              <span className="w-1.5 h-1.5 bg-indigo-500 rounded-full mr-2"></span>
              {feature}
            </li>
          ))}
        </ul>
      </div>
    </div>
  );
};
