
import { useEffect, useState } from 'react';
import { cn } from '@/lib/utils';
import Mermaid<PERSON>enderer from './MermaidRenderer';

interface MermaidDiagramProps {
  definition: string;
  className?: string;
}

export function MermaidDiagram({ definition, className }: MermaidDiagramProps) {
  const [diagramType, setDiagramType] = useState<string>('default');
  const [scale, setScale] = useState(1);
  const [isDragging, setIsDragging] = useState(false);
  const [position, setPosition] = useState({ x: 0, y: 0 });
  const [startPosition, setStartPosition] = useState({ x: 0, y: 0 });

  useEffect(() => {
    // Detect diagram type from definition
    const detectDiagramType = () => {
      if (definition.startsWith('sequenceDiagram')) {
        return 'sequence';
      } else if (definition.startsWith('quadrantChart')) {
        return 'quadrant';
      } else {
        return 'default';
      }
    };

    setDiagramType(detectDiagramType());
    // Reset zoom and position when definition changes
    setScale(1);
    setPosition({ x: 0, y: 0 });
  }, [definition]);

  // Handle zooming with wheel
  const handleWheel = (e: React.WheelEvent) => {
    e.preventDefault();
    const delta = e.deltaY * -0.01;
    const newScale = Math.min(Math.max(0.5, scale + delta), 2);
    setScale(newScale);
  };

  // Handle drag start
  const handleMouseDown = (e: React.MouseEvent) => {
    setIsDragging(true);
    setStartPosition({
      x: e.clientX - position.x,
      y: e.clientY - position.y
    });
  };

  // Handle dragging
  const handleMouseMove = (e: React.MouseEvent) => {
    if (!isDragging) return;
    
    setPosition({
      x: e.clientX - startPosition.x,
      y: e.clientY - startPosition.y
    });
  };

  // Handle drag end
  const handleMouseUp = () => {
    setIsDragging(false);
  };

  // Handle mouse leave
  const handleMouseLeave = () => {
    setIsDragging(false);
  };

  return (
    <div 
      className={cn(
        "relative overflow-auto border rounded-md bg-white p-2",
        className
      )}
      onWheel={handleWheel}
      onMouseDown={handleMouseDown}
      onMouseMove={handleMouseMove}
      onMouseUp={handleMouseUp}
      onMouseLeave={handleMouseLeave}
      style={{ cursor: isDragging ? 'grabbing' : 'grab' }}
    >
      <div 
        className="min-h-[200px] flex items-center justify-center"
        style={{
          transform: `scale(${scale}) translate(${position.x / scale}px, ${position.y / scale}px)`,
          transformOrigin: 'center center'
        }}
      >
        <MermaidRenderer definition={definition} />
      </div>
      
      <div className="absolute bottom-2 right-2 flex gap-1 bg-white/80 p-1 rounded-md shadow-sm">
        <button 
          onClick={() => setScale(prev => Math.min(prev + 0.1, 2))}
          className="w-6 h-6 flex items-center justify-center rounded hover:bg-gray-100"
          title="Zoom in"
        >
          +
        </button>
        <button 
          onClick={() => setScale(prev => Math.max(prev - 0.1, 0.5))}
          className="w-6 h-6 flex items-center justify-center rounded hover:bg-gray-100"
          title="Zoom out"
        >
          -
        </button>
        <button 
          onClick={() => {
            setScale(1);
            setPosition({ x: 0, y: 0 });
          }}
          className="w-6 h-6 flex items-center justify-center rounded hover:bg-gray-100"
          title="Reset view"
        >
          ↺
        </button>
      </div>
    </div>
  );
}
