
import React from 'react';
import { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>Tit<PERSON> } from "@/components/ui/card";
import { ExternalLink } from "lucide-react";
import { Badge } from "@/components/ui/badge";
import { cn } from "@/lib/utils";

interface Citation {
  url: string;
  title: string;
  source: string;
  relevance: string;
  date?: string;
}

interface CitationSectionProps {
  citations: Citation[] | any[];
  title: string;
  className?: string;
}

export const CitationSection = ({ citations, title, className }: CitationSectionProps) => {
  if (!citations || !Array.isArray(citations) || citations.length === 0) {
    console.log(`No citations available for ${title}:`, citations);
    return null;
  }

  // Log citations for debugging
  console.log(`Rendering citations for ${title}:`, citations);

  return (
    <Card className={cn("mt-6 border border-primary/10", className)}>
      <CardHeader className="pb-3">
        <CardTitle className="text-base font-medium flex items-center gap-2">
          Citations for {title}
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          {citations.map((citation, index) => {
            // Skip invalid citations
            if (!citation || typeof citation !== 'object' || !citation.url || !citation.title) {
              console.warn(`Invalid citation at index ${index} for ${title}`, citation);
              return null;
            }
            
            return (
              <div key={index} className="flex flex-col sm:flex-row sm:items-start gap-3 pb-4 last:pb-0 border-b last:border-0">
                <div className="flex-1 space-y-1">
                  <div className="flex items-start justify-between gap-2">
                    <a
                      href={citation.url}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="text-sm font-medium hover:underline flex items-center gap-1 text-primary"
                    >
                      {citation.title}
                      <ExternalLink className="h-3 w-3" />
                    </a>
                    {citation.source && (
                      <Badge variant="outline" className="shrink-0">
                        {citation.source}
                      </Badge>
                    )}
                  </div>
                  {citation.relevance && (
                    <p className="text-sm text-muted-foreground">{citation.relevance}</p>
                  )}
                  {citation.date && (
                    <p className="text-xs text-muted-foreground">Published: {citation.date}</p>
                  )}
                </div>
              </div>
            );
          })}
        </div>
      </CardContent>
    </Card>
  );
};
