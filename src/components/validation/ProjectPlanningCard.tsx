import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON>, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { FileText, Globe, Smartphone, Laptop, Code, Server, Database, Calendar, DollarSign, User, Palette, GitBranch } from "lucide-react";

const ensureArray = (data: any): any[] => {
  if (!data) return [];
  return Array.isArray(data) ? data : [data];
};

const safeToString = (value: any): string => {
  if (value === null || value === undefined) return '';
  return String(value);
};

const platformIcons = {
  web: <Globe className="h-4 w-4" />,
  ios: <Smartphone className="h-4 w-4" />,
  mobile: <Smartphone className="h-4 w-4" />,
  desktop: <Laptop className="h-4 w-4" />,
};

const techIcons = {
  react: <Code className="h-4 w-4" />,
  node: <Server className="h-4 w-4" />,
  nodejs: <Server className="h-4 w-4" />,
  postgres: <Database className="h-4 w-4" />,
  postgresql: <Database className="h-4 w-4" />,
  aws: <Server className="h-4 w-4" />,
  swift: <Code className="h-4 w-4" />,
  kotlin: <Code className="h-4 w-4" />,
};

interface ProjectPlanningCardProps {
  planningData: any;
  validationId: string;
  navigate: (path: string) => void;
}

export function ProjectPlanningCard({ planningData, validationId, navigate }: ProjectPlanningCardProps) {
  const handleEditPlanning = () => {
    console.log("Navigating to planning with ID:", validationId);
    navigate(`/validate-idea/results/${validationId}/planning`);
  };

  return (
    <Card className="mb-8 border border-primary/10 shadow-md transition-all hover:shadow-lg">
      <CardHeader className="bg-gradient-to-r from-primary/5 to-transparent pb-3 flex flex-row justify-between items-center">
        <div>
          <CardTitle className="text-xl font-bold">Project Planning</CardTitle>
          <CardDescription>You've completed project planning for this idea</CardDescription>
        </div>
        <Button 
          variant="outline" 
          size="sm"
          className="hover:bg-primary/10 gap-2 transition-all duration-200 transform hover:scale-105"
          onClick={handleEditPlanning}
        >
          <FileText className="h-4 w-4" />
          Edit Planning
        </Button>
      </CardHeader>
      <CardContent>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {planningData.platforms && (
            <div className="space-y-3">
              <h4 className="font-medium text-base flex items-center gap-2">
                <Globe className="h-5 w-5 text-blue-500" />
                Selected Platforms
              </h4>
              <div className="flex flex-wrap gap-2">
                {ensureArray(planningData.platforms).map((platform: any, i: number) => {
                  const platformKey = platform.toLowerCase();
                  const icon = platformIcons[platformKey] || <Globe className="h-4 w-4" />;
                  
                  return (
                    <div 
                      key={i} 
                      className="bg-slate-50 dark:bg-slate-800 py-2 px-4 rounded-full flex items-center gap-2 shadow-sm"
                    >
                      <span className="bg-blue-100 dark:bg-blue-900/50 p-1.5 rounded-full flex items-center justify-center">
                        {icon}
                      </span>
                      <span className="text-sm font-medium">{safeToString(platform)}</span>
                    </div>
                  );
                })}
              </div>
            </div>
          )}
          
          {planningData.development_phases && (
            <div className="space-y-3">
              <h4 className="font-medium text-base flex items-center gap-2">
                <GitBranch className="h-5 w-5 text-violet-500" />
                Development Phases
              </h4>
              <div className="flex flex-wrap gap-2">
                {ensureArray(planningData.development_phases).map((phase: any, i: number) => (
                  <Badge 
                    key={i} 
                    variant="outline"
                    className="py-1.5 px-3 border-violet-200 bg-violet-50 dark:bg-violet-900/20 hover:bg-violet-100 dark:hover:bg-violet-900/30 transition-colors"
                  >
                    {safeToString(phase)}
                  </Badge>
                ))}
              </div>
            </div>
          )}
          
          {planningData.technology_stack && (
            <div className="space-y-3">
              <h4 className="font-medium text-base flex items-center gap-2">
                <Code className="h-5 w-5 text-emerald-500" />
                Technology Stack
              </h4>
              <div className="flex flex-wrap gap-2">
                {ensureArray(planningData.technology_stack).map((tech: any, i: number) => (
                  <Badge 
                    key={i} 
                    variant="outline"
                    className="py-1.5 px-3 border-emerald-200 bg-emerald-50 dark:bg-emerald-900/20 hover:bg-emerald-100 dark:hover:bg-emerald-900/30 transition-colors flex items-center gap-1.5"
                  >
                    {techIcons[tech.toLowerCase()] || <Code className="h-4 w-4" />}
                    {safeToString(tech)}
                  </Badge>
                ))}
              </div>
            </div>
          )}
          
          {planningData.branding_preferences && (
            <div className="space-y-3">
              <h4 className="font-medium text-base flex items-center gap-2">
                <Palette className="h-5 w-5 text-rose-500" />
                Branding Preferences
              </h4>
              <div className="flex flex-wrap gap-2">
                {ensureArray(planningData.branding_preferences).map((pref: any, i: number) => (
                  <Badge 
                    key={i} 
                    variant="outline"
                    className="py-1.5 px-3 border-rose-200 bg-rose-50 dark:bg-rose-900/20 hover:bg-rose-100 dark:hover:bg-rose-900/30 transition-colors"
                  >
                    {safeToString(pref)}
                  </Badge>
                ))}
              </div>
            </div>
          )}
          
          {planningData.monthly_users && (
            <div className="space-y-3">
              <h4 className="font-medium text-base flex items-center gap-2">
                <User className="h-5 w-5 text-amber-500" />
                Monthly Users
              </h4>
              <Badge 
                variant="secondary"
                className="py-1.5 px-3 bg-amber-50 text-amber-700 dark:bg-amber-900/20 dark:text-amber-400 border border-amber-200"
              >
                {safeToString(planningData.monthly_users)}
              </Badge>
            </div>
          )}
          
          {planningData.timeline && (
            <div className="space-y-3">
              <h4 className="font-medium text-base flex items-center gap-2">
                <Calendar className="h-5 w-5 text-sky-500" />
                Timeline
              </h4>
              <Badge 
                variant="secondary"
                className="py-1.5 px-3 bg-sky-50 text-sky-700 dark:bg-sky-900/20 dark:text-sky-400 border border-sky-200"
              >
                {safeToString(planningData.timeline)}
              </Badge>
            </div>
          )}
          
          {planningData.budget_range && (
            <div className="space-y-3">
              <h4 className="font-medium text-base flex items-center gap-2">
                <DollarSign className="h-5 w-5 text-green-500" />
                Budget Range
              </h4>
              <Badge 
                variant="secondary"
                className="py-1.5 px-3 bg-green-50 text-green-700 dark:bg-green-900/20 dark:text-green-400 border border-green-200"
              >
                {safeToString(planningData.budget_range)}
              </Badge>
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  );
}
