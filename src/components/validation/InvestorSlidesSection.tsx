import React, { useState } from "react";
import { <PERSON>, CardContent } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { ScrollArea } from "@/components/ui/scroll-area";
import { 
  ChevronLeft, 
  ChevronRight, 
  Maximize2, 
  Download,
  Play,
  Pause,
  RotateCcw,
  Lightbulb, 
  Users, 
  Target, 
  TrendingUp, 
  BarChart3, 
  ShieldCheck, 
  DollarSign, 
  Rocket, 
  LineChart,
  Clock,
  CreditCard,
  Award,
  ArrowRight,
  CheckCircle,
  Sparkles,
  Zap,
  Globe,
  Star
} from "lucide-react";
import { cn } from "@/lib/utils";

interface InvestorSlidesSectionProps {
  validationData: any;
}

interface Slide {
  id: number;
  title: string;
  subtitle: string;
  icon: React.ComponentType<any>;
  content: React.ReactNode;
}

export const InvestorSlidesSection: React.FC<InvestorSlidesSectionProps> = ({ validationData }) => {
  const [currentSlide, setCurrentSlide] = useState(0);
  const [isAutoPlay, setIsAutoPlay] = useState(false);
  const [isFullscreen, setIsFullscreen] = useState(false);

  // Helper function to truncate text
  const truncateText = (text: string, maxLength: number) => {
    if (!text) return "";
    return text.length > maxLength ? `${text.substring(0, maxLength)}...` : text;
  };

  // Extract market size numbers from Market Size Analysis
  const extractMarketNumber = (text: string) => {
    if (!text) return "$N/A";
    
    // More flexible patterns to match various monetary formats
    const patterns = [
      // Standard formats: "$X billion", "USD X billion", "X billion USD"
      /(?:USD?\s*)?(\d+(?:\.\d+)?)\s*billion/i,
      /(\d+(?:\.\d+)?)\s*billion\s*(?:USD?)?/i,
      
      // Million patterns
      /(?:USD?\s*)?(\d+(?:\.\d+)?)\s*million/i,
      /(\d+(?:\.\d+)?)\s*million\s*(?:USD?)?/i,
      
      // Trillion patterns
      /(?:USD?\s*)?(\d+(?:\.\d+)?)\s*trillion/i,
      /(\d+(?:\.\d+)?)\s*trillion\s*(?:USD?)?/i,
      
      // Direct dollar amounts: $X.XB, $X.XM
      /\$(\d+(?:\.\d+)?)\s*B/i,
      /\$(\d+(?:\.\d+)?)\s*M/i,
      /\$(\d+(?:\.\d+)?)\s*T/i,
      
      // Valued at patterns: "valued at $X billion"
      /valued\s+at\s+(?:USD?\s*)?(\d+(?:\.\d+)?)\s*billion/i,
      /valued\s+at\s+(?:USD?\s*)?(\d+(?:\.\d+)?)\s*million/i,
      
      // Market size patterns: "market size of $X billion"
      /market\s+size\s+of\s+(?:USD?\s*)?(\d+(?:\.\d+)?)\s*billion/i,
      /market\s+size\s+of\s+(?:USD?\s*)?(\d+(?:\.\d+)?)\s*million/i,
    ];
    
    for (const pattern of patterns) {
      const match = text.match(pattern);
      if (match) {
        const value = parseFloat(match[1]);
        
        // Determine unit based on pattern
        if (pattern.source.includes('billion') || pattern.source.includes('B')) {
          return `$${value}B`;
        } else if (pattern.source.includes('million') || pattern.source.includes('M')) {
          return value >= 1000 ? `$${(value / 1000).toFixed(1)}B` : `$${value}M`;
        } else if (pattern.source.includes('trillion') || pattern.source.includes('T')) {
          return `$${value}T`;
        }
      }
    }
    
    return "$N/A";
  };

  const getTAMNumber = () => {
    const marketSize = validationData.marketValidation?.marketSize;
    if (!marketSize) return "$N/A";
    
    if (typeof marketSize === "object") {
      return extractMarketNumber(marketSize.totalAddressableMarket);
    }
    
    return extractMarketNumber(marketSize);
  };

  const getSAMNumber = () => {
    const marketSize = validationData.marketValidation?.marketSize;
    if (!marketSize) return "$N/A";
    
    if (typeof marketSize === "object" && marketSize.serviceableAddressableMarket) {
      return extractMarketNumber(marketSize.serviceableAddressableMarket);
    }
    
    return "$N/A";
  };

  const getSOMNumber = () => {
    const marketSize = validationData.marketValidation?.marketSize;
    if (!marketSize) return "$N/A";
    
    if (typeof marketSize === "object" && marketSize.serviceableObtainableMarket) {
      return extractMarketNumber(marketSize.serviceableObtainableMarket);
    }
    
    return "$N/A";
  };

  // Get market analysis text for justification
  const getMarketJustification = () => {
    const marketSize = validationData.marketValidation?.marketSize;
    if (!marketSize) return "Large and rapidly growing market with strong demand for innovative solutions.";
    
    if (typeof marketSize === "object") {
      return marketSize.totalAddressableMarket || validationData.marketValidation?.targetMarket || "Large and rapidly growing market with strong demand for innovative solutions.";
    }
    
    return marketSize;
  };

  const slides: Slide[] = [
    // Slide 1: Title Slide
    {
      id: 1,
      title: validationData.projectConcept?.overview?.split('.')[0] || "Your Startup",
      subtitle: "Investor Presentation",
      icon: Rocket,
      content: (
        <div className="h-full flex flex-col justify-center items-center p-8">
          <div className="text-center mb-6">
            <div className="relative inline-block mb-4">
              <Rocket className="h-16 w-16 text-[#4F46E5] mx-auto animate-bounce" />
              <div className="absolute -top-1 -right-1">
                <Sparkles className="h-4 w-4 text-[#F59E0B] animate-spin" />
              </div>
            </div>
            
            <h1 className="text-4xl font-black text-gray-900 mb-3 bg-gradient-to-r from-[#4F46E5] to-[#7C3AED] bg-clip-text text-transparent">
              {truncateText(validationData.projectConcept?.overview?.split('.')[0] || "Your Startup", 25)}
            </h1>
            <div className="w-20 h-1 bg-gradient-to-r from-[#4F46E5] via-[#7C3AED] to-[#EC4899] mx-auto rounded-full mb-2"></div>
            <p className="text-lg text-gray-600 font-medium">Investor Presentation</p>
          </div>
          
          <div className="bg-white/90 backdrop-blur-sm rounded-xl p-6 shadow-lg border border-[#4F46E5]/10 max-w-4xl">
            <div className="text-center">
              <h2 className="text-xl font-bold text-[#4F46E5] mb-3">Value Proposition</h2>
              <p className="text-base leading-relaxed text-gray-700 mb-4">
                {truncateText(validationData.marketValidation?.uniqueValue || "Revolutionizing the industry with innovative solutions", 100)}
              </p>
              
              <div className="flex items-center justify-center gap-4">
                <Badge className="px-4 py-2 text-sm bg-gradient-to-r from-[#4F46E5] to-[#7C3AED] text-white border-0">
                  <Star className="w-3 h-3 mr-2" />
                  Seed Round
                </Badge>
                <Badge variant="outline" className="px-4 py-2 text-sm border border-[#4F46E5]/30 text-[#4F46E5] bg-[#4F46E5]/5">
                  <Globe className="w-3 h-3 mr-2" />
                  {new Date().getFullYear()}
                </Badge>
              </div>
            </div>
          </div>
        </div>
      )
    },

    // Slide 2: Problem Statement
    {
      id: 2,
      title: "The Problem",
      subtitle: "What we're solving",
      icon: ShieldCheck,
      content: (
        <div className="h-full flex flex-col justify-center p-8">
          <div className="text-center mb-6">
            <ShieldCheck className="h-16 w-16 text-[#EF4444] mx-auto mb-3" />
            <h1 className="text-3xl font-black text-gray-900 mb-2 bg-gradient-to-r from-[#EF4444] to-[#F97316] bg-clip-text text-transparent">
              The Problem
            </h1>
            <p className="text-base text-gray-600">Critical market challenges</p>
          </div>
          
          <div className="bg-white/90 backdrop-blur-sm rounded-xl p-6 shadow-lg border border-[#EF4444]/10 max-w-5xl mx-auto">
            <div className="grid lg:grid-cols-2 gap-6">
              <div>
                <h2 className="text-lg font-bold text-[#4F46E5] mb-3 flex items-center">
                  <Zap className="h-5 w-5 mr-2" />
                  Current Challenge
                </h2>
                <p className="text-sm leading-relaxed text-gray-700">
                  {validationData.projectConcept?.overview ? 
                    truncateText(validationData.projectConcept.overview.split('.')[0] + ".", 120) : 
                    "Market lacks efficient solutions for key user needs."}
                </p>
              </div>
              
              {validationData.targetUsers && validationData.targetUsers.length > 0 && (
                <div>
                  <h2 className="text-lg font-bold text-[#4F46E5] mb-3">Key Pain Points</h2>
                  <div className="space-y-2">
                    {validationData.targetUsers[0]?.painPoints?.slice(0, 3).map((point: string, i: number) => (
                      <div key={i} className="flex items-start space-x-2 p-2 bg-[#4F46E5]/5 rounded-lg">
                        <div className="w-6 h-6 bg-gradient-to-br from-[#4F46E5] to-[#7C3AED] rounded-md flex items-center justify-center flex-shrink-0">
                          <span className="text-white font-bold text-xs">{i + 1}</span>
                        </div>
                        <p className="text-gray-700 text-xs pt-1">{truncateText(point, 50)}</p>
                      </div>
                    ))}
                  </div>
                </div>
              )}
            </div>
          </div>
        </div>
      )
    },

    // Slide 3: Solution
    {
      id: 3,
      title: "Our Solution",
      subtitle: "How we solve it",
      icon: Lightbulb,
      content: (
        <div className="h-full flex flex-col justify-center p-8">
          <div className="text-center mb-6">
            <Lightbulb className="h-16 w-16 text-[#F59E0B] mx-auto mb-3" />
            <h1 className="text-3xl font-black text-gray-900 mb-2 bg-gradient-to-r from-[#F59E0B] to-[#F97316] bg-clip-text text-transparent">
              Our Solution
            </h1>
            <p className="text-base text-gray-600">Innovation that makes a difference</p>
          </div>
          
          <div className="max-w-5xl mx-auto">
            <div className="bg-white/90 backdrop-blur-sm rounded-xl p-6 shadow-lg border border-[#F59E0B]/10">
              <div className="mb-4">
                <h2 className="text-lg font-bold text-[#4F46E5] mb-2 text-center">Value Proposition</h2>
                <p className="text-sm leading-relaxed text-gray-700 text-center">
                  {truncateText(validationData.marketValidation?.uniqueValue || "Innovative solution addressing key market gaps with cutting-edge technology.", 100)}
                </p>
              </div>
              
              <h2 className="text-lg font-bold text-[#4F46E5] mb-3 text-center">Core Features</h2>
              <div className="grid grid-cols-2 gap-3">
                {validationData.projectConcept?.features?.core?.slice(0, 4).map((feature: any, i: number) => (
                  <div key={i} className="bg-gradient-to-br from-[#4F46E5]/10 to-[#7C3AED]/10 rounded-lg p-3 border border-[#4F46E5]/20">
                    <div className="flex items-start space-x-2">
                      <div className="w-6 h-6 bg-gradient-to-br from-[#4F46E5] to-[#7C3AED] rounded-md flex items-center justify-center flex-shrink-0">
                        <span className="text-white font-bold text-xs">{i + 1}</span>
                      </div>
                      <div>
                        <h3 className="font-bold text-gray-900 mb-1 text-xs">Feature {i + 1}</h3>
                        <p className="text-gray-700 text-xs">{truncateText(feature.description, 40)}</p>
                      </div>
                    </div>
                  </div>
                )) || (
                  <div className="col-span-2 text-center py-4">
                    <p className="text-gray-500 text-sm">Features will be populated from validation</p>
                  </div>
                )}
              </div>
            </div>
          </div>
        </div>
      )
    },

    // Slide 4: Market Opportunity
    {
      id: 4,
      title: "Market Opportunity",
      subtitle: "TAM, SAM & SOM Analysis",
      icon: Target,
      content: (
        <div className="h-full flex flex-col justify-center items-center p-8">
          {/* Clean TAM/SAM/SOM Visual - Numbers Only */}
          <div className="grid grid-cols-3 gap-8 mb-8">
            {/* TAM */}
            <div className="text-center">
              <div className="w-24 h-24 rounded-full border-4 border-[#4F46E5] bg-[#4F46E5]/10 flex items-center justify-center mb-3 mx-auto">
                <div className="text-center">
                  <div className="text-xs font-bold text-[#4F46E5]">TAM</div>
                </div>
              </div>
              <div className="text-lg font-black text-[#4F46E5] mb-1">{getTAMNumber()}</div>
              <div className="text-xs text-gray-600">Total Addressable Market</div>
            </div>
            
            {/* SAM */}
            <div className="text-center">
              <div className="w-24 h-24 rounded-full border-4 border-[#7C3AED] bg-[#7C3AED]/10 flex items-center justify-center mb-3 mx-auto">
                <div className="text-center">
                  <div className="text-xs font-bold text-[#7C3AED]">SAM</div>
                </div>
              </div>
              <div className="text-lg font-black text-[#7C3AED] mb-1">{getSAMNumber()}</div>
              <div className="text-xs text-gray-600">Serviceable Available Market</div>
            </div>
            
            {/* SOM */}
            <div className="text-center">
              <div className="w-24 h-24 rounded-full border-4 border-[#10B981] bg-[#10B981]/10 flex items-center justify-center mb-3 mx-auto">
                <div className="text-center">
                  <div className="text-xs font-bold text-[#10B981]">SOM</div>
                </div>
              </div>
              <div className="text-lg font-black text-[#10B981] mb-1">{getSOMNumber()}</div>
              <div className="text-xs text-gray-600">Serviceable Obtainable Market</div>
            </div>
          </div>
          
          {/* Market Justification */}
          <div className="bg-white/90 backdrop-blur-sm rounded-xl p-4 shadow-lg border border-[#4F46E5]/10 max-w-4xl">
            <h3 className="text-lg font-bold text-[#4F46E5] mb-2 text-center">Market Justification</h3>
            <p className="text-sm text-gray-700 text-center leading-relaxed">
              {truncateText(getMarketJustification(), 200)}
            </p>
          </div>
        </div>
      )
    },

    // Slide 5: Business Model
    {
      id: 5,
      title: "Business Model",
      subtitle: "Revenue generation strategy",
      icon: DollarSign,
      content: (
        <div className="h-full flex flex-col justify-center p-8">
          <div className="text-center mb-6">
            <DollarSign className="h-16 w-16 text-[#10B981] mx-auto mb-3" />
            <h1 className="text-3xl font-black text-gray-900 mb-2 bg-gradient-to-r from-[#10B981] to-[#059669] bg-clip-text text-transparent">
              Business Model
            </h1>
            <p className="text-base text-gray-600">Revenue streams</p>
          </div>
          
          <div className="bg-white/90 backdrop-blur-sm rounded-xl p-6 shadow-lg border border-[#10B981]/10 max-w-5xl mx-auto">
            <h2 className="text-lg font-bold text-[#4F46E5] mb-4 text-center">Revenue Streams</h2>
            {validationData.marketValidation?.monetizationStrategies && validationData.marketValidation.monetizationStrategies.length > 0 ? (
              <div className="grid grid-cols-2 gap-4">
                {validationData.marketValidation.monetizationStrategies.slice(0, 4).map((strategy: string, i: number) => (
                  <div key={i} className="bg-gradient-to-br from-[#10B981]/10 to-[#059669]/10 rounded-lg p-3 border border-[#10B981]/20">
                    <div className="flex items-start space-x-2">
                      <div className="w-6 h-6 bg-gradient-to-br from-[#10B981] to-[#059669] rounded-md flex items-center justify-center flex-shrink-0">
                        <span className="text-white font-bold text-xs">{i + 1}</span>
                      </div>
                      <div>
                        <h3 className="font-bold text-gray-900 mb-1 text-xs">Stream {i + 1}</h3>
                        <p className="text-gray-700 text-xs">{truncateText(strategy, 60)}</p>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            ) : (
              <div className="grid grid-cols-3 gap-4">
                <div className="text-center bg-gradient-to-br from-[#10B981]/10 to-[#059669]/10 rounded-lg p-3 border border-[#10B981]/20">
                  <div className="w-10 h-10 bg-gradient-to-br from-[#10B981] to-[#059669] rounded-lg flex items-center justify-center mx-auto mb-2">
                    <Users className="h-5 w-5 text-white" />
                  </div>
                  <h3 className="font-bold text-[#4F46E5] mb-1 text-xs">Subscription</h3>
                  <p className="text-gray-600 text-xs">Recurring revenue</p>
                </div>
                <div className="text-center bg-gradient-to-br from-[#10B981]/10 to-[#059669]/10 rounded-lg p-3 border border-[#10B981]/20">
                  <div className="w-10 h-10 bg-gradient-to-br from-[#10B981] to-[#059669] rounded-lg flex items-center justify-center mx-auto mb-2">
                    <BarChart3 className="h-5 w-5 text-white" />
                  </div>
                  <h3 className="font-bold text-[#4F46E5] mb-1 text-xs">Transaction Fees</h3>
                  <p className="text-gray-600 text-xs">Platform commission</p>
                </div>
                <div className="text-center bg-gradient-to-br from-[#10B981]/10 to-[#059669]/10 rounded-lg p-3 border border-[#10B981]/20">
                  <div className="w-10 h-10 bg-gradient-to-br from-[#10B981] to-[#059669] rounded-lg flex items-center justify-center mx-auto mb-2">
                    <Star className="h-5 w-5 text-white" />
                  </div>
                  <h3 className="font-bold text-[#4F46E5] mb-1 text-xs">Premium Features</h3>
                  <p className="text-gray-600 text-xs">Advanced tools</p>
                </div>
              </div>
            )}
          </div>
        </div>
      )
    },

    // Slide 6: Competitive Advantage
    {
      id: 6,
      title: "Competitive Advantage",
      subtitle: "What makes us unique",
      icon: Award,
      content: (
        <div className="h-full flex flex-col justify-center p-8">
          <div className="text-center mb-6">
            <Award className="h-16 w-16 text-[#F59E0B] mx-auto mb-3" />
            <h1 className="text-3xl font-black text-gray-900 mb-2 bg-gradient-to-r from-[#F59E0B] to-[#F97316] bg-clip-text text-transparent">
              Competitive Advantage
            </h1>
            <p className="text-base text-gray-600">Why we'll win</p>
          </div>
          
          <div className="max-w-5xl mx-auto">
            <div className="bg-white/90 backdrop-blur-sm rounded-xl p-6 shadow-lg border border-[#F59E0B]/10">
              <div className="mb-4">
                <h2 className="text-lg font-bold text-[#4F46E5] mb-2 text-center">Unique Differentiators</h2>
                <p className="text-sm leading-relaxed text-gray-700 text-center">
                  {truncateText(validationData.marketValidation?.uniqueValue || "Cutting-edge technology with deep market understanding delivers unmatched value.", 120)}
                </p>
              </div>
              
              {validationData.marketValidation?.competitors && validationData.marketValidation.competitors.length > 0 && (
                <div>
                  <h2 className="text-lg font-bold text-[#4F46E5] mb-3 text-center">Competitor Gaps We Address</h2>
                  <div className="grid grid-cols-2 gap-3">
                    {validationData.marketValidation.competitors[0]?.weaknesses?.slice(0, 4).map((weakness: string, i: number) => (
                      <div key={i} className="flex items-start space-x-2 p-3 bg-gradient-to-br from-[#F59E0B]/10 to-[#F97316]/10 rounded-lg border border-[#F59E0B]/20">
                        <div className="w-6 h-6 bg-gradient-to-br from-[#10B981] to-[#059669] rounded-md flex items-center justify-center flex-shrink-0">
                          <CheckCircle className="h-3 w-3 text-white" />
                        </div>
                        <p className="text-gray-700 text-xs pt-1">{truncateText(weakness, 50)}</p>
                      </div>
                    ))}
                  </div>
                </div>
              )}
            </div>
          </div>
        </div>
      )
    },

    // Slide 7: Traction & Roadmap
    {
      id: 7,
      title: "Traction & Roadmap",
      subtitle: "Progress and future plans",
      icon: LineChart,
      content: (
        <div className="h-full flex flex-col justify-center p-6">
          <div className="text-center mb-4">
            <LineChart className="h-12 w-12 text-[#4F46E5] mx-auto mb-2" />
            <h1 className="text-2xl font-black text-gray-900 mb-1 bg-gradient-to-r from-[#4F46E5] to-[#7C3AED] bg-clip-text text-transparent">
              Traction & Roadmap
            </h1>
            <p className="text-sm text-gray-600">Development roadmap</p>
          </div>
          
          <div className="bg-white/90 backdrop-blur-sm rounded-lg p-4 shadow-lg border border-[#4F46E5]/10 max-w-4xl mx-auto">
            <h2 className="text-base font-bold text-[#4F46E5] mb-3 text-center">Development Phases</h2>
            <div className="grid grid-cols-3 gap-4">
              <div className="text-center">
                <div className="w-16 h-16 bg-gradient-to-br from-[#4F46E5] to-[#6366F1] rounded-lg flex items-center justify-center mx-auto mb-2">
                  <Clock className="h-8 w-8 text-white" />
                </div>
                <div className="bg-gradient-to-r from-[#4F46E5]/10 to-[#6366F1]/10 rounded-lg p-3 border border-[#4F46E5]/20">
                  <h3 className="text-xs font-bold text-gray-900 mb-1">Phase 1</h3>
                  <h4 className="text-xs font-semibold text-[#4F46E5] mb-1">Core Development</h4>
                  <p className="text-gray-700 text-xs">MVP & essential features</p>
                </div>
              </div>
              
              <div className="text-center">
                <div className="w-16 h-16 bg-gradient-to-br from-[#10B981] to-[#059669] rounded-lg flex items-center justify-center mx-auto mb-2">
                  <Users className="h-8 w-8 text-white" />
                </div>
                <div className="bg-gradient-to-r from-[#10B981]/10 to-[#059669]/10 rounded-lg p-3 border border-[#10B981]/20">
                  <h3 className="text-xs font-bold text-gray-900 mb-1">Phase 2</h3>
                  <h4 className="text-xs font-semibold text-[#10B981] mb-1">Beta Testing</h4>
                  <p className="text-gray-700 text-xs">User feedback & refinement</p>
                </div>
              </div>
              
              <div className="text-center">
                <div className="w-16 h-16 bg-gradient-to-br from-[#7C3AED] to-[#EC4899] rounded-lg flex items-center justify-center mx-auto mb-2">
                  <Rocket className="h-8 w-8 text-white" />
                </div>
                <div className="bg-gradient-to-r from-[#7C3AED]/10 to-[#EC4899]/10 rounded-lg p-3 border border-[#7C3AED]/20">
                  <h3 className="text-xs font-bold text-gray-900 mb-1">Phase 3</h3>
                  <h4 className="text-xs font-semibold text-[#7C3AED] mb-1">Market Launch</h4>
                  <p className="text-gray-700 text-xs">Release & growth</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      )
    },

    // Slide 8: Funding Ask
    {
      id: 8,
      title: "Funding Ask",
      subtitle: "Investment opportunity",
      icon: CreditCard,
      content: (
        <div className="h-full flex flex-col justify-center p-6">
          <div className="text-center mb-4">
            <CreditCard className="h-12 w-12 text-[#EC4899] mx-auto mb-2" />
            <h1 className="text-2xl font-black text-gray-900 mb-1 bg-gradient-to-r from-[#EC4899] to-[#EF4444] bg-clip-text text-transparent">
              Funding Ask
            </h1>
            <p className="text-sm text-gray-600">Investment breakdown</p>
          </div>
          
          <div className="max-w-4xl mx-auto">
            {/* Use of Funds */}
            <div className="bg-white/90 backdrop-blur-sm rounded-lg p-4 shadow-lg border border-[#EC4899]/10 mb-3">
              <h2 className="text-base font-bold text-[#4F46E5] mb-3 text-center">Use of Funds</h2>
              <div className="grid grid-cols-4 gap-3">
                <div className="text-center">
                  <div className="w-14 h-14 bg-gradient-to-br from-[#4F46E5] to-[#6366F1] rounded-lg flex items-center justify-center mx-auto mb-2">
                    <span className="text-lg font-black text-white">50%</span>
                  </div>
                  <h3 className="font-bold text-gray-900 mb-1 text-xs">Product</h3>
                  <p className="text-gray-600 text-xs">Development</p>
                </div>
                <div className="text-center">
                  <div className="w-14 h-14 bg-gradient-to-br from-[#10B981] to-[#059669] rounded-lg flex items-center justify-center mx-auto mb-2">
                    <span className="text-lg font-black text-white">25%</span>
                  </div>
                  <h3 className="font-bold text-gray-900 mb-1 text-xs">Marketing</h3>
                  <p className="text-gray-600 text-xs">Acquisition</p>
                </div>
                <div className="text-center">
                  <div className="w-14 h-14 bg-gradient-to-br from-[#F59E0B] to-[#F97316] rounded-lg flex items-center justify-center mx-auto mb-2">
                    <span className="text-lg font-black text-white">15%</span>
                  </div>
                  <h3 className="font-bold text-gray-900 mb-1 text-xs">Operations</h3>
                  <p className="text-gray-600 text-xs">Team & infra</p>
                </div>
                <div className="text-center">
                  <div className="w-14 h-14 bg-gradient-to-br from-[#7C3AED] to-[#EC4899] rounded-lg flex items-center justify-center mx-auto mb-2">
                    <span className="text-lg font-black text-white">10%</span>
                  </div>
                  <h3 className="font-bold text-gray-900 mb-1 text-xs">Reserve</h3>
                  <p className="text-gray-600 text-xs">Strategic</p>
                </div>
              </div>
            </div>
            
            {/* Milestones */}
            <div className="bg-white/90 backdrop-blur-sm rounded-lg p-4 shadow-lg border border-[#EC4899]/10">
              <h2 className="text-base font-bold text-[#4F46E5] mb-3 text-center">Key Milestones</h2>
              <div className="grid grid-cols-3 gap-3">
                <div className="text-center p-2 bg-gradient-to-r from-[#4F46E5]/10 to-[#6366F1]/10 rounded-lg border border-[#4F46E5]/20">
                  <div className="w-8 h-8 bg-gradient-to-br from-[#4F46E5] to-[#6366F1] rounded-md flex items-center justify-center mx-auto mb-2">
                    <span className="text-white font-black text-xs">1</span>
                  </div>
                  <p className="text-gray-700 text-xs">MVP & initial customers</p>
                </div>
                <div className="text-center p-2 bg-gradient-to-r from-[#10B981]/10 to-[#059669]/10 rounded-lg border border-[#10B981]/20">
                  <div className="w-8 h-8 bg-gradient-to-br from-[#10B981] to-[#059669] rounded-md flex items-center justify-center mx-auto mb-2">
                    <span className="text-white font-black text-xs">2</span>
                  </div>
                  <p className="text-gray-700 text-xs">Product-market fit</p>
                </div>
                <div className="text-center p-2 bg-gradient-to-r from-[#7C3AED]/10 to-[#EC4899]/10 rounded-lg border border-[#7C3AED]/20">
                  <div className="w-8 h-8 bg-gradient-to-br from-[#7C3AED] to-[#EC4899] rounded-md flex items-center justify-center mx-auto mb-2">
                    <span className="text-white font-black text-xs">3</span>
                  </div>
                  <p className="text-gray-700 text-xs">Scale for Series A</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      )
    }
  ];

  const nextSlide = () => {
    setCurrentSlide((prev) => (prev + 1) % slides.length);
  };

  const prevSlide = () => {
    setCurrentSlide((prev) => (prev - 1 + slides.length) % slides.length);
  };

  const goToSlide = (index: number) => {
    setCurrentSlide(index);
  };

  // Auto-play functionality
  React.useEffect(() => {
    if (isAutoPlay) {
      const interval = setInterval(nextSlide, 8000); // 8 seconds per slide
      return () => clearInterval(interval);
    }
  }, [isAutoPlay]);

  if (!validationData) {
    return (
      <Card className="border shadow-sm">
        <CardContent className="pt-6">
          <p className="text-muted-foreground text-center">No validation data available to generate investor slides.</p>
        </CardContent>
      </Card>
    );
  }

  const currentSlideData = slides[currentSlide];

  return (
    <div className="space-y-6">
      {/* Modern Slide Controls */}
      <div className="flex flex-col sm:flex-row items-start sm:items-center justify-between gap-4 p-4 sm:p-6 bg-gradient-to-r from-white via-gray-50 to-white rounded-xl sm:rounded-2xl border shadow-lg backdrop-blur-sm">
        <div className="flex items-center space-x-3 sm:space-x-4">
          <Button
            variant="outline"
            size="sm"
            onClick={prevSlide}
            disabled={currentSlide === 0}
            className="rounded-lg sm:rounded-xl border-2 hover:scale-105 transition-all duration-200 border-[#4F46E5]/30 hover:border-[#4F46E5]"
          >
            <ChevronLeft className="h-3 w-3 sm:h-4 sm:w-4" />
          </Button>
          
          <div className="px-3 sm:px-4 py-1.5 sm:py-2 bg-gradient-to-r from-[#4F46E5] to-[#7C3AED] text-white rounded-lg sm:rounded-xl font-bold text-sm sm:text-base">
            {currentSlide + 1} / {slides.length}
          </div>
          
          <Button
            variant="outline"
            size="sm"
            onClick={nextSlide}
            disabled={currentSlide === slides.length - 1}
            className="rounded-lg sm:rounded-xl border-2 hover:scale-105 transition-all duration-200 border-[#4F46E5]/30 hover:border-[#4F46E5]"
          >
            <ChevronRight className="h-3 w-3 sm:h-4 sm:w-4" />
          </Button>
        </div>

        <div className="flex flex-wrap items-center gap-2 sm:gap-3">
          <Button
            variant="outline"
            size="sm"
            onClick={() => setIsAutoPlay(!isAutoPlay)}
            className={cn(
              "rounded-lg sm:rounded-xl border-2 hover:scale-105 transition-all duration-200 text-xs sm:text-sm",
              isAutoPlay ? "bg-[#10B981]/10 border-[#10B981]/30 text-[#10B981]" : "border-[#4F46E5]/30 hover:border-[#4F46E5]"
            )}
          >
            {isAutoPlay ? <Pause className="h-3 w-3 sm:h-4 sm:w-4 mr-1 sm:mr-2" /> : <Play className="h-3 w-3 sm:h-4 sm:w-4 mr-1 sm:mr-2" />}
            <span className="hidden sm:inline">{isAutoPlay ? "Pause" : "Auto Play"}</span>
          </Button>
          
          <Button
            variant="outline"
            size="sm"
            onClick={() => goToSlide(0)}
            className="rounded-lg sm:rounded-xl border-2 hover:scale-105 transition-all duration-200 border-[#4F46E5]/30 hover:border-[#4F46E5] text-xs sm:text-sm"
          >
            <RotateCcw className="h-3 w-3 sm:h-4 sm:w-4 sm:mr-2" />
            <span className="hidden sm:inline">Reset</span>
          </Button>
          
          <Button
            variant="outline"
            size="sm"
            onClick={() => setIsFullscreen(!isFullscreen)}
            className="rounded-lg sm:rounded-xl border-2 hover:scale-105 transition-all duration-200 border-[#4F46E5]/30 hover:border-[#4F46E5] text-xs sm:text-sm"
          >
            <Maximize2 className="h-3 w-3 sm:h-4 sm:w-4 sm:mr-2" />
            <span className="hidden sm:inline">{isFullscreen ? "Exit" : "Fullscreen"}</span>
          </Button>
          
          <Button
            variant="outline"
            size="sm"
            className="rounded-lg sm:rounded-xl border-2 hover:scale-105 transition-all duration-200 bg-gradient-to-r from-[#4F46E5]/10 to-[#7C3AED]/10 border-[#4F46E5]/30 hover:border-[#4F46E5] text-xs sm:text-sm"
          >
            <Download className="h-3 w-3 sm:h-4 sm:w-4 sm:mr-2" />
            <span className="hidden sm:inline">Export</span>
          </Button>
        </div>
      </div>

      {/* Modern Navigation Dots */}
      <div className="flex justify-center space-x-2 sm:space-x-3">
        {slides.map((slide, index) => (
          <button
            key={index}
            onClick={() => goToSlide(index)}
            className={cn(
              "transition-all duration-300 rounded-full",
              index === currentSlide
                ? "w-6 h-2.5 sm:w-8 sm:h-3 bg-gradient-to-r from-[#4F46E5] to-[#7C3AED] shadow-lg"
                : "w-2.5 h-2.5 sm:w-3 sm:h-3 bg-gray-300 hover:bg-[#4F46E5]/40 hover:scale-125"
            )}
          />
        ))}
      </div>

      {/* Main Slide with Modern Design */}
      <div 
        className={cn(
          "relative overflow-hidden rounded-2xl sm:rounded-3xl shadow-2xl transition-all duration-500 border border-[#4F46E5]/20",
          isFullscreen ? "fixed inset-0 z-50 rounded-none border-0" : "aspect-[16/9] bg-white max-h-[70vh]"
        )}
      >
        <div className="w-full h-full relative bg-gradient-to-br from-white via-gray-50/50 to-white">
          <div className="absolute inset-0 bg-gradient-to-br from-[#4F46E5]/5 via-transparent to-[#7C3AED]/5"></div>
          
          {/* Modern Slide Title Overlay */}
          <div className="absolute top-0 left-0 right-0 bg-gradient-to-r from-[#4F46E5]/95 via-[#4F46E5]/90 to-[#7C3AED]/95 text-white p-2 sm:p-3 backdrop-blur-sm z-20">
            <div className="flex items-center space-x-2 sm:space-x-3">
              <div className="w-6 h-6 sm:w-8 sm:h-8 bg-white/20 rounded-lg flex items-center justify-center backdrop-blur-sm">
                <currentSlideData.icon className="h-3 w-3 sm:h-4 sm:w-4 text-white" />
              </div>
              <div>
                <h3 className="font-bold text-xs sm:text-sm">{currentSlideData.title}</h3>
                <p className="text-xs text-gray-200">{currentSlideData.subtitle}</p>
              </div>
            </div>
          </div>
          
          {/* Content Area - properly constrained */}
          <div className="relative z-10 w-full h-full pt-12 sm:pt-14">
            <div className="w-full h-full overflow-hidden">
              {currentSlideData.content}
            </div>
          </div>
        </div>
      </div>

      {/* Modern Slide Thumbnails */}
      <div className="grid grid-cols-4 md:grid-cols-8 gap-2 sm:gap-3">
        {slides.map((slide, index) => (
          <button
            key={slide.id}
            onClick={() => goToSlide(index)}
            className={cn(
              "group aspect-video rounded-lg sm:rounded-xl border-2 transition-all duration-300 p-2 sm:p-3 hover:scale-105",
              index === currentSlide
                ? "border-[#4F46E5] bg-[#4F46E5]/10 shadow-lg scale-105"
                : "border-gray-200 hover:border-[#4F46E5]/50 bg-white hover:shadow-md"
            )}
          >
            <div className="bg-gradient-to-br from-[#4F46E5]/10 to-[#7C3AED]/10 w-full h-full rounded-md sm:rounded-lg flex items-center justify-center transition-all duration-200 group-hover:scale-110">
              <slide.icon className="h-3 w-3 sm:h-4 sm:w-4 lg:h-5 lg:w-5 text-[#4F46E5]" />
            </div>
            <p className="text-xs mt-1 sm:mt-2 truncate font-medium text-gray-700">{slide.title}</p>
          </button>
        ))}
      </div>
    </div>
  );
};