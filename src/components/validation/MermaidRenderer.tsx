
import { useEffect, useRef, useState } from 'react';
// Import our custom Mermaid entry point
import mermaid from '@/lib/mermaid-custom';

interface MermaidRendererProps {
  definition: string;
}

export default function MermaidRenderer({ definition }: MermaidRendererProps) {
  const containerRef = useRef<HTMLDivElement>(null);
  const [error, setError] = useState<string | null>(null);
  const [renderAttempt, setRenderAttempt] = useState(0);

  useEffect(() => {
    setError(null);

    // Render the diagram
    const renderDiagram = async () => {
      if (!containerRef.current) return;
      
      try {
        console.log("Mermaid render attempt:", renderAttempt);
        console.log("Definition:", definition);
        
        // Clear previous content
        containerRef.current.innerHTML = '';
        
        // Create a unique ID for this diagram
        const id = `mermaid-${Date.now()}-${Math.random().toString(36).substring(2, 10)}`;
        containerRef.current.innerHTML = `<div id="${id}">${definition}</div>`;
        
        // Render the diagram with a timeout
        const renderPromise = new Promise<{ svg: string }>((resolve, reject) => {
          // Set a timeout in case mermaid gets stuck
          const timeoutId = setTimeout(() => {
            reject(new Error('Diagram rendering timed out'));
          }, 5000);
          
          // Try to render
          mermaid.render(id, definition)
            .then(result => {
              clearTimeout(timeoutId);
              resolve(result);
            })
            .catch(err => {
              clearTimeout(timeoutId);
              reject(err);
            });
        });
        
        const { svg } = await renderPromise;
        containerRef.current.innerHTML = svg;
        console.log("Mermaid diagram rendered successfully");
      } catch (err) {
        console.error('Error rendering Mermaid diagram:', err);
        setError(`Failed to render diagram: ${err instanceof Error ? err.message : String(err)}`);
        
        // Try to render a simplified version if there's an error
        if (renderAttempt < 2) {
          console.log("Retrying with simpler diagram...");
          setRenderAttempt(prev => prev + 1);
        }
      }
    };
    
    // Initialize mermaid before rendering
    const initAndRender = async () => {
      try {
        await mermaid.initialize();
        await renderDiagram();
      } catch (error) {
        console.error("Failed to initialize Mermaid:", error);
        setError(`Failed to initialize diagram renderer: ${error instanceof Error ? error.message : String(error)}`);
      }
    };
    
    initAndRender();
  }, [definition, renderAttempt]);

  if (error) {
    return (
      <div className="p-4 text-red-500 bg-red-50 rounded-md">
        <p className="font-medium">Error rendering diagram</p>
        <p className="text-sm mt-1">{error}</p>
      </div>
    );
  }

  return <div ref={containerRef} className="w-full" />;
}
