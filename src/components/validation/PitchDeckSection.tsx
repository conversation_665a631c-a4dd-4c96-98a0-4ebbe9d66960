
import React from "react";
import { <PERSON>, Card<PERSON>ontent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { 
  Lightbulb, 
  Users, 
  Target, 
  TrendingUp, 
  BarChart3, 
  ShieldCheck, 
  DollarSign, 
  Rocket, 
  LineChart,
  Clock,
  CreditCard,
  Award
} from "lucide-react";

interface PitchDeckSectionProps {
  validationData: any;
}

export const PitchDeckSection: React.FC<PitchDeckSectionProps> = ({ validationData }) => {
  if (!validationData) {
    return (
      <Card className="border shadow-sm">
        <CardContent className="pt-6">
          <p className="text-muted-foreground text-center">No validation data available to generate pitch deck content.</p>
        </CardContent>
      </Card>
    );
  }

  // Helper function to limit text length with ellipsis
  const truncateText = (text: string, maxLength: number) => {
    if (!text) return "";
    return text.length > maxLength ? `${text.substring(0, maxLength)}...` : text;
  };

  // Extract market size data in a readable format
  const getMarketSizeText = () => {
    const marketSize = validationData.marketValidation?.marketSize;
    if (!marketSize) return "N/A";
    
    if (typeof marketSize === "object") {
      return marketSize.totalAddressableMarket || "N/A";
    }
    
    return marketSize;
  };

  return (
    <div className="space-y-8">
      {/* Slide 1: Problem Statement */}
      <Card className="border border-primary/10 shadow-sm hover:shadow-md transition-all duration-300">
        <CardHeader className="border-b border-border/40 bg-muted/30">
          <div className="flex items-center gap-3">
            <ShieldCheck className="h-6 w-6 text-red-500" />
            <CardTitle className="text-xl font-bold">Problem Statement</CardTitle>
          </div>
          <CardDescription className="text-base mt-2">What problem are you solving and for whom?</CardDescription>
        </CardHeader>
        <CardContent className="pt-6">
          <div className="space-y-6">
            <div>
              <h3 className="text-lg font-semibold mb-3 text-red-600">Current Challenge:</h3>
              <p className="text-base leading-relaxed">
                {validationData.projectConcept?.overview ? 
                  truncateText(validationData.projectConcept.overview.split('.')[0] + ".", 300) : 
                  "No problem statement available."}
              </p>
            </div>
            
            {validationData.targetUsers && validationData.targetUsers.length > 0 && (
              <div>
                <h3 className="text-lg font-semibold mb-3 text-red-600">Target User Pain Points:</h3>
                <ul className="list-disc pl-6 space-y-2">
                  {validationData.targetUsers[0]?.painPoints?.slice(0, 3).map((point: string, i: number) => (
                    <li key={i} className="text-base leading-relaxed">{point}</li>
                  ))}
                </ul>
              </div>
            )}
          </div>
        </CardContent>
      </Card>

      {/* Slide 2: Solution */}
      <Card className="border border-primary/10 shadow-sm hover:shadow-md transition-all duration-300">
        <CardHeader className="border-b border-border/40 bg-muted/30">
          <div className="flex items-center gap-3">
            <Lightbulb className="h-6 w-6 text-yellow-500" />
            <CardTitle className="text-xl font-bold">Solution</CardTitle>
          </div>
          <CardDescription className="text-base mt-2">How your product solves the problem</CardDescription>
        </CardHeader>
        <CardContent className="pt-6">
          <div className="space-y-6">
            <div>
              <h3 className="text-lg font-semibold mb-3 text-yellow-600">Value Proposition:</h3>
              <p className="text-base leading-relaxed">
                {validationData.marketValidation?.uniqueValue || "No unique value proposition available."}
              </p>
            </div>
            
            <div>
              <h3 className="text-lg font-semibold mb-3 text-yellow-600">Core Features:</h3>
              <div className="grid grid-cols-1 sm:grid-cols-2 gap-3">
                {validationData.projectConcept?.features?.core?.slice(0, 4).map((feature: any, i: number) => (
                  <div key={i} className="flex items-start gap-3">
                    <Badge variant="outline" className="bg-green-50 dark:bg-green-900/20 border-green-200 dark:border-green-700 h-6 min-w-6 flex items-center justify-center text-sm font-medium">
                      {i + 1}
                    </Badge>
                    <p className="text-base leading-relaxed">{truncateText(feature.description, 120)}</p>
                  </div>
                )) || (
                  <p className="text-base text-muted-foreground">No core features available.</p>
                )}
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Slide 3: Market Opportunity */}
      <Card className="border border-primary/10 shadow-sm hover:shadow-md transition-all duration-300">
        <CardHeader className="border-b border-border/40 bg-muted/30">
          <div className="flex items-center gap-3">
            <Target className="h-6 w-6 text-blue-500" />
            <CardTitle className="text-xl font-bold">Market Opportunity</CardTitle>
          </div>
          <CardDescription className="text-base mt-2">Size and potential of your target market</CardDescription>
        </CardHeader>
        <CardContent className="pt-6">
          <div className="space-y-6">
            <div className="grid grid-cols-1 sm:grid-cols-2 gap-6">
              <div className="border border-border/40 rounded-lg p-5 bg-muted/10">
                <h3 className="text-base font-semibold mb-3 flex items-center gap-2">
                  <BarChart3 className="h-5 w-5 text-blue-500" />
                  Total Addressable Market (TAM)
                </h3>
                <p className="text-2xl font-bold text-blue-600">{getMarketSizeText()}</p>
              </div>
              
              <div className="border border-border/40 rounded-lg p-5 bg-muted/10">
                <h3 className="text-base font-semibold mb-3 flex items-center gap-2">
                  <TrendingUp className="h-5 w-5 text-green-500" />
                  Market Growth Potential
                </h3>
                <p className="text-base leading-relaxed">
                  {validationData.marketValidation?.opportunities?.[0] || "Market growth data not available."}
                </p>
              </div>
            </div>
            
            <div>
              <h3 className="text-lg font-semibold mb-3 text-blue-600">Target Market:</h3>
              <p className="text-base leading-relaxed">
                {validationData.marketValidation?.targetMarket || "No target market information available."}
              </p>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Slide 4: Competitive Advantage */}
      <Card className="border border-primary/10 shadow-sm hover:shadow-md transition-all duration-300">
        <CardHeader className="border-b border-border/40 bg-muted/30">
          <div className="flex items-center gap-3">
            <Award className="h-6 w-6 text-amber-500" />
            <CardTitle className="text-xl font-bold">Competitive Advantage</CardTitle>
          </div>
          <CardDescription className="text-base mt-2">What makes your solution unique</CardDescription>
        </CardHeader>
        <CardContent className="pt-6">
          <div className="space-y-6">
            <div>
              <h3 className="text-lg font-semibold mb-3 text-amber-600">Unique Differentiators:</h3>
              <p className="text-base leading-relaxed">
                {validationData.marketValidation?.uniqueValue || "No unique value proposition available."}
              </p>
            </div>
            
            {validationData.marketValidation?.competitors && validationData.marketValidation.competitors.length > 0 && (
              <div>
                <h3 className="text-lg font-semibold mb-3 text-amber-600">Competitor Weaknesses We Address:</h3>
                <ul className="list-disc pl-6 space-y-2">
                  {validationData.marketValidation.competitors[0]?.weaknesses?.slice(0, 3).map((weakness: string, i: number) => (
                    <li key={i} className="text-base leading-relaxed">{weakness}</li>
                  ))}
                </ul>
              </div>
            )}
          </div>
        </CardContent>
      </Card>

      {/* Slide 5: Business Model */}
      <Card className="border border-primary/10 shadow-sm hover:shadow-md transition-all duration-300">
        <CardHeader className="border-b border-border/40 bg-muted/30">
          <div className="flex items-center gap-3">
            <DollarSign className="h-6 w-6 text-green-500" />
            <CardTitle className="text-xl font-bold">Business Model</CardTitle>
          </div>
          <CardDescription className="text-base mt-2">How you'll generate revenue</CardDescription>
        </CardHeader>
        <CardContent className="pt-6">
          <div className="space-y-6">
            {validationData.marketValidation?.monetizationStrategies && validationData.marketValidation.monetizationStrategies.length > 0 ? (
              <div>
                <h3 className="text-lg font-semibold mb-3 text-green-600">Revenue Streams:</h3>
                <ul className="list-disc pl-6 space-y-2">
                  {validationData.marketValidation.monetizationStrategies.map((strategy: string, i: number) => (
                    <li key={i} className="text-base leading-relaxed">{strategy}</li>
                  ))}
                </ul>
              </div>
            ) : (
              <p className="text-base text-muted-foreground">No monetization strategies available.</p>
            )}
          </div>
        </CardContent>
      </Card>

      {/* Slide 6: Go-to-Market Strategy */}
      <Card className="border border-primary/10 shadow-sm hover:shadow-md transition-all duration-300">
        <CardHeader className="border-b border-border/40 bg-muted/30">
          <div className="flex items-center gap-3">
            <Rocket className="h-6 w-6 text-purple-500" />
            <CardTitle className="text-xl font-bold">Go-to-Market Strategy</CardTitle>
          </div>
          <CardDescription className="text-base mt-2">How you'll reach and acquire customers</CardDescription>
        </CardHeader>
        <CardContent className="pt-6 space-y-6">
          <div>
            <h3 className="text-lg font-semibold mb-3 text-purple-600">Target Users:</h3>
            <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
              {validationData.targetUsers?.slice(0, 2).map((user: any, i: number) => (
                <div key={i} className="border border-border/40 rounded-lg p-5 bg-muted/10">
                  <h4 className="font-semibold text-base">{user.description}</h4>
                  {user.demographics && (
                    <p className="text-sm text-muted-foreground mt-2">
                      {user.demographics.age} • {user.demographics.occupation} • {user.demographics.technicalLevel}
                    </p>
                  )}
                </div>
              ))}
            </div>
          </div>
          
          <div>
            <h3 className="text-lg font-semibold mb-3 text-purple-600">Phases of User Adoption:</h3>
            <div className="flex items-center gap-3 flex-wrap">
              <Badge variant="outline" className="bg-blue-50 dark:bg-blue-900/20 border-blue-200 dark:border-blue-700 text-sm py-2 px-3">
                Phase 1: Early Adopters
              </Badge>
              <Badge variant="outline" className="bg-green-50 dark:bg-green-900/20 border-green-200 dark:border-green-700 text-sm py-2 px-3">
                Phase 2: Market Expansion
              </Badge>
              <Badge variant="outline" className="bg-purple-50 dark:bg-purple-900/20 border-purple-200 dark:border-purple-700 text-sm py-2 px-3">
                Phase 3: Market Leadership
              </Badge>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Slide 7: Traction & Roadmap */}
      <Card className="border border-primary/10 shadow-sm hover:shadow-md transition-all duration-300">
        <CardHeader className="border-b border-border/40 bg-muted/30">
          <div className="flex items-center gap-3">
            <LineChart className="h-6 w-6 text-indigo-500" />
            <CardTitle className="text-xl font-bold">Traction & Roadmap</CardTitle>
          </div>
          <CardDescription className="text-base mt-2">Current progress and future plans</CardDescription>
        </CardHeader>
        <CardContent className="pt-6">
          <div className="space-y-6">
            <div>
              <h3 className="text-lg font-semibold mb-3 text-indigo-600">Development Roadmap:</h3>
              <div className="relative pl-8 space-y-4 border-l-2 border-border">
                <div className="relative">
                  <div className="absolute -left-[26px] w-5 h-5 bg-blue-100 dark:bg-blue-900/30 border-2 border-blue-500 rounded-full"></div>
                  <div>
                    <h4 className="text-base font-semibold flex items-center gap-2">
                      <Clock className="h-4 w-4 text-blue-500" />
                      Phase 1: Core Development
                    </h4>
                    <p className="text-sm text-muted-foreground mt-2">Building essential features and MVP</p>
                  </div>
                </div>
                
                <div className="relative">
                  <div className="absolute -left-[26px] w-5 h-5 bg-green-100 dark:bg-green-900/30 border-2 border-green-500 rounded-full"></div>
                  <div>
                    <h4 className="text-base font-semibold flex items-center gap-2">
                      <Users className="h-4 w-4 text-green-500" />
                      Phase 2: Beta Testing
                    </h4>
                    <p className="text-sm text-muted-foreground mt-2">User feedback and refinement</p>
                  </div>
                </div>
                
                <div className="relative">
                  <div className="absolute -left-[26px] w-5 h-5 bg-purple-100 dark:bg-purple-900/30 border-2 border-purple-500 rounded-full"></div>
                  <div>
                    <h4 className="text-base font-semibold flex items-center gap-2">
                      <Rocket className="h-4 w-4 text-purple-500" />
                      Phase 3: Market Launch
                    </h4>
                    <p className="text-sm text-muted-foreground mt-2">Official release and growth</p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Slide 8: Funding Ask */}
      <Card className="border border-primary/10 shadow-sm hover:shadow-md transition-all duration-300">
        <CardHeader className="border-b border-border/40 bg-muted/30">
          <div className="flex items-center gap-3">
            <CreditCard className="h-6 w-6 text-pink-500" />
            <CardTitle className="text-xl font-bold">Funding Ask</CardTitle>
          </div>
          <CardDescription className="text-base mt-2">Investment requirements and use of funds</CardDescription>
        </CardHeader>
        <CardContent className="pt-6">
          <div className="space-y-6">
            <div className="border border-border/40 rounded-lg p-5 bg-muted/10">
              <h3 className="text-lg font-semibold mb-3 text-pink-600">Use of Funds:</h3>
              <div className="grid grid-cols-2 gap-3">
                <div className="flex items-center gap-3">
                  <div className="w-4 h-4 rounded-full bg-blue-500"></div>
                  <span className="text-base">Product Development (50%)</span>
                </div>
                <div className="flex items-center gap-3">
                  <div className="w-4 h-4 rounded-full bg-green-500"></div>
                  <span className="text-base">Marketing (25%)</span>
                </div>
                <div className="flex items-center gap-3">
                  <div className="w-4 h-4 rounded-full bg-amber-500"></div>
                  <span className="text-base">Operations (15%)</span>
                </div>
                <div className="flex items-center gap-3">
                  <div className="w-4 h-4 rounded-full bg-purple-500"></div>
                  <span className="text-base">Reserve (10%)</span>
                </div>
              </div>
            </div>
            
            <div>
              <h3 className="text-lg font-semibold mb-3 text-pink-600">Fundraising Milestones:</h3>
              <div className="relative pl-8 space-y-4 border-l-2 border-border">
                <div className="relative">
                  <div className="absolute -left-[26px] w-5 h-5 bg-blue-100 dark:bg-blue-900/30 border-2 border-blue-500 rounded-full"></div>
                  <p className="text-base leading-relaxed">Complete MVP development and secure initial customers</p>
                </div>
                <div className="relative">
                  <div className="absolute -left-[26px] w-5 h-5 bg-green-100 dark:bg-green-900/30 border-2 border-green-500 rounded-full"></div>
                  <p className="text-base leading-relaxed">Achieve product-market fit with positive user feedback</p>
                </div>
                <div className="relative">
                  <div className="absolute -left-[26px] w-5 h-5 bg-purple-100 dark:bg-purple-900/30 border-2 border-purple-500 rounded-full"></div>
                  <p className="text-base leading-relaxed">Scale operations and prepare for Series A</p>
                </div>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};
