
import { create } from 'zustand';
import { persist } from 'zustand/middleware';
import { supabase } from '@/integrations/supabase/client';

interface Profile {
  id: string;
  firstName: string | null;
  lastName: string | null;
  email: string | null;
  avatarUrl: string | null;
  created_at: string;
  updated_at: string;
}

interface UserState {
  profile: Profile | null;
  isLoading: boolean;
  error: Error | null;
  
  fetchProfile: () => Promise<void>;
  updateProfile: (data: Partial<Profile>) => Promise<void>;
  resetProfile: () => void;
}

export const useUserStore = create<UserState>()(
  persist(
    (set, get) => ({
      profile: null,
      isLoading: false,
      error: null,
      
      fetchProfile: async () => {
        try {
          set({ isLoading: true });
          const { data: { user } } = await supabase.auth.getUser();
          
          if (!user) {
            set({ profile: null, isLoading: false, error: new Error('No authenticated user') });
            return;
          }
          
          const { data, error } = await supabase
            .from('profiles')
            .select('*')
            .eq('id', user.id)
            .single();
            
          if (error) {
            set({ error: error as any, isLoading: false });
            return;
          }
          
          set({ profile: { ...data, email: user.email } as Profile, isLoading: false, error: null });
        } catch (error) {
          set({ error: error as Error, isLoading: false });
        }
      },
      
      updateProfile: async (data) => {
        try {
          const state = get();
          if (!state.profile) {
            throw new Error('No profile loaded');
          }
          
          set({ isLoading: true });
          
          const { error } = await supabase
            .from('profiles')
            .update(data)
            .eq('id', state.profile.id);
            
          if (error) {
            set({ error: error as any, isLoading: false });
            return;
          }
          
          set({ 
            profile: { ...state.profile, ...data } as Profile,
            isLoading: false, 
            error: null 
          });
        } catch (error) {
          set({ error: error as Error, isLoading: false });
        }
      },
      
      resetProfile: () => {
        set({ profile: null, error: null });
      }
    }),
    {
      name: 'user-storage',
      partialize: (state) => ({ profile: state.profile }),
    }
  )
);
