
import { create } from 'zustand';
import { persist } from 'zustand/middleware';
import { supabase } from '@/integrations/supabase/client';

interface AuthState {
  isLoading: boolean;
  isAuthenticated: boolean;
  isAdmin: boolean;
  userId: string | null;
  
  // Actions
  setLoading: (isLoading: boolean) => void;
  setAuthState: (isAuthenticated: boolean, isAdmin: boolean, userId: string | null) => void;
  checkAuthStatus: () => Promise<void>;
  signOut: () => Promise<void>;
  logAuthState: () => void;
}

export const useAuthStore = create<AuthState>()(
  persist(
    (set, get) => ({
      isLoading: true,
      isAuthenticated: false,
      isAdmin: false,
      userId: null,
      
      logAuthState: () => {
        // Removed console.log
      },
      
      setLoading: (isLoading) => set({ isLoading }),
      
      setAuthState: (isAuthenticated, isAdmin, userId) => {
        // Removed console.log
        set({
          isAuthenticated,
          isAdmin,
          userId,
          isLoading: false
        });
      },
      
      checkAuthStatus: async () => {
        try {
          // Removed console.log
          set({ isLoading: true });
          
          // Get current session
          const { data: { session } } = await supabase.auth.getSession();
          // Removed console.log
          
          if (!session) {
            // Removed console.log
            set({ isAuthenticated: false, isAdmin: false, userId: null, isLoading: false });
            return;
          }
          
          // Removed console.log
          
          // Check if admin - make sure this RPC call is properly defined in Supabase
          const { data: isAdmin, error } = await supabase.rpc('is_admin');
          
          if (error) {
            // Removed console.log
            set({ isAuthenticated: true, isAdmin: false, userId: session.user.id, isLoading: false });
            return;
          }
          
          // Removed console.log
          
          set({
            isAuthenticated: true,
            isAdmin: !!isAdmin,
            userId: session.user.id,
            isLoading: false
          });
          
          // Removed console.log
        } catch (error) {
          // Removed console.log
          set({ isAuthenticated: false, isAdmin: false, userId: null, isLoading: false });
        }
      },
      
      signOut: async () => {
        try {
          // Removed console.log
          await supabase.auth.signOut();
          set({ isAuthenticated: false, isAdmin: false, userId: null });
          // Removed console.log
        } catch (error) {
          // Removed console.log
        }
      }
    }),
    {
      name: 'auth-storage', // name of the item in storage
      partialize: (state) => ({ 
        isAuthenticated: state.isAuthenticated,
        isAdmin: state.isAdmin,
        userId: state.userId
      }),
    }
  )
);

// Initialize auth listener
export const initAuthListener = () => {
  const { checkAuthStatus } = useAuthStore.getState();
  
  // Perform initial check
  checkAuthStatus();
  
  // Set up auth state change listener
  const { data: { subscription } } = supabase.auth.onAuthStateChange(async (event, session) => {
    // Removed console.log
    
    if (event === 'SIGNED_IN' || event === 'TOKEN_REFRESHED') {
      // Removed console.log
      checkAuthStatus();
    } else if (event === 'SIGNED_OUT') {
      // Removed console.log
      useAuthStore.setState({ 
        isAuthenticated: false, 
        isAdmin: false, 
        userId: null,
        isLoading: false 
      });
    }
  });
  
  return subscription;
};
