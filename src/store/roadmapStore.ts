
import { create } from 'zustand';
import { supabase } from '@/integrations/supabase/client';
import { RoadmapItem } from '@/types';
import { toast } from 'sonner';

interface RoadmapState {
  roadmaps: RoadmapItem[];
  isLoading: boolean;
  
  // Actions
  fetchRoadmaps: (validationId: string) => Promise<void>;
  generateRoadmap: (validationId: string, phase: string, content: any) => Promise<RoadmapItem | null>;
  createRoadmap: (validationId: string, phase: string, name: string) => Promise<void>;
  generateMultipleRoadmaps: (validationId: string, phases: string[]) => Promise<boolean>;
  requestRoadmap: (validationId: string, phase: string) => Promise<RoadmapItem | null>;
  updateRoadmap: (roadmapId: string, content: any) => Promise<void>;
  publishRoadmap: (roadmapId: string) => Promise<void>;
  generateAIRoadmap: (validationId: string, phase: string) => Promise<RoadmapItem | null>;
  generateAllPhases: (validationId: string, accessiblePhases: string[]) => Promise<boolean>;
}

export const useRoadmapStore = create<RoadmapState>((set, get) => ({
  roadmaps: [],
  isLoading: false,
  
  fetchRoadmaps: async (validationId) => {
    try {
      set({ isLoading: true });
      
      const { data, error } = await supabase.rpc('get_roadmap', {
        input_validation_id: validationId
      });
      
      if (error) {
        console.error("Error fetching roadmaps:", error);
        throw error;
      }

      console.log("Fetched roadmaps:", data);
      
      set({ roadmaps: data as RoadmapItem[] });
    } catch (error) {
      console.error("Failed to load roadmaps:", error);
      toast.error("Failed to load roadmaps");
    } finally {
      set({ isLoading: false });
    }
  },
  
  generateRoadmap: async (validationId, phase, content) => {
    try {
      set({ isLoading: true });
      
      console.log("Generating roadmap for phase:", phase);
      console.log("Roadmap content:", content);
      
      // Get user session first to verify we're authenticated
      const { data: session } = await supabase.auth.getSession();
      if (!session.session) {
        console.error("No authenticated session found");
        toast.error('You must be logged in to generate roadmaps');
        throw new Error('Authentication required');
      }
      
      // Check if this roadmap already exists
      const { data: existingRoadmap, error: checkError } = await supabase
        .from('roadmaps')
        .select('id')
        .eq('validation_id', validationId)
        .eq('phase', phase)
        .maybeSingle();
        
      if (checkError) {
        console.error("Error checking for existing roadmap:", checkError);
      }
      
      if (existingRoadmap) {
        console.log("Updating existing roadmap:", existingRoadmap.id);
        
        // Update existing roadmap
        const { data: updateData, error: updateError } = await supabase
          .from('roadmaps')
          .update({
            content: content,
            status: 'draft', // Valid value from enum
            is_locked: phase !== 'interactive' // Only 'interactive' is unlocked by default
          })
          .eq('id', existingRoadmap.id)
          .select()
          .single();
          
        if (updateError) {
          console.error("Error updating roadmap:", updateError);
          throw updateError;
        }
        
        console.log("Roadmap updated successfully:", updateData);
        return updateData as RoadmapItem;
      }
      
      // Try direct database insertion first (new roadmap)
      try {
        console.log("Creating new roadmap for validation:", validationId);
        
        const { data: insertData, error: insertError } = await supabase
          .from('roadmaps')
          .insert({
            validation_id: validationId,
            user_id: session.session.user.id,
            phase: phase,
            content: content,
            status: 'draft', // Using a valid status value
            is_locked: phase !== 'interactive' // Only 'interactive' is unlocked by default
          })
          .select()
          .single();
        
        if (insertError) {
          console.error("Error inserting roadmap:", insertError);
          throw insertError;
        }
        
        console.log("Roadmap created successfully:", insertData);
        return insertData as RoadmapItem;
      } catch (directInsertError: any) {
        console.error("Direct insert error:", directInsertError);
        
        // If the error is due to a unique constraint violation, we should update instead
        if (directInsertError.code === '23505') {
          console.log("Unique constraint violation, trying to update instead");
          
          // Get the existing roadmap ID first
          const { data: existingRoadmap, error: fetchError } = await supabase
            .from('roadmaps')
            .select('id')
            .eq('validation_id', validationId)
            .eq('phase', phase)
            .maybeSingle();
            
          if (fetchError || !existingRoadmap) {
            console.error("Error fetching existing roadmap:", fetchError);
            throw directInsertError; // Re-throw the original error
          }
          
          // Update the existing roadmap
          const { data: updateData, error: updateError } = await supabase
            .from('roadmaps')
            .update({
              content: content,
              status: 'draft',
              is_locked: phase !== 'interactive'
            })
            .eq('id', existingRoadmap.id)
            .select()
            .single();
            
          if (updateError) {
            console.error("Error updating roadmap:", updateError);
            throw updateError;
          }
          
          console.log("Roadmap updated successfully:", updateData);
          return updateData as RoadmapItem;
        } else {
          // For other errors, try the RPC method as a last resort
          try {
            console.log("Trying RPC method as fallback");
            
            // Call the Supabase function to generate the roadmap - stripped parameters to match function signature
            const { data, error } = await supabase.rpc('generate_roadmap', {
              input_validation_id: validationId,
              input_phase: phase,
              roadmap_content: content
            });
            
            if (error) {
              console.error(`Failed to generate roadmap for ${phase}:`, error);
              toast.error(`Failed to generate roadmap for ${phase}: ${error.message}`);
              throw error;
            }
            
            if (!data) {
              console.error(`Failed to generate roadmap for ${phase}: No data returned`);
              toast.error(`Failed to generate roadmap for ${phase}: No data returned`);
              throw new Error('No data returned from generate_roadmap');
            }
            
            console.log("Roadmap generated successfully via RPC:", data);
            return { id: data } as RoadmapItem; // Return minimal data to indicate success
          } catch (rpcError) {
            console.error("RPC method failed:", rpcError);
            throw rpcError;
          }
        }
      }
    } catch (error) {
      console.error("Failed to generate roadmap:", error);
      toast.error("Failed to generate roadmap");
      return null;
    } finally {
      // Add a delay before refreshing to allow the database to update
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      // Refresh roadmaps after generation to get the latest data
      await get().fetchRoadmaps(validationId);
      set({ isLoading: false });
    }
  },
  
  createRoadmap: async (validationId, phase, name) => {
    try {
      set({ isLoading: true });
      
      // Create a basic roadmap content structure
      const content = {
        name: name,
        phase: phase,
        // Add default content structure with consistent camelCase field names
        phases: [{
          name: name,
          phase: phase,
          duration: "4 weeks",
          cost: "$5,000 - $10,000",
          hoursPerResource: "160",
          teamComposition: [],
          deliverables: [],
          milestones: [],
          risks: []
        }]
      };
      
      // Get user session first to verify we're authenticated
      const { data: session } = await supabase.auth.getSession();
      if (!session.session) {
        toast.error('You must be logged in to create roadmaps');
        throw new Error('Authentication required');
      }
      
      // Try direct database insertion first
      try {
        const { error: insertError } = await supabase
          .from('roadmaps')
          .insert({
            validation_id: validationId,
            user_id: session.session.user.id,
            phase: phase,
            content: content,
            status: 'draft', // Using a valid status value
            is_locked: phase !== 'interactive'
          });
          
        if (insertError) {
          // Fallback to RPC method
          const { error } = await supabase.rpc('generate_roadmap', {
            input_validation_id: validationId,
            input_phase: phase,
            roadmap_content: content,
            roadmap_status: 'draft', // Using a valid status value
            user_id: session.session.user.id
          });
          
          if (error) throw error;
        }
      } catch (error) {
        console.error("Error creating roadmap:", error);
        throw error;
      }
      
      // Add a delay before refreshing to allow the database to update
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      // Refresh roadmaps after creation
      await get().fetchRoadmaps(validationId);
    } catch (error) {
      console.error("Failed to create roadmap:", error);
      throw error;
    } finally {
      set({ isLoading: false });
    }
  },
  
  generateMultipleRoadmaps: async (validationId, phases) => {
    try {
      set({ isLoading: true });
      console.log("Generating multiple roadmaps for phases:", phases.join(', '));
      
      // Track successful generations
      let successCount = 0;
      
      // Get user session first to verify we're authenticated
      const { data: session } = await supabase.auth.getSession();
      if (!session.session) {
        console.error("No authenticated session found");
        toast.error('You must be logged in to generate roadmaps');
        throw new Error('Authentication required');
      }
      
      // Create a roadmap for each phase sequentially
      for (const phase of phases) {
        try {
          const { data, error } = await supabase.functions.invoke('generate-roadmap', {
            body: { 
              validationId, 
              phase
            }
          });
          
          if (error) {
            console.error(`Error generating roadmap for phase ${phase}:`, error);
            continue; // Skip to next phase on error
          }
          
          if (data && data.success) {
            successCount++;
            console.log(`Successfully generated roadmap for phase: ${phase}`);
          } else {
            console.error(`Failed to generate roadmap for phase ${phase}:`, data?.error || 'Unknown error');
          }
        } catch (phaseError) {
          console.error(`Error processing phase ${phase}:`, phaseError);
          // Continue with next phase even if one fails
        }
      }
      
      console.log(`Completed generating multiple roadmaps. Success: ${successCount}/${phases.length}`);
      
      // Add a longer delay before refreshing to allow the database to update
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      // Refresh roadmaps after all generations to ensure we have the latest data
      await get().fetchRoadmaps(validationId);
      
      return successCount > 0;
    } catch (error) {
      console.error("Failed to generate multiple roadmaps:", error);
      toast.error("Failed to generate roadmaps");
      return false;
    } finally {
      set({ isLoading: false });
    }
  },
  
  requestRoadmap: async (validationId, phase) => {
    try {
      set({ isLoading: true });
      
      // Create a roadmap request with pending status and no content
      const { data: session } = await supabase.auth.getSession();
      if (!session.session) {
        toast.error("You must be logged in to request a roadmap");
        return null;
      }
      
      const userId = session.session.user.id;
      
      // Insert the roadmap with proper status
      // For the interactive prototype, create it with status "in_progress" to show it's being worked on
      const { data, error } = await supabase
        .from('roadmaps')
        .insert({
          validation_id: validationId,
          user_id: userId,
          phase: phase,
          status: phase === 'interactive' ? 'in_progress' : 'pending', // Changed to valid values
          is_locked: phase !== 'interactive' // Only 'interactive' is unlocked by default
        })
        .select()
        .single();
      
      if (error) throw error;
      
      // Refresh roadmaps after request
      await get().fetchRoadmaps(validationId);
      
      // Explicitly cast the returned data to RoadmapItem
      return data as unknown as RoadmapItem;
    } catch (error) {
      console.error("Error requesting roadmap:", error);
      return null;
    } finally {
      set({ isLoading: false });
    }
  },
  
  updateRoadmap: async (roadmapId, content) => {
    try {
      set({ isLoading: true });
      
      console.log("Updating roadmap content:", content);
      
      // Update the roadmap content in the database but don't change status to published
      const { error } = await supabase
        .from('roadmaps')
        .update({
          content: content
          // Note: We're no longer automatically setting status to 'published'
        })
        .eq('id', roadmapId);
      
      if (error) {
        console.error("Error updating roadmap:", error);
        throw error;
      }
      
      // Update the local state
      set(state => ({
        roadmaps: state.roadmaps.map(roadmap => 
          roadmap.id === roadmapId 
            ? { ...roadmap, content }
            : roadmap
        )
      }));
      
      toast.success("Roadmap updated successfully");
    } catch (error) {
      console.error("Failed to update roadmap:", error);
      toast.error("Failed to update roadmap");
      throw error;
    } finally {
      set({ isLoading: false });
    }
  },

  publishRoadmap: async (roadmapId) => {
    try {
      set({ isLoading: true });
      
      // Update the roadmap status to 'published'
      const { error } = await supabase
        .from('roadmaps')
        .update({ status: 'published' })
        .eq('id', roadmapId);
      
      if (error) {
        console.error("Error publishing roadmap:", error);
        throw error;
      }
      
      // Update the local state
      set(state => ({
        roadmaps: state.roadmaps.map(roadmap => 
          roadmap.id === roadmapId 
            ? { ...roadmap, status: 'published' }
            : roadmap
        )
      }));
      
      toast.success("Roadmap published successfully");
    } catch (error) {
      console.error("Failed to publish roadmap:", error);
      toast.error("Failed to publish roadmap");
      throw error;
    } finally {
      set({ isLoading: false });
    }
  },
  
  generateAIRoadmap: async (validationId, phase) => {
    try {
      set({ isLoading: true });
      
      console.log("Generating AI roadmap for phase:", phase);
      
      // Get user session first to verify we're authenticated
      const { data: session } = await supabase.auth.getSession();
      if (!session.session) {
        console.error("No authenticated session found");
        toast.error('You must be logged in to generate roadmaps');
        throw new Error('Authentication required');
      }
      
      // Call the Edge Function to generate roadmap content
      const { data: aiData, error: aiError } = await supabase.functions.invoke('generate-roadmap', {
        body: {
          validationId: validationId,
          phase: phase
        }
      });
      
      if (aiError) {
        console.error("AI generation error:", aiError);
        throw aiError;
      }
      
      if (!aiData || !aiData.success) {
        console.error("AI roadmap generation failed:", aiData?.message || 'Unknown error');
        throw new Error(aiData?.message || 'AI roadmap generation failed');
      }
      
      console.log("AI generated roadmap:", aiData.roadmapContent);
      
      return {
        id: aiData.roadmapId,
        content: aiData.roadmapContent
      } as RoadmapItem;
    } catch (error) {
      console.error("Failed to generate AI roadmap:", error);
      toast.error("Failed to generate AI roadmap: " + (error.message || "Unknown error"));
      return null;
    } finally {
      await get().fetchRoadmaps(validationId);
      set({ isLoading: false });
    }
  },
  
  generateAllPhases: async (validationId, accessiblePhases) => {
    try {
      set({ isLoading: true });
      toast.info("Generating roadmaps for all phases. This may take a moment...");
      
      // Track generation results
      const results = [];
      
      for (const phase of accessiblePhases) {
        try {
          console.log(`Generating roadmap for phase ${phase}...`);
          
          // Call the Edge Function directly
          const { data, error } = await supabase.functions.invoke('generate-roadmap', {
            body: { 
              validationId,
              phase 
            }
          });
          
          if (error) {
            console.error(`Error generating roadmap for phase ${phase}:`, error);
            results.push({ phase, success: false });
            continue;
          }
          
          console.log(`Successfully generated roadmap for phase ${phase}:`, data);
          results.push({ phase, success: true });
          
        } catch (phaseError) {
          console.error(`Error generating roadmap for phase ${phase}:`, phaseError);
          results.push({ phase, success: false });
        }
      }
      
      // Show a summary of results
      const successCount = results.filter(r => r.success).length;
      if (successCount > 0) {
        toast.success(`Generated ${successCount} of ${accessiblePhases.length} roadmaps successfully`);
      } else {
        toast.error('Failed to generate any roadmaps');
      }
      
      // Force refresh of roadmaps list
      await new Promise(resolve => setTimeout(resolve, 1000));
      await get().fetchRoadmaps(validationId);
      
      return successCount > 0;
      
    } catch (error) {
      console.error("Failed to generate all phases:", error);
      toast.error("Failed to generate roadmaps for all phases");
      return false;
    } finally {
      set({ isLoading: false });
    }
  }
}));
