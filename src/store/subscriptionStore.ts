import { create } from 'zustand';
import { supabase } from '@/integrations/supabase/client';

// Define types for pricing plans
interface PricingPlan {
  id: string;
  name: string;
  price: number;
  interval: string;
  features?: any;
  is_active: boolean;
  description?: string;
}

// Define types for free plan limits
interface FreePlanLimits {
  idea_validations: number;
  roadmaps: number;
}

// Interface for subscription data returned from Supabase
interface SubscriptionData {
  created_at: string;
  id: string;
  pack_type: string | null;
  payment_id: string;
  payment_processor: string;
  plan_id: string;
  status: string;
  updated_at: string;
  user_id: string;
  valid_until: string;
  project_id?: string | null;
}

interface SubscriptionState {
  currentSubscription: SubscriptionData | null;
  isLoading: boolean;
  plans: PricingPlan[];
  freePlanLimits: FreePlanLimits | null;
  isPro: boolean;
  
  // Actions
  fetchCurrentSubscription: () => Promise<void>;
  fetchPlans: () => Promise<void>;
  fetchFreePlanLimits: () => Promise<void>;
  checkRoadmapAccess: (phase: string, projectId?: string) => Promise<boolean>;
  getUserAccessiblePhases: (projectId: string) => Promise<string[]>;
  clearAccessCache: () => void;
}

export const useSubscriptionStore = create<SubscriptionState>((set, get) => ({
  currentSubscription: null,
  isLoading: false,
  plans: [],
  freePlanLimits: null,
  isPro: false,

  fetchCurrentSubscription: async () => {
    try {
      set({ isLoading: true });
      
      const { data: session } = await supabase.auth.getSession();
      if (!session.session) {
        // User not logged in
        set({ currentSubscription: null, isLoading: false, isPro: false });
        return;
      }
      
      const userId = session.session.user.id;
      
      // Fetch the latest subscription data for the user
      const { data, error } = await supabase
        .from('user_subscriptions') 
        .select('*')
        .eq('user_id', userId)
        .order('created_at', { ascending: false })
        .limit(1)
        .single();
      
      if (error) {
        console.error("Error fetching subscription:", error);
        set({ currentSubscription: null, isLoading: false, isPro: false });
        return;
      }
      
      console.log("Current subscription data:", data);
      
      // Check if the user has an active subscription
      const isPro = data && 
                    data.status === 'active' && 
                    new Date(data.valid_until) > new Date();
      
      set({ 
        currentSubscription: data, 
        isLoading: false,
        isPro
      });
    } catch (error) {
      console.error("Error in fetchCurrentSubscription:", error);
      set({ currentSubscription: null, isLoading: false, isPro: false });
    } finally {
      set({ isLoading: false });
    }
  },

  fetchPlans: async () => {
    try {
      set({ isLoading: true });
      
      // Fetch active pricing plans
      const { data, error } = await supabase.rpc('get_pricing_plans');
      
      if (error) {
        console.error("Error fetching pricing plans:", error);
        return;
      }
      
      console.log("Fetched pricing plans:", data);
      set({ plans: data || [] });
    } catch (error) {
      console.error("Error in fetchPlans:", error);
    } finally {
      set({ isLoading: false });
    }
  },

  fetchFreePlanLimits: async () => {
    try {
      // Fetch free plan limits from system settings
      const { data, error } = await supabase.rpc('get_free_plan_limits');
      
      if (error) {
        console.error("Error fetching free plan limits:", error);
        return;
      }
      
      console.log("Fetched free plan limits:", data);
      set({ freePlanLimits: data as FreePlanLimits });
    } catch (error) {
      console.error("Error in fetchFreePlanLimits:", error);
    }
  },

  // Don't cache anything, directly call the backend function
  checkRoadmapAccess: async (phase, projectId = "") => {
    if (phase === 'interactive') {
      console.log("Interactive prototype is always accessible");
      return true;
    }
    
    try {
      // Always make a direct check with a specific project ID
      console.log(`Performing direct access check for phase ${phase} and project ${projectId || 'global'}`);
      
      if (!projectId) {
        // If no project ID is provided, check global access via RPC
        const { data, error } = await supabase.rpc('check_roadmap_access', { phase });
        
        if (error) {
          console.error("Error calling check_roadmap_access:", error);
          return false;
        }
        
        return !!data;
      }
      
      // Use the Edge Function for project-specific access check
      const { data, error } = await supabase.functions.invoke('get-accessible-phases', {
        body: { projectId }
      });
      
      if (error) {
        console.error("Error invoking get-accessible-phases:", error);
        return false;
      }
      
      // Check if the phase is in the returned list of accessible phases
      return data && 
             data.phases && 
             Array.isArray(data.phases) && 
             data.phases.includes(phase);
      
    } catch (error) {
      console.error("Error in checkRoadmapAccess:", error);
      return false;
    }
  },
  
  // New function to get all accessible phases for a project at once
  getUserAccessiblePhases: async (projectId: string) => {
    if (!projectId) {
      console.error("No project ID provided to getUserAccessiblePhases");
      return ['interactive']; // Default to just interactive prototype access
    }
    
    console.log(`Getting all accessible phases for project ${projectId}`);
    
    try {
      // Skip if no auth session
      const { data: session } = await supabase.auth.getSession();
      if (!session.session) {
        console.log("No auth session, defaulting to interactive only");
        return ['interactive'];
      }
      
      // Check for admin role first with direct DB query
      const { data: adminRoles, error: adminError } = await supabase
        .from('user_roles')
        .select('role')
        .eq('user_id', session.session.user.id)
        .eq('role', 'admin')
        .limit(1);
      
      const isAdmin = !adminError && adminRoles && adminRoles.length > 0;
      
      if (isAdmin) {
        console.log("User is admin, has access to all phases");
        return ['interactive', 'poc', 'mvp', 'production'];
      }
      
      // Always make a fresh direct call to the Edge Function
      const { data, error } = await supabase.functions.invoke('get-accessible-phases', {
        body: { projectId }
      });
      
      if (error) {
        console.error("Error fetching accessible phases:", error);
        return ['interactive']; // Default to just interactive
      }
      
      console.log("Response from get-accessible-phases:", data);
      
      if (data && data.phases && Array.isArray(data.phases)) {
        console.log(`Accessible phases for project ${projectId}:`, data.phases);
        return data.phases;
      }
      
      return ['interactive']; // Default fallback
    } catch (error) {
      console.error("Error in getUserAccessiblePhases:", error);
      return ['interactive']; // Default to just interactive prototype
    }
  },

  clearAccessCache: () => {
    console.log("Access cache cleared (direct DB approach doesn't use cache)");
    // Since we're using direct DB queries, this is now just a no-op function
    // We keep it to maintain API compatibility with the components that call it
  }
}));

// Export the subscription listener initialization function
export const initSubscriptionListener = () => {
  const { fetchCurrentSubscription } = useSubscriptionStore.getState();
  
  // Initial fetch
  fetchCurrentSubscription();
  
  // Set up a listener for auth state changes to refresh subscription
  const { data: { subscription } } = supabase.auth.onAuthStateChange((event) => {
    if (event === 'SIGNED_IN' || event === 'TOKEN_REFRESHED') {
      console.log("Auth event triggered subscription refresh:", event);
      // Fetch fresh subscription data
      fetchCurrentSubscription();
    } else if (event === 'SIGNED_OUT') {
      console.log("User signed out, clearing subscription data");
      useSubscriptionStore.setState({ 
        currentSubscription: null,
        isPro: false,
      });
    }
  });
  
  return subscription;
};
