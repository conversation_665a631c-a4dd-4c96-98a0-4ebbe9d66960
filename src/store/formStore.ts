
import { create } from 'zustand';
import { persist } from 'zustand/middleware';

interface PlanningForm {
  platforms: string[];
  developmentPhases: string[];
  technologyStack: string[];
  brandingPreferences: string[];
  monthlyUsers: string;
  timeline: string;
  budgetRange: string;
  validationId?: string;
}

interface FormState {
  ideaForm: {
    idea: string;
    validationId?: string;
  };
  planningForm: PlanningForm;
  setIdeaForm: (idea: string) => void;
  setValidationId: (id: string) => void;
  resetIdeaForm: () => void;
  setPlanningField: <K extends keyof PlanningForm>(field: K, value: PlanningForm[K]) => void;
  resetPlanningForm: () => void;
}

const initialPlanningForm: PlanningForm = {
  platforms: [],
  developmentPhases: [],
  technologyStack: [],
  brandingPreferences: [],
  monthlyUsers: '',
  timeline: '',
  budgetRange: '',
  validationId: undefined,
};

export const useFormStore = create<FormState>()(
  persist(
    (set) => ({
      ideaForm: {
        idea: "",
        validationId: undefined,
      },
      planningForm: initialPlanningForm,
      setIdeaForm: (idea) => set((state) => ({ 
        ideaForm: { ...state.ideaForm, idea } 
      })),
      setValidationId: (validationId) => set((state) => ({
        ideaForm: { ...state.ideaForm, validationId },
        planningForm: { ...state.planningForm, validationId }
      })),
      resetIdeaForm: () => set({ 
        ideaForm: { idea: "", validationId: undefined } 
      }),
      setPlanningField: (field, value) => set((state) => ({
        planningForm: { ...state.planningForm, [field]: value }
      })),
      resetPlanningForm: () => set({ 
        planningForm: { ...initialPlanningForm } 
      }),
    }),
    {
      name: 'form-storage',
    }
  )
);
