
import { useState, useEffect, useMemo } from "react";
import { useNavigate } from "react-router-dom";
import { useQuery } from "@tanstack/react-query";
import { supabase } from "@/integrations/supabase/client";
import { AdminSidebar } from "@/components/admin/AdminSidebar";
import { SidebarProvider } from "@/components/ui/sidebar";
import { useSidebarStore } from "@/store/sidebarStore";
import { Card, CardContent } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Badge } from "@/components/ui/badge";
import { Loader2, ChevronRight, Edit, CheckCircle, RefreshCw, PlusCircle } from "lucide-react";
import { toast } from "sonner";
import { GenerateRoadmapDialog } from "@/components/roadmap/GenerateRoadmapDialog";
import { DEVELOPMENT_PHASES } from "@/types";
import { useRoadmapStore } from "@/store/roadmapStore";

// Define a grouped roadmap type for consolidated view
interface GroupedRoadmap {
  validationId: string;
  projectName: string;
  userName: string | null;
  userEmail: string | null;
  createdAt: string;
  hasPublishedRoadmap: boolean;
  phases: {
    [key: string]: {
      id: string;
      status: string;
    } | null
  };
}

export default function AdminRoadmaps() {
  const navigate = useNavigate();
  const { open, setOpen } = useSidebarStore();
  const { publishRoadmap } = useRoadmapStore();
  const [selectedValidationId, setSelectedValidationId] = useState<string | null>(null);
  const [selectedPhase, setSelectedPhase] = useState<string>("interactive");
  const [showGenerateDialog, setShowGenerateDialog] = useState(false);
  const [selectedRoadmap, setSelectedRoadmap] = useState<any | null>(null);
  const [isPublishing, setIsPublishing] = useState<string | null>(null);
  const [isRefreshing, setIsRefreshing] = useState(false);

  // Get the display name for a phase ID
  const getPhaseDisplayName = (phaseId: string) => {
    return DEVELOPMENT_PHASES[phaseId]?.name || phaseId;
  };

  // Fetch all roadmap requests
  const { data: roadmapRequests, isLoading, refetch } = useQuery({
    queryKey: ['admin-roadmap-requests'],
    queryFn: async () => {
      // First fetch the roadmaps with content included
      const { data: roadmapsData, error: roadmapsError } = await supabase
        .from('roadmaps')
        .select(`
          id, 
          phase, 
          status, 
          created_at,
          validation_id,
          content,
          idea_validations (
            id,
            originalIdea,
            projectConcept,
            userId
          )
        `)
        .order('created_at', { ascending: false });
      
      if (roadmapsError) {
        console.error('Error fetching roadmaps:', roadmapsError);
        toast.error('Failed to load roadmaps');
        throw roadmapsError;
      }

      // Now process the data to include user information
      const processedData = await Promise.all((roadmapsData || []).map(async (roadmap) => {
        if (roadmap.idea_validations?.userId) {
          // Fetch user profile separately - using only firstName and lastName here
          const { data: userData, error: userError } = await supabase
            .from('profiles')
            .select('firstName, lastName')
            .eq('id', roadmap.idea_validations.userId)
            .single();
          
          if (userError) {
            console.warn(`Could not fetch user for roadmap ${roadmap.id}:`, userError);
            // Return with empty user info
            return {
              ...roadmap,
              idea_validations: {
                ...roadmap.idea_validations,
                user: null
              }
            };
          }
          
          // Return with user info attached
          return {
            ...roadmap,
            idea_validations: {
              ...roadmap.idea_validations,
              user: userData
            }
          };
        }
        
        // Return without user info if userId is missing
        return {
          ...roadmap,
          idea_validations: {
            ...roadmap.idea_validations,
            user: null
          }
        };
      }));
      
      return processedData;
    }
  });

  // Group roadmaps by project (validation_id)
  const groupedRoadmaps = useMemo(() => {
    if (!roadmapRequests) return [];
    
    const projectMap = new Map<string, GroupedRoadmap>();
    
    roadmapRequests.forEach(roadmap => {
      const validationId = roadmap.validation_id;
      const projectName = roadmap.idea_validations?.originalIdea || "Untitled Project";
      const userName = roadmap.idea_validations?.user 
        ? `${roadmap.idea_validations.user.firstName || ''} ${roadmap.idea_validations.user.lastName || ''}`.trim() 
        : null;
      
      if (!projectMap.has(validationId)) {
        // Create new project entry
        projectMap.set(validationId, {
          validationId,
          projectName,
          userName,
          userEmail: null, // Add email if needed
          createdAt: roadmap.created_at,
          hasPublishedRoadmap: roadmap.status === 'published',
          phases: {
            interactive: null,
            poc: null,
            mvp: null,
            production: null
          }
        });
      } else {
        // Update existing project if this roadmap is published
        if (roadmap.status === 'published') {
          projectMap.get(validationId)!.hasPublishedRoadmap = true;
        }
      }
      
      // Add or update phase info
      projectMap.get(validationId)!.phases[roadmap.phase] = {
        id: roadmap.id,
        status: roadmap.status
      };
    });
    
    // Convert map to array and sort by most recent
    return Array.from(projectMap.values())
      .sort((a, b) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime());
  }, [roadmapRequests]);

  // Manually refresh data
  const handleRefreshData = async () => {
    setIsRefreshing(true);
    await refetch();
    setIsRefreshing(false);
    toast.success("Data refreshed");
  };

  // Handle roadmap generation success
  const handleGenerateSuccess = async () => {
    toast.success("Roadmap generated successfully");
    
    // Force immediate refetch after generation with a slight delay
    // to allow database operations to complete
    setIsRefreshing(true);
    await new Promise(resolve => setTimeout(resolve, 300));
    await refetch();
    setIsRefreshing(false);
    
    setShowGenerateDialog(false);
    setSelectedRoadmap(null);
  };

  // Function to handle publishing a roadmap
  const handlePublishRoadmap = async (roadmapId: string) => {
    try {
      setIsPublishing(roadmapId);
      await publishRoadmap(roadmapId);
      
      // Force immediate refetch after publishing with a slight delay
      await new Promise(resolve => setTimeout(resolve, 300));
      await refetch();
    } catch (error) {
      console.error("Error publishing roadmap:", error);
      toast.error("Failed to publish roadmap");
    } finally {
      setIsPublishing(null);
    }
  };

  // Go directly to the roadmap view page
  const handleViewProject = (validationId: string) => {
    navigate(`/roadmap/${validationId}`);
  };

  // Handle opening the roadmap generation dialog
  const handleGenerateRoadmap = (validationId: string, phase: string = "interactive", existingRoadmapId?: string) => {
    setSelectedValidationId(validationId);
    setSelectedPhase(phase);
    setSelectedRoadmap(existingRoadmapId ? { id: existingRoadmapId } : null);
    setShowGenerateDialog(true);
  };

  // Filter roadmaps based on tab selection
  const filterRoadmapsByStatus = (status: string) => {
    if (status === 'all') return groupedRoadmaps;
    
    if (status === 'pending') {
      return groupedRoadmaps.filter(project => 
        Object.values(project.phases).some(phase => 
          phase && (phase.status === 'pending' || phase.status === 'in_progress')
        )
      );
    }
    
    return groupedRoadmaps.filter(project => 
      Object.values(project.phases).some(phase => 
        phase && phase.status === status
      )
    );
  };

  return (
    <SidebarProvider open={open} setOpen={setOpen}>
      <div className="min-h-screen flex bg-gradient-to-b from-background via-background/95 to-background">
        <AdminSidebar open={open} setOpen={setOpen} />
        <div className="flex-1 p-6 md:p-8">
          <div className="max-w-7xl mx-auto">
            <div className="flex justify-between items-center mb-6">
              <h1 className="text-3xl font-bold">Roadmap Management</h1>
              
              <Button 
                variant="outline" 
                size="sm" 
                onClick={handleRefreshData}
                disabled={isRefreshing}
              >
                {isRefreshing ? (
                  <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                ) : (
                  <RefreshCw className="h-4 w-4 mr-2" />
                )}
                Refresh
              </Button>
            </div>
            
            <Tabs defaultValue="pending" className="w-full">
              <TabsList className="grid w-full max-w-md grid-cols-4 mb-8">
                <TabsTrigger value="pending">Pending</TabsTrigger>
                <TabsTrigger value="draft">Draft</TabsTrigger>
                <TabsTrigger value="published">Published</TabsTrigger>
                <TabsTrigger value="all">All</TabsTrigger>
              </TabsList>
              
              {isLoading ? (
                <div className="flex items-center justify-center py-16">
                  <Loader2 className="h-8 w-8 animate-spin text-primary" />
                </div>
              ) : (
                <>
                  <TabsContent value="pending">
                    <ProjectRoadmapList 
                      projects={filterRoadmapsByStatus('pending')} 
                      onGenerateRoadmap={handleGenerateRoadmap}
                      onViewProject={handleViewProject}
                      onPublish={handlePublishRoadmap}
                      publishingId={isPublishing}
                    />
                  </TabsContent>
                  
                  <TabsContent value="draft">
                    <ProjectRoadmapList 
                      projects={filterRoadmapsByStatus('draft')} 
                      onGenerateRoadmap={handleGenerateRoadmap}
                      onViewProject={handleViewProject}
                      onPublish={handlePublishRoadmap}
                      publishingId={isPublishing}
                    />
                  </TabsContent>
                  
                  <TabsContent value="published">
                    <ProjectRoadmapList 
                      projects={filterRoadmapsByStatus('published')} 
                      onGenerateRoadmap={handleGenerateRoadmap}
                      onViewProject={handleViewProject}
                      onPublish={handlePublishRoadmap}
                      publishingId={isPublishing}
                    />
                  </TabsContent>
                  
                  <TabsContent value="all">
                    <ProjectRoadmapList 
                      projects={filterRoadmapsByStatus('all')} 
                      onGenerateRoadmap={handleGenerateRoadmap}
                      onViewProject={handleViewProject}
                      onPublish={handlePublishRoadmap}
                      publishingId={isPublishing}
                    />
                  </TabsContent>
                </>
              )}
            </Tabs>
          </div>
        </div>
      </div>
      
      {/* Generate Roadmap Dialog */}
      {selectedValidationId && (
        <GenerateRoadmapDialog 
          validationId={selectedValidationId}
          phase={selectedPhase}
          externalOpen={showGenerateDialog}
          onOpenChange={setShowGenerateDialog}
          onSuccess={handleGenerateSuccess}
          existingRoadmapId={selectedRoadmap?.id}
          existingContent={selectedRoadmap?.content}
        />
      )}
    </SidebarProvider>
  );
}

// Component to display the consolidated project roadmap list
function ProjectRoadmapList({ 
  projects, 
  onGenerateRoadmap,
  onViewProject,
  onPublish,
  publishingId
}: { 
  projects: GroupedRoadmap[]; 
  onGenerateRoadmap: (validationId: string, phase?: string, existingRoadmapId?: string) => void;
  onViewProject: (validationId: string) => void;
  onPublish: (roadmapId: string) => void;
  publishingId: string | null;
}) {
  const allPhases = ['interactive', 'poc', 'mvp', 'production'];
  
  // Get status badge variant
  const getStatusBadgeVariant = (status: string) => {
    switch (status) {
      case 'published': return "default";
      case 'draft': return "secondary";
      case 'pending': return "outline";
      case 'in_progress': return "outline";
      default: return "outline";
    }
  };
  
  // Get status display text
  const getStatusDisplayText = (status: string) => {
    switch (status) {
      case 'in_progress': return "In Progress";
      default: return status.charAt(0).toUpperCase() + status.slice(1);
    }
  };
  
  if (projects.length === 0) {
    return (
      <Card className="border border-dashed">
        <CardContent className="py-10 text-center">
          <p className="text-muted-foreground">No roadmap requests found in this category.</p>
        </CardContent>
      </Card>
    );
  }
  
  return (
    <div className="grid gap-4">
      {projects.map((project) => (
        <Card key={project.validationId} className="overflow-hidden">
          <CardContent className="p-0">
            <div className="grid grid-cols-1 md:grid-cols-4 divide-y md:divide-y-0 md:divide-x">
              <div className="p-4 md:p-6">
                <p className="text-sm text-muted-foreground mb-1">Project Idea</p>
                <p className="font-medium truncate">
                  {project.projectName}
                </p>
                
                <div className="flex flex-wrap gap-2 mt-4">
                  {project.hasPublishedRoadmap && (
                    <Badge>Published</Badge>
                  )}
                </div>
              </div>
              
              <div className="p-4 md:p-6 md:col-span-2">
                <p className="text-sm text-muted-foreground mb-1">Requested by</p>
                <p className="font-medium">
                  {project.userName || "Unknown user"}
                </p>
                
                <p className="text-sm text-muted-foreground mt-3 mb-1">Roadmap Phases</p>
                <div className="flex flex-wrap gap-2 mt-2">
                  {allPhases.map(phaseId => {
                    const phase = project.phases[phaseId];
                    return (
                      <Badge 
                        key={phaseId}
                        variant={phase ? getStatusBadgeVariant(phase.status) : "outline"}
                        className={!phase ? "opacity-50" : ""}
                      >
                        {DEVELOPMENT_PHASES[phaseId]?.name || phaseId}: {' '}
                        {phase ? getStatusDisplayText(phase.status) : "Not Generated"}
                      </Badge>
                    );
                  })}
                </div>
                
                <p className="text-sm text-muted-foreground mt-3 mb-1">Date Created</p>
                <p className="font-medium">
                  {new Date(project.createdAt).toLocaleString()}
                </p>
              </div>
              
              <div className="p-4 md:p-6 flex flex-col justify-center gap-2">
                <div className="w-full space-y-2">
                  <Button 
                    onClick={() => onGenerateRoadmap(project.validationId)}
                    variant="outline"
                    className="w-full justify-start"
                  >
                    <PlusCircle className="mr-2 h-4 w-4" />
                    Generate All Phases
                  </Button>
                  
                  {/* If there's at least one draft roadmap, show the publish button */}
                  {Object.values(project.phases).some(phase => phase && phase.status === 'draft') && (
                    <Button 
                      onClick={() => {
                        // Find the first draft roadmap to publish
                        for (const phaseId of allPhases) {
                          const phase = project.phases[phaseId];
                          if (phase && phase.status === 'draft') {
                            onPublish(phase.id);
                            break;
                          }
                        }
                      }}
                      variant="default" 
                      className="w-full justify-start"
                      disabled={!!publishingId}
                    >
                      {publishingId ? (
                        <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                      ) : (
                        <CheckCircle className="mr-2 h-4 w-4" />
                      )}
                      Publish Next Draft
                    </Button>
                  )}
                  
                  <Button 
                    variant="ghost"
                    className="w-full justify-start"
                    onClick={() => onViewProject(project.validationId)}
                  >
                    View Roadmap
                    <ChevronRight className="ml-auto h-4 w-4" />
                  </Button>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      ))}
    </div>
  );
}
