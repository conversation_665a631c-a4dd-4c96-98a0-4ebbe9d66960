import { useEffect, useState } from "react";
import { useNavigate, useParams, useLocation } from "react-router-dom";
import { ChevronLeft, Check } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card";
import { UserSidebar } from "@/components/user/UserSidebar";
import { SidebarProvider } from "@/components/ui/sidebar";
import { useSidebarStore } from "@/store/sidebarStore";
import { useSubscriptionStore } from "@/store/subscriptionStore";
import { toast } from "sonner";
import { Separator } from "@/components/ui/separator";

export default function Checkout() {
  const navigate = useNavigate();
  const location = useLocation();
  const { planId } = useParams<{ planId: string }>();
  const { open, setOpen } = useSidebarStore();
  const { 
    plans, 
    fetchPlans, 
    isLoading, 
    currentSubscription,
    fetchCurrentSubscription,
    isPro,
    freePlanLimits
  } = useSubscriptionStore();
  const [selectedPlan, setSelectedPlan] = useState<any>(null);
  
  // Get URL parameters
  const queryParams = new URLSearchParams(location.search);
  const ideaCount = queryParams.get('count') || "1";
  const roadmapPack = queryParams.get('pack') || "poc";
  const projectId = queryParams.get('project') || "";
  
  useEffect(() => {
    fetchPlans();
    fetchCurrentSubscription();
  }, [fetchPlans, fetchCurrentSubscription]);

  useEffect(() => {
    // Set selected plan once plans are loaded
    if (plans.length > 0 && planId) {
      const plan = plans.find(p => p.id === planId);
      
      // Handle plan not found (except for our custom plans)
      if (!plan && planId !== 'multi-idea' && planId !== 'roadmap-pack') {
        toast.error("Selected plan not found");
        navigate("/pricing");
        return;
      }

      // Check if already subscribed
      // For roadmap packs, check both plan_id and pack_type
      if (planId === 'roadmap-pack') {
        if (currentSubscription?.plan_id === 'roadmap-pack' && currentSubscription?.pack_type === roadmapPack) {
          toast.info("You are already subscribed to this roadmap pack");
          navigate("/pricing");
          return;
        }
      } else if (currentSubscription?.plan_id === planId) {
        toast.info("You are already subscribed to this plan");
        navigate("/pricing");
        return;
      }

      // Check if free plan (which should already be available)
      if (planId === 'free') {
        toast.info("You already have access to the Free plan");
        navigate("/pricing");
        return;
      }

      // For multi-idea plans, create a custom plan object with the selected count
      if (planId === 'multi-idea') {
        const price = getMultiIdeaPrice(ideaCount);
        const multiIdeaPlan = {
          id: 'multi-idea',
          name: 'Idea Validation Pack',
          description: getMultiIdeaDescription(ideaCount),
          price: price,
          interval: 'one-time purchase',
          count: ideaCount
        };
        setSelectedPlan(multiIdeaPlan);
      } 
      // For roadmap packs, create a custom plan object with the selected pack
      else if (planId === 'roadmap-pack') {
        const price = getRoadmapPackPrice(roadmapPack);
        const roadmapPackPlan = {
          id: 'roadmap-pack',
          name: 'Roadmap Package',
          description: getRoadmapPackDescription(roadmapPack),
          price: price,
          interval: 'one-time purchase',
          packType: roadmapPack,
          projectId: projectId
        };
        setSelectedPlan(roadmapPackPlan);
      } else {
        setSelectedPlan(plan);
      }
    }
  }, [plans, planId, currentSubscription, navigate, ideaCount, roadmapPack, projectId]);

  const handleProceedToPayment = () => {
    // For multi-idea plan, pass the count parameter
    if (planId === 'multi-idea') {
      navigate(`/payment/${planId}?count=${ideaCount}`);
    } 
    // For roadmap pack, pass the pack parameter and projectId
    else if (planId === 'roadmap-pack') {
      navigate(`/payment/${planId}?pack=${roadmapPack}${projectId ? `&project=${projectId}` : ''}`);
    } else {
      navigate(`/payment/${planId}`);
    }
  };

  const getMultiIdeaPrice = (count: string) => {
    switch (count) {
      case "1": return 5.00;
      case "2": return 9.99;
      case "5": return 19.99;
      case "10": return 45.00;
      default: return 5.00;
    }
  };

  const getRoadmapPackPrice = (pack: string) => {
    switch (pack) {
      case "poc": return 10.00;
      case "poc-mvp": return 17.00;
      case "all": return 20.00;
      default: return 10.00;
    }
  };

  const getMultiIdeaDescription = (count: string) => {
    return `Validate ${count} additional idea${count === "1" ? "" : "s"} and expand your project portfolio`;
  };

  const getRoadmapPackDescription = (pack: string) => {
    switch (pack) {
      case "poc": 
        return "Access to Proof of Concept roadmap";
      case "poc-mvp": 
        return "Access to Proof of Concept and MVP roadmaps";
      case "all": 
        return "Access to all roadmaps (POC, MVP, and Production)";
      default: 
        return "Access to roadmap features";
    }
  };

  const getDetailedFeatures = (planId: string, packType?: string) => {
    if (planId === 'multi-idea') {
      return [
        `Validate ${ideaCount} additional idea${ideaCount === "1" ? "" : "s"} (${freePlanLimits?.idea_validations || 1} included in free plan)`,
        "Compare different project concepts",
        "Export validation reports",
        "Priority processing",
        "Historical comparison analytics"
      ];
    } else if (planId === 'roadmap-pack') {
      switch (packType) {
        case 'poc':
          return [
            "Proof of Concept roadmap",
            "Technical architecture document",
            "API definitions",
            "Performance metrics",
            "Proof of feasibility"
          ];
        case 'poc-mvp':
          return [
            "Proof of Concept roadmap",
            "MVP roadmap with user onboarding",
            "Payment processing documentation",
            "Admin dashboard specifications",
            "Basic analytics setup"
          ];
        case 'all':
          return [
            "Proof of Concept roadmap",
            "MVP roadmap with complete feature set",
            "Production-ready roadmap",
            "Scaling strategy documentation",
            "Security recommendations",
            "DevOps configuration guides",
            "Maintenance plan"
          ];
        default:
          return [];
      }
    } else {
      return [];
    }
  };

  if (isLoading) {
    return (
      <SidebarProvider open={open} setOpen={setOpen}>
        <div className="min-h-screen flex bg-gradient-to-b from-background via-background/95 to-background">
          <UserSidebar open={open} setOpen={setOpen} />
          <div className="flex-1 p-6 md:p-8 flex items-center justify-center">
            <Card className="w-full max-w-3xl border animate-pulse">
              <CardHeader className="pb-8">
                <div className="h-8 bg-muted-foreground/20 w-48 rounded mb-2"></div>
                <div className="h-5 bg-muted-foreground/20 w-64 rounded"></div>
              </CardHeader>
              <CardContent className="pb-8 space-y-4">
                {Array(5).fill(0).map((_, i) => (
                  <div key={i} className="flex items-center">
                    <div className="w-5 h-5 rounded-full bg-muted-foreground/20 mr-3"></div>
                    <div className="h-4 bg-muted-foreground/20 w-full rounded"></div>
                  </div>
                ))}
              </CardContent>
              <CardFooter>
                <div className="h-10 bg-muted-foreground/20 w-full rounded"></div>
              </CardFooter>
            </Card>
          </div>
        </div>
      </SidebarProvider>
    );
  }

  if (!selectedPlan && !isLoading) {
    navigate("/pricing");
    return null;
  }

  return (
    <SidebarProvider open={open} setOpen={setOpen}>
      <div className="min-h-screen flex bg-gradient-to-b from-background via-background/95 to-background">
        <UserSidebar open={open} setOpen={setOpen} />
        <div className="flex-1 p-6 md:p-8">
          <div className="max-w-3xl mx-auto">
            <Button 
              variant="ghost" 
              className="mb-6 px-0 text-muted-foreground hover:text-foreground"
              onClick={() => navigate('/pricing')}
            >
              <ChevronLeft className="mr-1 h-4 w-4" />
              Back to Pricing
            </Button>

            {selectedPlan && (
              <Card className="border shadow-sm">
                <CardHeader className="pb-6">
                  <div className="flex items-center gap-2 mb-2">
                    {selectedPlan.icon}
                    <CardTitle className="text-2xl">
                      {selectedPlan.name}
                      {selectedPlan.id === 'multi-idea' && selectedPlan.count !== "1" && (
                        <span className="ml-2 text-sm font-normal text-muted-foreground">
                          ({selectedPlan.count} Ideas)
                        </span>
                      )}
                      {selectedPlan.id === 'roadmap-pack' && (
                        <span className="ml-2 text-sm font-normal text-muted-foreground">
                          ({selectedPlan.packType === 'poc' ? 'POC' : 
                             selectedPlan.packType === 'poc-mvp' ? 'POC & MVP' : 'All Roadmaps'})
                        </span>
                      )}
                    </CardTitle>
                  </div>
                  <CardDescription className="text-base">
                    {selectedPlan.description}
                    {selectedPlan.projectId && (
                      <span className="block mt-1 text-sm font-medium text-blue-600 dark:text-blue-400">
                        This purchase will unlock access for the current project only.
                      </span>
                    )}
                  </CardDescription>
                </CardHeader>

                <CardContent className="space-y-6">
                  <div className="p-4 bg-muted/50 rounded-lg">
                    <div className="text-2xl font-bold mb-2">
                      ${selectedPlan.price.toFixed(2)}
                      <span className="text-sm font-normal text-muted-foreground ml-1">
                        {selectedPlan.interval}
                      </span>
                    </div>
                    <p className="text-sm text-muted-foreground">
                      {selectedPlan.id === 'multi-idea' 
                        ? `One-time payment for ${ideaCount} additional idea validation${ideaCount === "1" ? "" : "s"}`
                        : selectedPlan.id === 'roadmap-pack'
                          ? `One-time payment for ${selectedPlan.projectId ? 'project-specific' : 'permanent'} access to selected roadmaps`
                          : "One-time payment for permanent access to this plan"}
                    </p>
                  </div>

                  <div>
                    <h3 className="text-lg font-medium mb-4">What's included:</h3>
                    <ul className="space-y-2">
                      {getDetailedFeatures(selectedPlan.id, selectedPlan.packType).map((feature, index) => (
                        <li key={index} className="flex items-start">
                          <Check className="text-green-500 mr-2 h-5 w-5 flex-shrink-0 mt-0.5" />
                          <span>{feature}</span>
                        </li>
                      ))}
                    </ul>
                  </div>

                  <Separator />

                  <div>
                    <h3 className="text-lg font-medium mb-4">Benefits:</h3>
                    <ul className="space-y-2">
                      {selectedPlan.id === 'multi-idea' ? (
                        <>
                          <li className="flex items-start">
                            <Check className="text-green-500 mr-2 h-5 w-5 flex-shrink-0 mt-0.5" />
                            <span>Validate multiple project ideas</span>
                          </li>
                          <li className="flex items-start">
                            <Check className="text-green-500 mr-2 h-5 w-5 flex-shrink-0 mt-0.5" />
                            <span>Compare different concepts</span>
                          </li>
                          <li className="flex items-start">
                            <Check className="text-green-500 mr-2 h-5 w-5 flex-shrink-0 mt-0.5" />
                            <span>Identify the most promising opportunities</span>
                          </li>
                          <li className="flex items-start">
                            <Check className="text-green-500 mr-2 h-5 w-5 flex-shrink-0 mt-0.5" />
                            <span>Build a diverse project portfolio</span>
                          </li>
                        </>
                      ) : selectedPlan.id === 'roadmap-pack' ? (
                        <>
                          <li className="flex items-start">
                            <Check className="text-green-500 mr-2 h-5 w-5 flex-shrink-0 mt-0.5" />
                            <span>Detailed roadmaps for selected development phases</span>
                          </li>
                          <li className="flex items-start">
                            <Check className="text-green-500 mr-2 h-5 w-5 flex-shrink-0 mt-0.5" />
                            <span>AI-generated implementation guidelines</span>
                          </li>
                          <li className="flex items-start">
                            <Check className="text-green-500 mr-2 h-5 w-5 flex-shrink-0 mt-0.5" />
                            <span>{selectedPlan.projectId ? 'Project-specific access' : 'Permanent access'} to all purchased roadmaps</span>
                          </li>
                          <li className="flex items-start">
                            <Check className="text-green-500 mr-2 h-5 w-5 flex-shrink-0 mt-0.5" />
                            <span>Downloadable resources and templates</span>
                          </li>
                        </>
                      ) : (
                        <>
                          <li className="flex items-start">
                            <Check className="text-green-500 mr-2 h-5 w-5 flex-shrink-0 mt-0.5" />
                            <span>Detailed roadmap for this development phase</span>
                          </li>
                          <li className="flex items-start">
                            <Check className="text-green-500 mr-2 h-5 w-5 flex-shrink-0 mt-0.5" />
                            <span>AI-generated implementation guidelines</span>
                          </li>
                          <li className="flex items-start">
                            <Check className="text-green-500 mr-2 h-5 w-5 flex-shrink-0 mt-0.5" />
                            <span>Permanent access to all phase documents</span>
                          </li>
                          <li className="flex items-start">
                            <Check className="text-green-500 mr-2 h-5 w-5 flex-shrink-0 mt-0.5" />
                            <span>Downloadable resources and templates</span>
                          </li>
                        </>
                      )}
                    </ul>
                  </div>
                </CardContent>

                <CardFooter className="flex flex-col space-y-4 pt-4">
                  <Button 
                    onClick={handleProceedToPayment}
                    className="w-full"
                    size="lg"
                  >
                    Proceed to Payment
                  </Button>
                  <p className="text-xs text-center text-muted-foreground">
                    By proceeding, you agree to our Terms of Service and Privacy Policy
                  </p>
                </CardFooter>
              </Card>
            )}
          </div>
        </div>
      </div>
    </SidebarProvider>
  );
}
