
import { useEffect } from "react";
import { useNavigate } from "react-router-dom";
import Navigation from "@/components/Navigation";
import HeroSection from "@/components/HeroSection";
import { Benefits } from "@/components/Benefits";
import { Process } from "@/components/Process";
import { FAQ } from "@/components/FAQ";
import Footer from "@/components/Footer";
import HowItWorks from "@/components/HowItWorks";
import { Showcase } from "@/components/Showcase";
import About from "@/components/About";
import { useAuthStore } from "@/store/authStore";
import { useUserStore } from "@/store/userStore";
import { useSubscriptionStore } from "@/store/subscriptionStore";

export default function Index() {
  const navigate = useNavigate();
  const { isAuthenticated, isAdmin, isLoading } = useAuthStore();
  const { fetchProfile } = useUserStore();
  const { fetchCurrentSubscription, fetchFreePlanLimits } = useSubscriptionStore();

  useEffect(() => {
    // When authenticated, fetch necessary data
    const loadUserData = async () => {
      if (isAuthenticated) {
        await fetchProfile();
        await fetchCurrentSubscription();
        await fetchFreePlanLimits();
      }
    };
    
    loadUserData();
  }, [isAuthenticated, fetchProfile, fetchCurrentSubscription, fetchFreePlanLimits]);

  useEffect(() => {
    // Redirect based on authentication status only when first accessing the app
    // but don't redirect after user is already authenticated
    if (!isLoading && isAuthenticated && window.location.pathname === '/') {
      setTimeout(() => {
        if (isAdmin) {
          navigate('/admin/dashboard', { replace: true });
        } else {
          navigate('/dashboard', { replace: true });
        }
      }, 100); // Small delay to ensure all auth data is processed
    }
  }, [isAuthenticated, isAdmin, navigate, isLoading]);

  // Always show the landing page when visiting /index explicitly
  return (
    <div className="min-h-screen bg-background">
      <Navigation />
      <HeroSection />
      <About />
      <Benefits />
      <HowItWorks />
      <Process />
      <Showcase />
      <FAQ />
      <Footer />
    </div>
  );
}
