
import { useEffect, useState } from "react";
import { AdminSidebar } from "@/components/admin/AdminSidebar";
import { SidebarProvider } from "@/components/ui/sidebar";
import { useSidebarStore } from "@/store/sidebarStore";
import { Card, CardContent, CardHeader, CardTitle, CardDescription, CardFooter } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Separator } from "@/components/ui/separator";
import { Switch } from "@/components/ui/switch";
import { FormField, FormItem, FormLabel, FormControl, FormDescription, FormMessage, Form } from "@/components/ui/form";
import { Tabs, TabsList, TabsTrigger, TabsContent } from "@/components/ui/tabs";
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogFooter } from "@/components/ui/dialog";
import { AlertDialog, AlertDialogAction, AlertDialogCancel, AlertDialogContent, AlertDialogDescription, AlertDialogFooter, AlertDialogHeader, AlertDialogTitle } from "@/components/ui/alert-dialog";
import { supabase } from "@/integrations/supabase/client";
import { Loader2, Plus, Save, X, Edit, Trash2 } from "lucide-react";
import { toast } from "sonner";
import { useForm } from "react-hook-form";
import { z } from "zod";
import { zodResolver } from "@hookform/resolvers/zod";
import { Badge } from "@/components/ui/badge";

// Schema for plan form
const planSchema = z.object({
  id: z.string().min(2, "ID is required"),
  name: z.string().min(2, "Name is required"),
  price: z.coerce.number().min(0, "Price must be 0 or higher"),
  interval: z.string().min(1, "Interval is required"),
  features: z.object({
    validations: z.coerce.number().min(1, "Must allow at least 1 validation"),
    phases: z.array(z.string()).min(1, "Must include at least one phase")
  }),
  is_active: z.boolean().default(true)
});

// Schema for free plan limits
const freeLimitsSchema = z.object({
  idea_validations: z.coerce.number().min(1, "Must allow at least 1 validation"),
  unlocked_phases: z.array(z.string()).min(1, "Must include at least one phase")
});

export default function PlanSettings() {
  const { open, setOpen } = useSidebarStore();
  const [isLoading, setIsLoading] = useState(false);
  const [plans, setPlans] = useState([]);
  const [editingPlan, setEditingPlan] = useState(null);
  const [showDialog, setShowDialog] = useState(false);
  const [freePlanLimits, setFreePlanLimits] = useState(null);
  const [deletingPlan, setDeletingPlan] = useState(null);
  const [showDeleteDialog, setShowDeleteDialog] = useState(false);
  
  // Initialize forms
  const planForm = useForm({
    resolver: zodResolver(planSchema),
    defaultValues: {
      id: "",
      name: "",
      price: 0,
      interval: "month",
      features: {
        validations: 1,
        phases: ["Interactive Prototype"]
      },
      is_active: true
    }
  });
  
  const freeLimitsForm = useForm({
    resolver: zodResolver(freeLimitsSchema),
    defaultValues: {
      idea_validations: 1,
      unlocked_phases: ["Interactive Prototype"]
    }
  });
  
  // Development phases options from our roadmap
  const phaseOptions = [
    "Interactive Prototype",
    "POC",
    "MVP",
    "Production Ready"
  ];

  // Fetch plans
  const fetchPlans = async () => {
    try {
      setIsLoading(true);
      const { data, error } = await supabase.rpc('get_pricing_plans');
      
      if (error) throw error;
      
      setPlans(data || []);
    } catch (error) {
      console.error("Error fetching plans:", error);
      toast.error("Failed to load pricing plans");
    } finally {
      setIsLoading(false);
    }
  };
  
  // Fetch free plan limits
  const fetchFreePlanLimits = async () => {
    try {
      setIsLoading(true);
      const { data, error } = await supabase.rpc('get_free_plan_limits');
      
      if (error) throw error;
      
      setFreePlanLimits(data);
      
      // Update form with existing data
      if (data) {
        freeLimitsForm.reset({
          idea_validations: data.idea_validations,
          unlocked_phases: data.unlocked_phases
        });
      }
    } catch (error) {
      console.error("Error fetching free plan limits:", error);
      toast.error("Failed to load free plan limits");
    } finally {
      setIsLoading(false);
    }
  };
  
  // Handle edit plan
  const handleEditPlan = (plan) => {
    planForm.reset({
      id: plan.id,
      name: plan.name,
      price: plan.price,
      interval: plan.interval,
      features: {
        validations: plan.features?.validations || 1,
        phases: plan.features?.phases || ["Interactive Prototype"]
      },
      is_active: plan.is_active
    });
    setEditingPlan(plan);
    setShowDialog(true);
  };
  
  // Handle new plan
  const handleNewPlan = () => {
    planForm.reset({
      id: "",
      name: "",
      price: 0,
      interval: "month",
      features: {
        validations: 1,
        phases: ["Interactive Prototype"]
      },
      is_active: true
    });
    setEditingPlan(null);
    setShowDialog(true);
  };
  
  // Save plan
  const onSavePlan = async (values) => {
    try {
      setIsLoading(true);
      
      const { error } = await supabase.rpc('upsert_pricing_plan', {
        plan_id: values.id,
        plan_name: values.name,
        plan_price: values.price,
        plan_interval: values.interval,
        plan_features: values.features,
        is_plan_active: values.is_active
      });
      
      if (error) throw error;
      
      toast.success("Plan saved successfully");
      fetchPlans();
      setShowDialog(false);
    } catch (error) {
      console.error("Error saving plan:", error);
      toast.error("Failed to save plan");
    } finally {
      setIsLoading(false);
    }
  };

  // Handle delete plan
  const handleDeletePlan = (plan) => {
    setDeletingPlan(plan);
    setShowDeleteDialog(true);
  };
  
  // Delete plan
  const onDeletePlan = async () => {
    if (!deletingPlan) return;
    
    try {
      setIsLoading(true);
      
      const { error } = await supabase.rpc('delete_pricing_plan', {
        plan_id: deletingPlan.id
      });
      
      if (error) throw error;
      
      toast.success("Plan deleted successfully");
      fetchPlans();
      setShowDeleteDialog(false);
      setDeletingPlan(null);
    } catch (error) {
      console.error("Error deleting plan:", error);
      toast.error("Failed to delete plan");
    } finally {
      setIsLoading(false);
    }
  };
  
  // Save free plan limits
  const onSaveFreeLimits = async (values) => {
    try {
      setIsLoading(true);
      
      const { error } = await supabase.rpc('update_free_plan_limits', {
        new_limits: values
      });
      
      if (error) throw error;
      
      toast.success("Free plan limits updated successfully");
      fetchFreePlanLimits();
    } catch (error) {
      console.error("Error saving free plan limits:", error);
      toast.error("Failed to update free plan limits");
    } finally {
      setIsLoading(false);
    }
  };
  
  // Initial load
  useEffect(() => {
    fetchPlans();
    fetchFreePlanLimits();
  }, []);
  
  return (
    <SidebarProvider open={open} setOpen={setOpen}>
      <div className="min-h-screen flex bg-background">
        <AdminSidebar open={open} setOpen={setOpen} />
        <div className="flex-1 p-6 md:p-8">
          <div className="max-w-6xl mx-auto">
            <div className="flex justify-between items-center mb-6">
              <div>
                <h1 className="text-3xl font-bold">Pricing Plans</h1>
                <p className="text-muted-foreground">Manage pricing plans and feature access</p>
              </div>
              <Button onClick={handleNewPlan}>
                <Plus className="mr-2 h-4 w-4" />
                Add New Plan
              </Button>
            </div>
            
            <Tabs defaultValue="plans" className="space-y-6">
              <TabsList>
                <TabsTrigger value="plans">Active Plans</TabsTrigger>
                <TabsTrigger value="limits">Free Plan Limits</TabsTrigger>
              </TabsList>
              
              <TabsContent value="plans">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  {isLoading ? (
                    Array(2).fill(0).map((_, i) => (
                      <Card key={i} className="border animate-pulse">
                        <CardHeader className="pb-2">
                          <div className="h-7 bg-muted-foreground/20 w-24 rounded mb-2"></div>
                          <div className="h-5 bg-muted-foreground/20 w-32 rounded"></div>
                        </CardHeader>
                        <CardContent className="space-y-4">
                          {Array(3).fill(0).map((_, j) => (
                            <div key={j} className="h-4 bg-muted-foreground/20 w-full rounded"></div>
                          ))}
                        </CardContent>
                      </Card>
                    ))
                  ) : plans.length > 0 ? (
                    plans.map((plan) => (
                      <Card key={plan.id} className="relative">
                        <CardHeader className="pb-2">
                          <div className="flex justify-between items-start">
                            <div>
                              <Badge variant={plan.is_active ? "default" : "outline"} className="mb-2">
                                {plan.is_active ? "Active" : "Inactive"}
                              </Badge>
                              <CardTitle>{plan.name}</CardTitle>
                              <CardDescription className="text-2xl font-bold mt-1">
                                ${plan.price}
                                {plan.interval !== 'once' && (
                                  <span className="text-sm font-normal text-muted-foreground">/{plan.interval}</span>
                                )}
                              </CardDescription>
                            </div>
                            <div className="flex space-x-1">
                              <Button variant="ghost" size="icon" onClick={() => handleEditPlan(plan)}>
                                <Edit className="h-4 w-4" />
                              </Button>
                              <Button variant="ghost" size="icon" onClick={() => handleDeletePlan(plan)}>
                                <Trash2 className="h-4 w-4 text-red-500" />
                              </Button>
                            </div>
                          </div>
                        </CardHeader>
                        <CardContent className="space-y-4">
                          <div>
                            <h4 className="text-sm font-medium mb-1">Features:</h4>
                            <ul className="space-y-1">
                              <li className="text-sm flex items-center">
                                <span className="mr-2 h-1.5 w-1.5 rounded-full bg-primary flex-shrink-0"></span>
                                <span>Validations: {plan.features?.validations || 0}</span>
                              </li>
                              <li className="text-sm">
                                <div className="flex items-center">
                                  <span className="mr-2 h-1.5 w-1.5 rounded-full bg-primary flex-shrink-0"></span>
                                  <span>Development Phases:</span>
                                </div>
                                <div className="ml-4 mt-1 flex flex-wrap gap-1">
                                  {plan.features?.phases?.map((phase) => (
                                    <Badge key={phase} variant="outline" className="text-xs">
                                      {phase}
                                    </Badge>
                                  ))}
                                </div>
                              </li>
                            </ul>
                          </div>
                        </CardContent>
                      </Card>
                    ))
                  ) : (
                    <div className="col-span-2 text-center py-10">
                      <p className="text-muted-foreground">No plans found. Create your first plan.</p>
                    </div>
                  )}
                </div>
              </TabsContent>
              
              <TabsContent value="limits">
                <Card>
                  <CardHeader>
                    <CardTitle>Free Plan Limitations</CardTitle>
                    <CardDescription>
                      Configure the limits for free users. These settings determine what features are available without a paid subscription.
                    </CardDescription>
                  </CardHeader>
                  <CardContent>
                    {isLoading ? (
                      <div className="space-y-4 animate-pulse">
                        <div className="h-8 bg-muted-foreground/20 w-1/3 rounded mb-2"></div>
                        <div className="h-6 bg-muted-foreground/20 w-2/3 rounded mb-2"></div>
                        <div className="h-6 bg-muted-foreground/20 w-full rounded"></div>
                      </div>
                    ) : (
                      <Form {...freeLimitsForm}>
                        <form onSubmit={freeLimitsForm.handleSubmit(onSaveFreeLimits)} className="space-y-6">
                          <FormField
                            control={freeLimitsForm.control}
                            name="idea_validations"
                            render={({ field }) => (
                              <FormItem>
                                <FormLabel>Max Idea Validations</FormLabel>
                                <FormControl>
                                  <Input type="number" min={1} {...field} />
                                </FormControl>
                                <FormDescription>
                                  Maximum number of idea validations allowed for free users
                                </FormDescription>
                                <FormMessage />
                              </FormItem>
                            )}
                          />
                          
                          <div>
                            <Label>Unlocked Development Phases</Label>
                            <div className="grid grid-cols-1 sm:grid-cols-2 gap-4 mt-2">
                              {phaseOptions.map((phase) => (
                                <div key={phase} className="flex items-center space-x-2">
                                  <Switch
                                    id={`phase-${phase}`}
                                    checked={freeLimitsForm.watch("unlocked_phases")?.includes(phase)}
                                    onCheckedChange={(checked) => {
                                      const current = freeLimitsForm.getValues("unlocked_phases") || [];
                                      if (checked) {
                                        freeLimitsForm.setValue("unlocked_phases", [...current, phase]);
                                      } else {
                                        freeLimitsForm.setValue(
                                          "unlocked_phases",
                                          current.filter((p) => p !== phase)
                                        );
                                      }
                                    }}
                                  />
                                  <Label htmlFor={`phase-${phase}`}>{phase}</Label>
                                </div>
                              ))}
                            </div>
                          </div>
                          
                          <Button type="submit" disabled={isLoading}>
                            {isLoading ? (
                              <>
                                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                                Saving...
                              </>
                            ) : (
                              <>
                                <Save className="mr-2 h-4 w-4" />
                                Save Changes
                              </>
                            )}
                          </Button>
                        </form>
                      </Form>
                    )}
                  </CardContent>
                </Card>
              </TabsContent>
            </Tabs>
          </div>
        </div>
      </div>
      
      {/* Plan Edit Dialog */}
      <Dialog open={showDialog} onOpenChange={setShowDialog}>
        <DialogContent className="sm:max-w-[600px]">
          <DialogHeader>
            <DialogTitle>{editingPlan ? "Edit Plan" : "Create New Plan"}</DialogTitle>
          </DialogHeader>
          
          <Form {...planForm}>
            <form onSubmit={planForm.handleSubmit(onSavePlan)} className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <FormField
                  control={planForm.control}
                  name="id"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Plan ID</FormLabel>
                      <FormControl>
                        <Input 
                          {...field} 
                          placeholder="e.g., pro, business" 
                          disabled={!!editingPlan}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                
                <FormField
                  control={planForm.control}
                  name="name"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Plan Name</FormLabel>
                      <FormControl>
                        <Input {...field} placeholder="e.g., Pro, Business" />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>
              
              <div className="grid grid-cols-2 gap-4">
                <FormField
                  control={planForm.control}
                  name="price"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Price</FormLabel>
                      <FormControl>
                        <Input type="number" min={0} step="0.01" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                
                <FormField
                  control={planForm.control}
                  name="interval"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Billing Interval</FormLabel>
                      <FormControl>
                        <select 
                          className="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
                          {...field}
                        >
                          <option value="month">Monthly</option>
                          <option value="year">Yearly</option>
                          <option value="once">One-time</option>
                        </select>
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>
              
              <Separator />
              
              <FormField
                control={planForm.control}
                name="features.validations"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Number of Validations</FormLabel>
                    <FormControl>
                      <Input type="number" min={1} {...field} />
                    </FormControl>
                    <FormDescription>
                      How many idea validations this plan allows
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />
              
              <div>
                <Label>Included Development Phases</Label>
                <div className="grid grid-cols-1 sm:grid-cols-2 gap-4 mt-2">
                  {phaseOptions.map((phase) => (
                    <div key={phase} className="flex items-center space-x-2">
                      <Switch
                        id={`plan-phase-${phase}`}
                        checked={planForm.watch("features.phases")?.includes(phase)}
                        onCheckedChange={(checked) => {
                          const current = planForm.getValues("features.phases") || [];
                          if (checked) {
                            planForm.setValue("features.phases", [...current, phase]);
                          } else {
                            planForm.setValue(
                              "features.phases",
                              current.filter((p) => p !== phase)
                            );
                          }
                        }}
                      />
                      <Label htmlFor={`plan-phase-${phase}`}>{phase}</Label>
                    </div>
                  ))}
                </div>
              </div>
              
              <FormField
                control={planForm.control}
                name="is_active"
                render={({ field }) => (
                  <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4">
                    <div className="space-y-0.5">
                      <FormLabel className="text-base">Active Status</FormLabel>
                      <FormDescription>
                        Whether this plan is active and available for purchase
                      </FormDescription>
                    </div>
                    <FormControl>
                      <Switch
                        checked={field.value}
                        onCheckedChange={field.onChange}
                      />
                    </FormControl>
                  </FormItem>
                )}
              />
              
              <DialogFooter>
                <Button variant="outline" type="button" onClick={() => setShowDialog(false)}>
                  <X className="mr-2 h-4 w-4" />
                  Cancel
                </Button>
                <Button type="submit" disabled={isLoading}>
                  {isLoading ? (
                    <>
                      <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                      Saving...
                    </>
                  ) : (
                    <>
                      <Save className="mr-2 h-4 w-4" />
                      Save Plan
                    </>
                  )}
                </Button>
              </DialogFooter>
            </form>
          </Form>
        </DialogContent>
      </Dialog>

      {/* Delete Confirmation Dialog */}
      <AlertDialog open={showDeleteDialog} onOpenChange={setShowDeleteDialog}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Delete Plan</AlertDialogTitle>
            <AlertDialogDescription>
              Are you sure you want to delete the plan "{deletingPlan?.name}"? This action cannot be undone.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel onClick={() => setDeletingPlan(null)}>Cancel</AlertDialogCancel>
            <AlertDialogAction 
              onClick={onDeletePlan}
              className="bg-red-500 hover:bg-red-600 text-white"
              disabled={isLoading}
            >
              {isLoading ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Deleting...
                </>
              ) : (
                <>Delete</>
              )}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </SidebarProvider>
  );
}
