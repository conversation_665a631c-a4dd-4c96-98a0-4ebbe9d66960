import { useState, useEffect } from "react";
import { useNavigate, useLocation } from "react-router-dom";
import { Loader2, ChevronLeft, AlertCircle, Zap, Database } from "lucide-react";
import { supabase } from "@/integrations/supabase/client";
import { Button } from "@/components/ui/button";
import { Textarea } from "@/components/ui/textarea";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Progress } from "@/components/ui/progress";
import { UserSidebar } from "@/components/user/UserSidebar";
import { SidebarProvider } from "@/components/ui/sidebar";
import { useSidebarStore } from "@/store/sidebarStore";
import { useToast } from "@/hooks/use-toast";
import { useAuthStore } from "@/store/authStore";
import { useSubscriptionStore } from "@/store/subscriptionStore";
import { AlertBanner } from "@/components/ui/AlertBanner";
import { Badge } from "@/components/ui/badge";

const generateTitle = (idea: string): string => {
  const firstPeriodPos = idea.indexOf('.');
  if (firstPeriodPos > 0 && firstPeriodPos < 60) {
    return idea.substring(0, firstPeriodPos);
  }
  
  return idea.length > 60 ? `${idea.substring(0, 60)}...` : idea;
};

export default function ValidateIdea() {
  const { open, setOpen } = useSidebarStore();
  const navigate = useNavigate();
  const location = useLocation();
  const { toast } = useToast();
  const { userId, isAuthenticated } = useAuthStore();
  const { isPro, freePlanLimits, fetchFreePlanLimits } = useSubscriptionStore();
  
  const [idea, setIdea] = useState("");
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [validationId, setValidationId] = useState<string | null>(null);
  const [validationStatus, setValidationStatus] = useState<string | null>(null);
  const [validationDetail, setValidationDetail] = useState<string | null>(null);
  const [progress, setProgress] = useState(0);
  const [error, setError] = useState({ show: false, message: "" });
  const [completedValidationsCount, setCompletedValidationsCount] = useState(0);
  const [isCheckingLimits, setIsCheckingLimits] = useState(false);
  const [retryCount, setRetryCount] = useState(0);
  const [functionCallError, setFunctionCallError] = useState(false);
  const [useEnhancedValidation] = useState(true); // Always use enhanced validation

  useEffect(() => {
    if (location.state?.validationId) {
      setValidationId(location.state.validationId);
    }
    
    fetchFreePlanLimits();
    
    if (isAuthenticated && userId) {
      checkCompletedValidations();
    }
  }, [location.state, fetchFreePlanLimits, isAuthenticated, userId]);

  useEffect(() => {
    if (validationId) {
      const interval = setInterval(async () => {
        try {
          const { data, error } = await supabase
            .from('idea_validations')
            .select('status, statusDetail, originalIdea')
            .eq('id', validationId)
            .single();
          
          if (error) throw error;
          
          if (data) {
            setValidationStatus(data.status);
            setValidationDetail(data.statusDetail || null);
            setIdea(data.originalIdea || "");
            
            if (data.status === 'pending') setProgress(10);
            else if (data.status === 'analyzing') setProgress(50);
            else if (data.status === 'completed') {
              setProgress(100);
              clearInterval(interval);
              
              setTimeout(() => {
                navigate(`/validate-idea/results/${validationId}`, { 
                  state: { validationId } 
                });
              }, 1500);
            } else if (data.status === 'error') {
              clearInterval(interval);
              setError({
                show: true,
                message: data.statusDetail || "An error occurred during validation. Please try again."
              });
            }
          }
        } catch (err) {
          console.error("Error polling validation status:", err);
        }
      }, 3000);
      
      return () => clearInterval(interval);
    }
  }, [validationId, navigate]);

  const checkCompletedValidations = async () => {
    if (!isAuthenticated || !userId) return;
    
    setIsCheckingLimits(true);
    try {
      const { count, error } = await supabase
        .from('idea_validations')
        .select('*', { count: 'exact' })
        .eq('status', 'completed');
      
      if (error) {
        console.error('Error checking validation count:', error);
        throw error;
      }
      
      console.log('User has completed', count, 'validations');
      setCompletedValidationsCount(count || 0);
    } catch (err) {
      console.error('Failed to check validation limits:', err);
    } finally {
      setIsCheckingLimits(false);
    }
  };

  const handleSubmitIdea = async () => {
    if (idea.trim().length === 0) {
      setError({
        show: true,
        message: "Please provide some text for your idea."
      });
      return;
    }

    setError({ show: false, message: "" });
    setFunctionCallError(false);

    if (!isPro && (completedValidationsCount >= (freePlanLimits?.idea_validations || 0))) {
      console.log('User has reached validation limit', {
        completedCount: completedValidationsCount,
        freePlanLimit: freePlanLimits?.idea_validations
      });
      
      toast({
        title: "Validation limit reached",
        description: "Please upgrade to our Pro plan to validate more ideas.",
      });
      
      navigate('/pricing');
      return;
    }
    
    try {
      setIsSubmitting(true);
      
      const title = generateTitle(idea);
      
      const { data, error } = await supabase.from('idea_validations').insert({
        originalIdea: idea,
        status: 'pending',
        userId: userId,
        title: title
      }).select();
      
      if (error) throw error;
      
      if (data && data.length > 0) {
        const newValidationId = data[0].id;
        setValidationId(newValidationId);
        
        const { data: sessionData } = await supabase.auth.getSession();
        
        const supabaseUrl = "https://hvljvkfpbavbtlxzjkqs.supabase.co";
        const functionName = useEnhancedValidation ? 'enhanced-idea-validation-full' : 'validate-idea';
        
        console.log(`Calling edge function at: ${supabaseUrl}/functions/v1/${functionName}`);
        
        try {
          const functionResponse = await fetch(
            `${supabaseUrl}/functions/v1/${functionName}`,
            {
              method: 'POST',
              headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${sessionData.session?.access_token}`
              },
              body: JSON.stringify({
                idea,
                validationId: newValidationId
              })
            }
          );
          
          if (!functionResponse.ok) {
            let errorMessage = 'Failed to start validation process';
            try {
              const errorData = await functionResponse.json();
              errorMessage = errorData.error || errorMessage;
            } catch (parseError) {
              errorMessage = `${errorMessage}: ${functionResponse.status} ${functionResponse.statusText}`;
              console.error("Error response parse failed:", parseError);
            }
            throw new Error(errorMessage);
          }
          
          const contentType = functionResponse.headers.get('content-type');
          if (contentType && contentType.includes('application/json')) {
            try {
              await functionResponse.json();
            } catch (jsonError) {
              console.error("JSON parsing error:", jsonError);
              throw new Error("Invalid response format from server");
            }
          }
          
          toast({
            title: "Validation started",
            description: "Your idea is being analyzed. This may take a few minutes."
          });
        } catch (fetchError) {
          console.error("Fetch error:", fetchError);
          
          setValidationId(newValidationId);
          setFunctionCallError(true);
          
          toast({
            variant: "destructive",
            title: "Network error",
            description: "There was a problem communicating with our servers. Your idea has been saved, and we'll try to process it."
          });
          
          await supabase
            .from('idea_validations')
            .update({ 
              status: 'error',
              statusDetail: `Network error: ${fetchError.message || 'Failed to fetch'}` 
            })
            .eq('id', newValidationId);
            
          throw fetchError;
        }
      }
    } catch (error) {
      console.error("Error submitting idea:", error);
      setError({
        show: true,
        message: error instanceof Error ? error.message : "An unexpected error occurred. Please try again."
      });
      
      if (!functionCallError) {
        setValidationId(null);
      }
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleRetryValidation = async () => {
    if (!validationId) return;
    
    setRetryCount(prev => prev + 1);
    setFunctionCallError(false);
    setIsSubmitting(true);
    
    try {
      await supabase
        .from('idea_validations')
        .update({ 
          status: 'pending',
          statusDetail: 'Retrying validation...' 
        })
        .eq('id', validationId);
      
      const { data: sessionData } = await supabase.auth.getSession();
      const supabaseUrl = "https://hvljvkfpbavbtlxzjkqs.supabase.co";
      
      console.log(`Retrying edge function at: ${supabaseUrl}/functions/v1/validate-idea`);
      
      const functionResponse = await fetch(
        `${supabaseUrl}/functions/v1/validate-idea`,
        {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${sessionData.session?.access_token}`
          },
          body: JSON.stringify({
            idea,
            validationId
          })
        }
      );
      
      if (!functionResponse.ok) {
        throw new Error(`Server error: ${functionResponse.status} ${functionResponse.statusText}`);
      }
      
      toast({
        title: "Validation restarted",
        description: "Your idea is now being analyzed. This may take a few minutes."
      });
      
    } catch (error) {
      console.error("Error retrying validation:", error);
      
      toast({
        variant: "destructive",
        title: "Retry failed",
        description: "We couldn't restart validation at this time. Please try again later."
      });
      
      setFunctionCallError(true);
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleCancel = () => {
    navigate('/dashboard');
  };

  return (
    <SidebarProvider open={open} setOpen={setOpen}>
      <div className="min-h-screen flex bg-gradient-to-b from-background via-background/95 to-background">
        <UserSidebar open={open} setOpen={setOpen} />
        
        <div className="flex-1 p-6 md:p-8">
          <AlertBanner 
            variant="error"
            message={error.message}
            show={error.show}
            onClose={() => setError({ ...error, show: false })}
          />
          
          <div className="max-w-3xl mx-auto">
            <Button 
              variant="ghost" 
              className="mb-8 px-0 text-muted-foreground hover:text-foreground group transition-all duration-200"
              onClick={() => navigate('/dashboard')}
            >
              <ChevronLeft className="mr-1.5 h-4 w-4 group-hover:-translate-x-0.5 transition-transform" />
              <span className="group-hover:underline">Back to Dashboard</span>
            </Button>
            
            <Card className="border shadow-sm">
              <CardHeader>
                <CardTitle className="text-2xl">Validate Your Idea</CardTitle>
                <CardDescription>
                  Describe your idea in detail. Our AI will analyze it and provide validation across multiple dimensions.
                </CardDescription>
              </CardHeader>
              
              <CardContent className="space-y-6">
                {validationId ? (
                  <div className="space-y-6">
                    <div className="p-6 bg-primary/5 rounded-lg border border-primary/10">
                      <h3 className="font-medium mb-2">Your idea is being validated</h3>
                      <p className="text-sm text-muted-foreground mb-4">{validationDetail || "Processing your idea..."}</p>
                      
                      <div className="space-y-2">
                        <Progress value={progress} className="h-2" />
                        <div className="flex justify-between text-xs text-muted-foreground">
                          <span>Analyzing</span>
                          <span>{progress}%</span>
                        </div>
                      </div>
                    </div>
                    
                    <div className="bg-muted/50 p-4 rounded-md">
                      <h4 className="text-sm font-medium mb-2">Your submitted idea:</h4>
                      <p className="text-sm text-muted-foreground whitespace-pre-wrap">{idea}</p>
                    </div>
                    
                    {functionCallError && (
                      <div className="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 p-4 rounded-md">
                        <div className="flex items-start gap-3">
                          <AlertCircle className="h-5 w-5 text-red-500 mt-0.5 flex-shrink-0" />
                          <div>
                            <h4 className="text-sm font-medium mb-1">There was a problem starting the validation</h4>
                            <p className="text-sm text-muted-foreground mb-3">
                              Your idea has been saved, but we couldn't connect to our validation servers. This might be due to:
                            </p>
                            <ul className="text-sm text-muted-foreground list-disc pl-5 mb-4 space-y-1">
                              <li>A temporary network issue</li>
                              <li>High server load</li>
                              <li>A system maintenance</li>
                            </ul>
                            <Button 
                              onClick={handleRetryValidation} 
                              className="bg-red-100 hover:bg-red-200 text-red-700 border border-red-200"
                              disabled={isSubmitting}
                            >
                              {isSubmitting ? (
                                <>
                                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                                  Retrying...
                                </>
                              ) : (
                                <>Retry Validation ({retryCount > 0 ? `Attempt ${retryCount + 1}` : "Retry"})</>
                              )}
                            </Button>
                          </div>
                        </div>
                      </div>
                    )}
                    
                    <div className="flex justify-center">
                      <Button 
                        variant="outline" 
                        onClick={handleCancel}
                        className="w-full sm:w-auto"
                      >
                        Return to Dashboard
                      </Button>
                    </div>
                  </div>
                ) : (
                  <div className="space-y-6">
                    <div>
                      <Textarea 
                        placeholder="Describe your idea in detail. For example: A mobile app that helps pet owners find pet-friendly restaurants and cafes nearby, with reviews and ratings from other pet owners."
                        className="min-h-[200px] resize-none"
                        value={idea}
                        onChange={(e) => setIdea(e.target.value)}
                        disabled={isSubmitting}
                      />
                      <p className="text-xs text-muted-foreground mt-2">
                        The more details you provide, the better the validation will be.
                      </p>
                    </div>
                    
                    {!isPro && (
                      <div className="flex items-center p-4 bg-amber-50 dark:bg-amber-950/20 text-amber-800 dark:text-amber-300 rounded-md text-sm">
                        <AlertCircle className="h-4 w-4 mr-2 flex-shrink-0" />
                        <p>
                          Free plan users can validate up to {freePlanLimits?.idea_validations || '...'} ideas. 
                          You have used {completedValidationsCount} so far.
                        </p>
                      </div>
                    )}

                    {/* Info about enhanced validation */}
                    <div className="p-3 bg-blue-50 dark:bg-blue-950/20 text-blue-800 dark:text-blue-300 rounded-md text-sm">
                      <div className="flex items-start gap-2">
                        <Zap className="h-4 w-4 mt-0.5 flex-shrink-0" />
                        <div>
                          <p className="font-medium mb-1">Enhanced validation includes:</p>
                          <ul className="text-xs space-y-0.5">
                            <li>• Real-time market research with verified data sources</li>
                            <li>• Current market trends and competitive intelligence</li>
                            <li>• Verified citations from actual web sources</li>
                            <li>• Comprehensive analysis optimized for accuracy</li>
                          </ul>
                        </div>
                      </div>
                    </div>
                    
                    <div className="flex flex-col sm:flex-row gap-3 pt-2">
                      <Button 
                        className="w-full sm:w-auto"
                        onClick={handleSubmitIdea}
                        disabled={isSubmitting || isCheckingLimits || idea.trim().length === 0}
                      >
                        {isSubmitting || isCheckingLimits ? (
                          <>
                            <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                            {isSubmitting ? "Processing..." : "Checking..."}
                          </>
                        ) : (
                          <>
                            <>
                              <Zap className="mr-2 h-4 w-4" />
                              Validate Idea
                            </>
                          </>
                        )}
                      </Button>
                      
                      <Button 
                        variant="outline" 
                        onClick={handleCancel}
                        disabled={isSubmitting}
                        className="w-full sm:w-auto"
                      >
                        Cancel
                      </Button>
                    </div>
                  </div>
                )}
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </SidebarProvider>
  );
}
