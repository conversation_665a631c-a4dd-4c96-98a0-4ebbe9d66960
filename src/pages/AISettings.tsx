
import { useState } from "react";
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { supabase } from "@/integrations/supabase/client";
import { Skeleton } from "@/components/ui/skeleton";
import { SidebarProvider } from "@/components/ui/sidebar";
import AdminSidebar from "@/components/admin/AdminSidebar";
import { Switch } from "@/components/ui/switch";
import { Label } from "@/components/ui/label";
import { useToast } from "@/components/ui/use-toast";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { AlertCircle } from "lucide-react";
import { useSidebarStore } from "@/store/sidebarStore";

// Update type definition to match updated database schema
type AIProvider = {
  id: string;
  provider: 'openai' | 'anthropic' | 'deepseek';
  is_active: boolean;
  openai_model: 'gpt-4o' | 'gpt-4o-mini' | 'gpt-4o-2024-05' | 'o1' | 'o1-mini' | 'o2-preview' | 'o3-mini' | null;
  anthropic_model: 'claude-3-5-sonnet-latest' | 'claude-3-5-haiku-latest' | 'claude-3-opus-latest' | 'claude-3-7-sonnet-latest' | null;
  deepseek_model: 'deepseek-chat' | 'deepseek-reasoner' | 'deepseek-llm-70b' | null;
};

// Make sure this type matches the database schema
type AIProviderUpdate = {
  id?: string;
  provider: AIProvider['provider'];
  is_active?: boolean;
  openai_model?: AIProvider['openai_model'];
  anthropic_model?: AIProvider['anthropic_model']; 
  deepseek_model?: AIProvider['deepseek_model'];
};

export default function AISettings() {
  const { toast } = useToast();
  const { open, setOpen } = useSidebarStore();
  const queryClient = useQueryClient();

  const { data: settings, isLoading, error } = useQuery({
    queryKey: ['ai-settings'],
    queryFn: async () => {
      const { data, error } = await supabase
        .from('ai_provider_settings')
        .select('*');
      
      if (error) throw error;
      return data as AIProvider[];
    },
  });

  const updateSettings = useMutation({
    mutationFn: async (newSettings: AIProviderUpdate) => {
      const { data, error } = await supabase
        .from('ai_provider_settings')
        .upsert(newSettings)
        .select()
        .single();

      if (error) throw error;
      return data;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['ai-settings'] });
      toast({
        title: "Success",
        description: "Settings updated successfully.",
      });
    },
    onError: (error) => {
      toast({
        variant: "destructive",
        title: "Error",
        description: "Failed to update settings. Please try again.",
      });
    },
  });

  const handleProviderChange = async (provider: AIProvider['provider'], isActive: boolean) => {
    const existingSettings = settings?.find(s => s.provider === provider);
    
    if (!isActive) {
      // User is trying to deactivate the provider
      if (settings?.filter(s => s.is_active).length === 1 && existingSettings?.is_active) {
        toast({
          variant: "destructive",
          title: "Error",
          description: "At least one provider must be active.",
        });
        return;
      }
    }
    
    // Use updated types here
    const updatedSettings: AIProviderUpdate = {
      id: existingSettings?.id,
      provider,
      is_active: isActive,
      openai_model: provider === 'openai' ? (existingSettings?.openai_model || 'gpt-4o-mini') : null,
      anthropic_model: provider === 'anthropic' ? (existingSettings?.anthropic_model || 'claude-3-5-haiku-latest') : null,
      deepseek_model: provider === 'deepseek' ? (existingSettings?.deepseek_model || 'deepseek-chat') : null,
    };

    // If activating a provider, deactivate others
    if (isActive) {
      for (const setting of settings || []) {
        if (setting.provider !== provider && setting.is_active) {
          await updateSettings.mutateAsync({
            id: setting.id,
            provider: setting.provider,
            is_active: false,
          });
        }
      }
    }

    await updateSettings.mutateAsync(updatedSettings);
  };

  const handleModelChange = async (model: string, provider: AIProvider['provider'], isActive: boolean) => {
    if (!isActive) {
      toast({
        variant: "destructive",
        title: "Error",
        description: "At least one model must be selected.",
      });
      return;
    }
    
    const existingSettings = settings?.find(s => s.provider === provider);
    if (!existingSettings) return;

    const updateData: AIProviderUpdate = {
      id: existingSettings.id,
      provider: existingSettings.provider,
    };

    switch (provider) {
      case 'openai':
        // Ensure we only use valid OpenAI models
        if (model === 'gpt-4o' || 
            model === 'gpt-4o-mini' || 
            model === 'gpt-4o-2024-05' ||
            model === 'o1' || 
            model === 'o1-mini' || 
            model === 'o2-preview' ||
            model === 'o3-mini') {
          updateData.openai_model = model as AIProvider['openai_model'];
        }
        break;
      case 'anthropic':
        // Ensure we only use valid Anthropic models
        if (model === 'claude-3-5-sonnet-latest' || 
            model === 'claude-3-5-haiku-latest' || 
            model === 'claude-3-opus-latest' ||
            model === 'claude-3-7-sonnet-latest') {
          updateData.anthropic_model = model as AIProvider['anthropic_model'];
        }
        break;
      case 'deepseek':
        // Ensure we only use valid DeepSeek models
        if (model === 'deepseek-chat' || 
            model === 'deepseek-reasoner' ||
            model === 'deepseek-llm-70b') {
          updateData.deepseek_model = model as AIProvider['deepseek_model'];
        }
        break;
    }

    await updateSettings.mutateAsync(updateData);
  };

  return (
    <SidebarProvider open={open} setOpen={setOpen}>
      <div className="min-h-screen flex bg-gradient-to-b from-background via-background/95 to-background">
        <AdminSidebar open={open} setOpen={setOpen} />
        <div className="flex-1 p-8">
          <div className="max-w-6xl mx-auto">
            <div className="mb-8">
              <h1 className="text-3xl font-bold text-primary">Settings</h1>
              <p className="text-muted-foreground mt-2">Configure AI providers and models</p>
            </div>

            <div className="space-y-8">
              {isLoading ? (
                <div className="space-y-4">
                  {[...Array(3)].map((_, i) => (
                    <div key={i} className="w-full h-16">
                      <Skeleton className="h-full w-full" />
                    </div>
                  ))}
                </div>
              ) : error ? (
                <Alert variant="destructive">
                  <AlertCircle className="h-4 w-4" />
                  <AlertTitle>Error</AlertTitle>
                  <AlertDescription>
                    Failed to load settings. Please check your admin permissions.
                  </AlertDescription>
                </Alert>
              ) : (
                <>
                  <div className="bg-card/60 backdrop-blur-sm rounded-xl border border-primary/10 shadow-lg p-6">
                    <h2 className="text-xl font-semibold mb-4">Select AI Provider</h2>
                    <div className="space-y-4">
                      {settings?.map((setting) => (
                        <div key={setting.provider} className="flex items-center justify-between">
                          <Label htmlFor={`provider-${setting.provider}`} className="flex items-center gap-2">
                            {setting.provider.charAt(0).toUpperCase() + setting.provider.slice(1)}
                          </Label>
                          <Switch
                            id={`provider-${setting.provider}`}
                            checked={setting.is_active}
                            onCheckedChange={(checked) => handleProviderChange(setting.provider, checked)}
                          />
                        </div>
                      ))}
                    </div>
                  </div>

                  {settings?.map((setting) => setting.is_active && (
                    <div key={setting.id} className="bg-card/60 backdrop-blur-sm rounded-xl border border-primary/10 shadow-lg p-6">
                      <h2 className="text-xl font-semibold mb-4">Select Model for {setting.provider.charAt(0).toUpperCase() + setting.provider.slice(1)}</h2>
                      <div className="space-y-4">
                        {setting.provider === 'openai' && (
                          <>
                            <div className="flex items-center justify-between">
                              <Label htmlFor="gpt-4o" className="flex items-center gap-2">
                                GPT-4o (Most Capable)
                              </Label>
                              <Switch
                                id="gpt-4o"
                                checked={setting.openai_model === 'gpt-4o'}
                                onCheckedChange={(checked) => handleModelChange('gpt-4o', 'openai', checked)}
                              />
                            </div>
                            <div className="flex items-center justify-between">
                              <Label htmlFor="gpt-4o-mini" className="flex items-center gap-2">
                                GPT-4o Mini (Fast & Efficient)
                              </Label>
                              <Switch
                                id="gpt-4o-mini"
                                checked={setting.openai_model === 'gpt-4o-mini'}
                                onCheckedChange={(checked) => handleModelChange('gpt-4o-mini', 'openai', checked)}
                              />
                            </div>
                            <div className="flex items-center justify-between">
                              <Label htmlFor="gpt-4o-2024-05" className="flex items-center gap-2">
                                GPT-4o 2024-05 (Latest Version)
                              </Label>
                              <Switch
                                id="gpt-4o-2024-05"
                                checked={setting.openai_model === 'gpt-4o-2024-05'}
                                onCheckedChange={(checked) => handleModelChange('gpt-4o-2024-05', 'openai', checked)}
                              />
                            </div>
                            <div className="flex items-center justify-between">
                              <Label htmlFor="o1" className="flex items-center gap-2">
                                O1 (Advanced Reasoning)
                              </Label>
                              <Switch
                                id="o1"
                                checked={setting.openai_model === 'o1'}
                                onCheckedChange={(checked) => handleModelChange('o1', 'openai', checked)}
                              />
                            </div>
                            <div className="flex items-center justify-between">
                              <Label htmlFor="o1-mini" className="flex items-center gap-2">
                                O1 Mini (Efficient Reasoning)
                              </Label>
                              <Switch
                                id="o1-mini"
                                checked={setting.openai_model === 'o1-mini'}
                                onCheckedChange={(checked) => handleModelChange('o1-mini', 'openai', checked)}
                              />
                            </div>
                            <div className="flex items-center justify-between">
                              <Label htmlFor="o2-preview" className="flex items-center gap-2">
                                O2 Preview (Next-Gen Reasoning)
                              </Label>
                              <Switch
                                id="o2-preview"
                                checked={setting.openai_model === 'o2-preview'}
                                onCheckedChange={(checked) => handleModelChange('o2-preview', 'openai', checked)}
                              />
                            </div>
                            <div className="flex items-center justify-between">
                              <Label htmlFor="o3-mini" className="flex items-center gap-2">
                                O3 Mini (Vision Optimized)
                              </Label>
                              <Switch
                                id="o3-mini"
                                checked={setting.openai_model === 'o3-mini'}
                                onCheckedChange={(checked) => handleModelChange('o3-mini', 'openai', checked)}
                              />
                            </div>
                          </>
                        )}
                        {setting.provider === 'anthropic' && (
                          <>
                            <div className="flex items-center justify-between">
                              <Label htmlFor="claude-3-opus-latest" className="flex items-center gap-2">
                                Claude 3 Opus (Most Powerful)
                              </Label>
                              <Switch
                                id="claude-3-opus-latest"
                                checked={setting.anthropic_model === 'claude-3-opus-latest'}
                                onCheckedChange={(checked) => handleModelChange('claude-3-opus-latest', 'anthropic', checked)}
                              />
                            </div>
                            <div className="flex items-center justify-between">
                              <Label htmlFor="claude-3-5-sonnet-latest" className="flex items-center gap-2">
                                Claude 3.5 Sonnet (Enhanced Performance)
                              </Label>
                              <Switch
                                id="claude-3-5-sonnet-latest"
                                checked={setting.anthropic_model === 'claude-3-5-sonnet-latest'}
                                onCheckedChange={(checked) => handleModelChange('claude-3-5-sonnet-latest', 'anthropic', checked)}
                              />
                            </div>
                            <div className="flex items-center justify-between">
                              <Label htmlFor="claude-3-7-sonnet-latest" className="flex items-center gap-2">
                                Claude 3.7 Sonnet (Latest Model)
                              </Label>
                              <Switch
                                id="claude-3-7-sonnet-latest"
                                checked={setting.anthropic_model === 'claude-3-7-sonnet-latest'}
                                onCheckedChange={(checked) => handleModelChange('claude-3-7-sonnet-latest', 'anthropic', checked)}
                              />
                            </div>
                            <div className="flex items-center justify-between">
                              <Label htmlFor="claude-3-5-haiku-latest" className="flex items-center gap-2">
                                Claude 3.5 Haiku (Fast & Efficient)
                              </Label>
                              <Switch
                                id="claude-3-5-haiku-latest"
                                checked={setting.anthropic_model === 'claude-3-5-haiku-latest'}
                                onCheckedChange={(checked) => handleModelChange('claude-3-5-haiku-latest', 'anthropic', checked)}
                              />
                            </div>
                          </>
                        )}
                        {setting.provider === 'deepseek' && (
                          <>
                            <div className="flex items-center justify-between">
                              <Label htmlFor="deepseek-chat" className="flex items-center gap-2">
                                DeepSeek Chat (General Purpose)
                              </Label>
                              <Switch
                                id="deepseek-chat"
                                checked={setting.deepseek_model === 'deepseek-chat'}
                                onCheckedChange={(checked) => handleModelChange('deepseek-chat', 'deepseek', checked)}
                              />
                            </div>
                            <div className="flex items-center justify-between">
                              <Label htmlFor="deepseek-reasoner" className="flex items-center gap-2">
                                DeepSeek Reasoner (Advanced Logic)
                              </Label>
                              <Switch
                                id="deepseek-reasoner"
                                checked={setting.deepseek_model === 'deepseek-reasoner'}
                                onCheckedChange={(checked) => handleModelChange('deepseek-reasoner', 'deepseek', checked)}
                              />
                            </div>
                            <div className="flex items-center justify-between">
                              <Label htmlFor="deepseek-llm-70b" className="flex items-center gap-2">
                                DeepSeek LLM 70B (Large Scale Model)
                              </Label>
                              <Switch
                                id="deepseek-llm-70b"
                                checked={setting.deepseek_model === 'deepseek-llm-70b'}
                                onCheckedChange={(checked) => handleModelChange('deepseek-llm-70b', 'deepseek', checked)}
                              />
                            </div>
                          </>
                        )}
                      </div>
                    </div>
                  ))}
                </>
              )}
            </div>
          </div>
        </div>
      </div>
    </SidebarProvider>
  );
}
