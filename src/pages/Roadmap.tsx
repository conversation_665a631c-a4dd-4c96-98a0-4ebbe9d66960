
import { useEffect, useState, use<PERSON><PERSON>back, useMemo } from "react";
import { useParams, useNavigate, useLocation } from "react-router-dom";
import { useAuthStore } from "@/store/authStore";
import { useRoadmapStore } from "@/store/roadmapStore";
import { supabase } from "@/integrations/supabase/client";
import { SidebarProvider } from "@/components/ui/sidebar";
import { useSidebarStore } from "@/store/sidebarStore";
import { AdminSidebar } from "@/components/admin/AdminSidebar";
import { UserSidebar } from "@/components/user/UserSidebar";
import { Card, CardContent } from "@/components/ui/card";
import { Skeleton } from "@/components/ui/skeleton";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Badge } from "@/components/ui/badge";
import { But<PERSON> } from "@/components/ui/button";
import { toast } from "sonner";
import { Loader2, <PERSON><PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, Spark<PERSON>, Code, Users } from "lucide-react";
import RoadmapPhase from "@/components/roadmap/RoadmapPhase";
import { DEVELOPMENT_PHASES, ProjectPlanning } from "@/types";
import { useSubscriptionStore } from "@/store/subscriptionStore";

interface Validation {
  id: string;
  userId: string;
  originalIdea: string;
  projectConcept: any;
}

export default function Roadmap() {
  const { id: validationId } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const location = useLocation();
  const { roadmaps, isLoading, fetchRoadmaps } = useRoadmapStore();
  const { isAdmin, userId } = useAuthStore();
  const { open, setOpen } = useSidebarStore();
  const [validation, setValidation] = useState<Validation | null>(null);
  const [planningData, setPlanningData] = useState<ProjectPlanning | null>(null);
  const [pageLoading, setPageLoading] = useState(true);
  const [availablePhases, setAvailablePhases] = useState<string[]>(['interactive']);
  const { getUserAccessiblePhases, clearAccessCache, fetchCurrentSubscription } = useSubscriptionStore();

  const [accessState, setAccessState] = useState({
    accessiblePhases: ['interactive'] as string[],
    selectedTab: 'interactive' as string,
    accessChecked: false
  });

  // Clear cache and refresh subscription data on route change or component mount
  useEffect(() => {
    clearAccessCache();
    fetchCurrentSubscription();
    
    // Reset access state whenever the component mounts or route changes
    setAccessState(prev => ({
      ...prev,
      accessChecked: false
    }));
  }, [clearAccessCache, fetchCurrentSubscription, location.key]);

  // Load validation data and planning data
  useEffect(() => {
    if (!validationId) {
      navigate("/dashboard");
      return;
    }

    const loadData = async () => {
      try {
        setPageLoading(true);
        
        const { data, error } = await supabase
          .from("idea_validations")
          .select("id, userId, originalIdea, projectConcept")
          .eq("id", validationId)
          .single();

        if (error) {
          console.error("Error loading validation:", error);
          throw error;
        }

        if (!isAdmin && data.userId !== userId) {
          toast.error("You don't have permission to view this validation");
          navigate("/dashboard");
          return;
        }

        setValidation(data);
        
        const { data: planData, error: planningError } = await supabase
          .from("project_planning")
          .select("development_phases, platforms, technology_stack, branding_preferences, monthly_users, timeline, budget_range")
          .eq("validation_id", validationId)
          .maybeSingle();
          
        if (!planningError && planData) {
          const ensureStringArray = (value: any): string[] => {
            if (!value) return [];
            if (typeof value === 'string') return [value];
            if (Array.isArray(value)) {
              return value.map(item => String(item));
            }
            return [];
          };
          
          const processedPlanningData: ProjectPlanning = {
            id: '',
            validation_id: validationId,
            platforms: ensureStringArray(planData.platforms),
            development_phases: ensureStringArray(planData.development_phases),
            technology_stack: ensureStringArray(planData.technology_stack),
            branding_preferences: ensureStringArray(planData.branding_preferences),
            monthly_users: planData.monthly_users ? Number(planData.monthly_users) : null,
            timeline: planData.timeline ? String(planData.timeline) : null,
            budget_range: planData.budget_range ? String(planData.budget_range) : null,
            created_at: '',
            updated_at: ''
          };
          
          setPlanningData(processedPlanningData);
        }
        
        await fetchRoadmaps(validationId);
      } catch (error) {
        console.error("Error loading validation:", error);
        toast.error("Failed to load this project");
        navigate("/dashboard");
      } finally {
        setPageLoading(false);
      }
    };

    loadData();
  }, [validationId, navigate, userId, fetchRoadmaps, isAdmin]);

  // Determine available phases from planning data or roadmaps
  const getAvailablePhases = useMemo(() => {
    if (planningData?.development_phases && planningData.development_phases.length > 0) {
      return planningData.development_phases;
    }
    
    if (roadmaps && roadmaps.length > 0) {
      return [...new Set(roadmaps.map(roadmap => roadmap.phase))];
    }
    
    return ["interactive"];
  }, [planningData, roadmaps]);

  // Update available phases when planning data or roadmaps change
  useEffect(() => {
    if (!pageLoading) {
      const phases = getAvailablePhases;
      setAvailablePhases(phases);
    }
  }, [pageLoading, getAvailablePhases]);

  // Check user access to phases
  useEffect(() => {
    const checkAccess = async () => {
      if (pageLoading || accessState.accessChecked) return;
      
      try {
        if (isAdmin) {
          // Admins have access to all phases
          const allPhases = ["interactive", "poc", "mvp", "production"];
          setAccessState({
            accessiblePhases: allPhases,
            selectedTab: accessState.selectedTab || availablePhases[0] || 'interactive',
            accessChecked: true
          });
          return;
        }
        
        if (!validationId) {
          console.error("No validation ID available");
          return;
        }
        
        // Use the new function to get all accessible phases in one call
        const accessiblePhases = await getUserAccessiblePhases(validationId);
        
        // Find the first available phase that the user has access to
        let selectedTab = accessState.selectedTab;
        if (!accessiblePhases.includes(selectedTab)) {
          selectedTab = accessiblePhases[0] || 'interactive';
        }
        
        setAccessState({
          accessiblePhases,
          selectedTab,
          accessChecked: true
        });
      } catch (error) {
        console.error("Error checking phase access:", error);
        
        // Fallback to interactive prototype only
        setAccessState({
          accessiblePhases: ['interactive'],
          selectedTab: 'interactive',
          accessChecked: true
        });
      }
    };
    
    checkAccess();
  }, [pageLoading, validationId, isAdmin, accessState.accessChecked, availablePhases, getUserAccessiblePhases, accessState.selectedTab]);

  const handleTabChange = useCallback((value: string) => {
    if (accessState.accessiblePhases.includes(value)) {
      setAccessState(prev => ({
        ...prev,
        selectedTab: value
      }));
    }
  }, [accessState.accessiblePhases]);

  const getPhaseIcon = (phaseId: string) => {
    switch (phaseId) {
      case "interactive":
        return <Sparkles className="h-4 w-4 mr-1" />;
      case "poc":
        return <Code className="h-4 w-4 mr-1" />;
      case "mvp":
        return <Users className="h-4 w-4 mr-1" />;
      case "production":
        return <Rocket className="h-4 w-4 mr-1" />;
      default:
        return null;
    }
  };

  const getPhaseDisplayName = (phaseId: string) => {
    return DEVELOPMENT_PHASES[phaseId]?.name || phaseId;
  };

  const handleUpgradeClick = () => {
    navigate(`/pricing?project=${validationId}`);
  };

  if (pageLoading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <Loader2 className="h-8 w-8 animate-spin text-primary" />
      </div>
    );
  }

  if (!validation) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <Card className="border border-dashed">
          <CardContent className="py-10 text-center">
            <p className="text-muted-foreground">Failed to load project.</p>
          </CardContent>
        </Card>
      </div>
    );
  }

  // For admin, always show all four phases
  const displayPhases = isAdmin ? ["interactive", "poc", "mvp", "production"] : accessState.accessiblePhases;
  const hasLockedPhases = !isAdmin && availablePhases.length > accessState.accessiblePhases.length;

  return (
    <SidebarProvider open={open} setOpen={setOpen}>
      <div className="min-h-screen flex bg-gradient-to-b from-background via-background/95 to-background">
        {isAdmin ? <AdminSidebar open={open} setOpen={setOpen} /> : <UserSidebar open={open} setOpen={setOpen} />}
        <div className="flex-1 p-4 md:p-8">
          <div className="max-w-7xl mx-auto">
            <div className="mb-8">
              <div className="flex items-center gap-2 mb-4">
                <Button 
                  variant="ghost" 
                  size="sm" 
                  onClick={() => navigate(isAdmin ? "/admin/roadmaps" : "/dashboard")}
                  className="gap-1 hover:bg-gray-100 dark:hover:bg-gray-800 transition-colors"
                >
                  <ArrowLeft className="h-4 w-4" />
                  Back
                </Button>
              </div>
              
              <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-4">
                <div>
                  <h1 className="text-3xl font-bold mb-2 text-gray-900 dark:text-gray-50">
                    {validation.originalIdea}
                  </h1>
                  <p className="text-muted-foreground">
                    {isAdmin ? "Admin view - Roadmap management" : "Development roadmaps for your project"}
                  </p>
                </div>
                <div className="flex flex-wrap gap-2">
                  <Badge variant="secondary" className="shadow-sm">
                    Project ID: {validation.id.slice(0, 8)}...
                  </Badge>
                  {roadmaps.some(r => r.status === 'published') && (
                    <Badge variant="default" className="bg-gradient-to-r from-green-500 to-green-600 text-white shadow-sm">
                      <Rocket className="h-3 w-3 mr-1" />
                      Roadmap Available
                    </Badge>
                  )}
                </div>
              </div>
            </div>

            {hasLockedPhases && !isAdmin && (
              <div className="mb-8 p-6 bg-gradient-to-r from-blue-50 to-indigo-50 dark:from-blue-950/30 dark:to-indigo-950/30 border border-blue-100 dark:border-blue-800 rounded-xl shadow-sm">
                <div className="flex flex-col md:flex-row items-center justify-between gap-4">
                  <div className="flex items-center gap-4">
                    <div className="bg-blue-100 dark:bg-blue-900/50 p-3 rounded-full shadow-sm">
                      <Lock className="h-6 w-6 text-blue-600 dark:text-blue-400" />
                    </div>
                    <div>
                      <h3 className="font-medium text-lg text-blue-800 dark:text-blue-400 mb-1">
                        Premium Roadmap Phases
                      </h3>
                      <p className="text-sm text-blue-700 dark:text-blue-500 max-w-md">
                        Unlock Proof of Concept, MVP, and Production roadmaps to get comprehensive development plans for your project
                      </p>
                    </div>
                  </div>
                  <div>
                    <Button 
                      onClick={handleUpgradeClick}
                      className="min-w-[200px] bg-gradient-to-r from-blue-500 to-indigo-600 hover:from-blue-600 hover:to-indigo-700 shadow-md hover:shadow-lg transition-all duration-300"
                    >
                      <Sparkles className="mr-2 h-4 w-4" />
                      Unlock Premium Phases
                    </Button>
                  </div>
                </div>
              </div>
            )}

            {isLoading ? (
              <div className="flex items-center justify-center py-24">
                <Loader2 className="h-8 w-8 animate-spin text-primary" />
              </div>
            ) : (
              accessState.accessChecked && (
                <Tabs 
                  value={accessState.selectedTab}
                  onValueChange={handleTabChange}
                  defaultValue={accessState.accessiblePhases[0]}
                  className="w-full"
                >
                  <div className="mb-8 border-b">
                    <TabsList className="h-auto p-0 bg-transparent flex flex-wrap gap-1 w-full">
                      {displayPhases.map((phaseId) => {
                        const isActive = accessState.selectedTab === phaseId;
                        return (
                          <TabsTrigger 
                            key={phaseId} 
                            value={phaseId}
                            className={`
                              flex items-center gap-1 px-4 py-2.5 shadow-none border-b-2 rounded-none
                              ${isActive 
                                ? 'border-primary text-primary font-medium' 
                                : 'border-transparent text-muted-foreground hover:text-foreground hover:bg-gray-50 dark:hover:bg-gray-900'}
                              transition-all
                            `}
                          >
                            {getPhaseIcon(phaseId)}
                            {getPhaseDisplayName(phaseId)}
                          </TabsTrigger>
                        );
                      })}
                    </TabsList>
                  </div>
                  
                  {displayPhases.map((phaseId) => (
                    <TabsContent key={phaseId} value={phaseId}>
                      <div className="py-2">
                        <RoadmapPhase 
                          validationId={validationId ?? ''}
                          phase={phaseId}
                          isAdmin={isAdmin}
                        />
                      </div>
                    </TabsContent>
                  ))}
                </Tabs>
              )
            )}
          </div>
        </div>
      </div>
    </SidebarProvider>
  );
}
