import { AlertBanner } from "@/components/ui/AlertBanner";
import { Badge } from "@/components/ui/badge";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { SidebarProvider } from "@/components/ui/sidebar";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { UserSidebar } from "@/components/user/UserSidebar";
import { InvestorSlidesSection } from "@/components/validation/InvestorSlidesSection";
import { MermaidDiagram } from "@/components/validation/MermaidDiagram";
import { ProjectPlanningCard } from "@/components/validation/ProjectPlanningCard";
import { useToast } from "@/hooks/use-toast";
import { supabase } from "@/integrations/supabase/client";
import { useSidebarStore } from "@/store/sidebarStore";
import { useSubscriptionStore } from "@/store/subscriptionStore";
import { generateCategorySequenceDiagram, generateMarketOpportunityDiagram } from "@/utils/mermaidGenerator";
import { useQuery } from "@tanstack/react-query";
import {
    BarChart3,
    ChevronLeft,
    Clock,
    ExternalLink,
    FileText, GitBranch,
    Globe,
    Lightbulb,
    Loader2,
    PackageOpen,
    PresentationIcon,
    Smartphone,
    TrendingDown,
    TrendingUp,
    Users
} from "lucide-react";
import { useEffect, useState } from "react";
import { useLocation, useNavigate, useParams } from "react-router-dom";

function FeatureList({ features }: { features?: Array<{ description: string; platforms?: { web?: boolean; mobile?: boolean } }> }) {
  if (!features || features.length === 0) {
    return <p className="text-muted-foreground text-sm">No features in this category.</p>;
  }

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
      {features.map((feature, index) => (
        <Card key={index} className="border shadow-sm hover:shadow-md transition-all duration-300">
          <CardContent className="pt-4">
            <div className="flex items-start gap-3">
              <div className="flex-1">
                <p className="text-sm">{feature.description}</p>
                {feature.platforms && (
                  <div className="mt-2 flex gap-2">
                    {feature.platforms.web && (
                      <Badge variant="outline" className="bg-blue-50 dark:bg-blue-900/20 border-blue-200 dark:border-blue-700 flex items-center gap-1">
                        <Globe className="h-3 w-3" />
                        <span>Web</span>
                      </Badge>
                    )}
                    {feature.platforms.mobile && (
                      <Badge variant="outline" className="bg-green-50 dark:bg-green-900/20 border-green-200 dark:border-green-700 flex items-center gap-1">
                        <Smartphone className="h-3 w-3" />
                        <span>Mobile</span>
                      </Badge>
                    )}
                  </div>
                )}
              </div>
            </div>
          </CardContent>
        </Card>
      ))}
    </div>
  );
}

export default function ValidationResults() {
  const location = useLocation();
  const navigate = useNavigate();
  const { id } = useParams();
  const { open, setOpen } = useSidebarStore();
  const [activeTab, setActiveTab] = useState("concept");
  const [error, setError] = useState({ show: false, message: "" });
  const { toast } = useToast();

  const validationId = id || location.state?.validationId;

  const { data: validation, isLoading, isError } = useQuery({
    queryKey: ['validation', validationId],
    queryFn: async () => {
      if (!validationId) {
        throw new Error('No validation ID provided');
      }
      
      const { data, error } = await supabase
        .from('idea_validations')
        .select('*')
        .eq('id', validationId)
        .single();
      
      if (error) throw error;
      return data;
    },
    enabled: !!validationId,
    refetchInterval: (query) => {
      const queryData = query.state.data as { status?: string };
      return queryData?.status === 'analyzing' ? 5000 : false;
    }
  });

  const { data: planningData } = useQuery({
    queryKey: ['planning', validationId],
    queryFn: async () => {
      if (!validationId) return null;
      
      const { data, error } = await supabase
        .from('project_planning')
        .select('*')
        .eq('validation_id', validationId)
        .maybeSingle();
      
      if (error) throw error;
      return data;
    },
    enabled: !!validationId && validation?.status === 'completed'
  });

  useEffect(() => {
    if (isError || (!isLoading && !validation && !location.state?.validation)) {
      setError({
        show: true,
        message: "Failed to load validation results. Please try again or go back to dashboard."
      });
    }
  }, [isError, isLoading, validation, location.state]);

  const validationData = validation || location.state?.validation;

  const { isPro, freePlanLimits } = useSubscriptionStore();

  const handleNewValidation = async () => {
    if (isPro) {
      navigate('/validate-idea');
      return;
    }

    const { count, error } = await supabase
      .from('idea_validations')
      .select('*', { count: 'exact' })
      .eq('status', 'completed');

    if (error) {
      console.error('Error checking validation count:', error);
      toast({
        variant: "destructive",
        title: "Error",
        description: "Unable to check validation eligibility. Please try again."
      });
      return;
    }

    if (!freePlanLimits || (count && count >= freePlanLimits.idea_validations)) {
      toast({
        title: "Validation limit reached",
        description: "Please upgrade to our Pro plan to validate more ideas."
      });
      navigate('/pricing');
      return;
    }

    navigate('/validate-idea');
  };

  const handlePlanningNavigation = () => {
    console.log("Navigating to planning with validation ID:", validationId);
    navigate(`/validate-idea/results/${validationId}/planning`);
  };

  if (isLoading) {
    return (
      <SidebarProvider open={open} setOpen={setOpen}>
        <div className="min-h-screen flex bg-gradient-to-b from-background via-background/95 to-background">
          <UserSidebar open={open} setOpen={setOpen} />
          <div className="flex-1 p-6 md:p-8 flex items-center justify-center">
            <div className="text-center">
              <Loader2 className="mx-auto h-8 w-8 animate-spin text-primary" />
              <p className="mt-4 text-muted-foreground">Loading validation results...</p>
            </div>
          </div>
        </div>
      </SidebarProvider>
    );
  }

  if (!validationData) {
    return (
      <SidebarProvider open={open} setOpen={setOpen}>
        <div className="min-h-screen flex bg-gradient-to-b from-background via-background/95 to-background">
          <UserSidebar open={open} setOpen={setOpen} />
          <div className="flex-1 p-6 md:p-8">
            <AlertBanner
              variant="error"
              message="No validation data found. Please return to dashboard and try again."
              show={true}
              onClose={() => {}}
            />
            <div className="max-w-7xl mx-auto text-center mt-16">
              <Button 
                variant="default" 
                onClick={() => navigate('/dashboard')}
                className="mx-auto"
              >
                <ChevronLeft className="mr-1 h-4 w-4" />
                Back to Dashboard
              </Button>
            </div>
          </div>
        </div>
      </SidebarProvider>
    );
  }

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return new Intl.DateTimeFormat('en-US', {
      year: 'numeric', 
      month: 'long', 
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    }).format(date);
  };

  const ensureArray = <T,>(data: T | T[] | null | undefined): T[] => {
    if (!data) return [];
    return Array.isArray(data) ? data : [data];
  };

  const safeToString = (value: unknown): string => {
    if (value === null || value === undefined) return '';
    return String(value);
  };

  const logCitations = (section: string, data: any) => {
    if (data?.citations) {
      console.log(`${section} citations:`, data.citations);
    } else if (typeof data === 'object' && 'citations' in data) {
      console.log(`${section} citations from object:`, data.citations);
    } else {
      console.log(`No citations found for ${section}`, data);
    }
  };

  if (validationData.marketValidation) {
    logCitations('Market Validation', validationData.marketValidation);
  }
  
  if (validationData.targetUsers) {
    logCitations('Target Users', validationData.targetUsers);
  }

  return (
    <SidebarProvider open={open} setOpen={setOpen}>
      <div className="min-h-screen flex bg-gradient-to-b from-background via-background/95 to-background">
        <UserSidebar open={open} setOpen={setOpen} />
        <div className="flex-1 p-6 md:p-8">
          <AlertBanner 
            variant="error"
            message={error.message}
            show={error.show}
            onClose={() => setError({ ...error, show: false })}
          />
          
          <div className="max-w-7xl mx-auto">
            <div className="mb-12">
              <Button 
                variant="ghost" 
                className="mb-4 px-0 text-muted-foreground hover:text-foreground group transition-all duration-200"
                onClick={() => navigate('/dashboard')}
              >
                <ChevronLeft className="mr-1.5 h-4 w-4 group-hover:-translate-x-0.5 transition-transform" />
                <span className="group-hover:underline">Back to Dashboard</span>
              </Button>
              
              <div className="flex flex-col sm:flex-row sm:items-end justify-between gap-6 border-b pb-6 border-border/30">
                <div className="space-y-1">
                  <h1 className="text-3xl font-bold tracking-tight bg-gradient-to-r from-foreground to-foreground/70 bg-clip-text text-transparent">
                    {validationData.title || "Idea Validation Results"}
                  </h1>
                  <div className="flex items-center mt-2 text-sm text-muted-foreground">
                    <Clock className="mr-1.5 h-3.5 w-3.5" />
                    <span>Validated on {formatDate(validationData.created_at)}</span>
                    <span className="mx-2">•</span>
                    <Badge variant={validationData.status === 'completed' ? "success" : "secondary"} className="capitalize">
                      {validationData.status}
                    </Badge>
                  </div>
                </div>
                
                <div className="flex flex-wrap sm:flex-nowrap gap-3 sm:justify-end">
                  {validationData.status === 'completed' && (
                    <>
                      <Button
                        className="gap-2 w-full sm:w-auto"
                        onClick={handlePlanningNavigation}
                        variant={planningData ? "outline" : "default"}
                        size="sm"
                      >
                        <FileText className="h-4 w-4" />
                        <span>{planningData ? "Edit Planning Data" : "Start Project Planning"}</span>
                      </Button>
                      
                      {planningData && (
                        <Button
                          className="gap-2 w-full sm:w-auto"
                          onClick={() => navigate(`/validate-idea/results/${validationId}/roadmap`)}
                          variant="default"
                          size="sm"
                        >
                          <GitBranch className="h-4 w-4" />
                          <span>Development Roadmap</span>
                        </Button>
                      )}
                    </>
                  )}
                  <Button
                    className="w-full sm:w-auto bg-primary hover:bg-primary/90"
                    size="sm"
                    onClick={handleNewValidation}
                  >
                    Validate Another Idea
                  </Button>
                </div>
              </div>
            </div>

            <Card className="mb-8 border border-primary/10 shadow-sm hover:shadow-md transition-all duration-300">
              <CardHeader className="pb-3 bg-gradient-to-r from-primary/5 to-transparent">
                <CardTitle className="text-lg font-medium">Original Idea</CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-sm text-muted-foreground whitespace-pre-wrap leading-relaxed">{validationData.originalIdea}</p>
              </CardContent>
            </Card>

            {validationData.status === 'analyzing' && (
              <Card className="mb-8 border border-primary/10 shadow-sm bg-primary/5">
                <CardContent className="pt-6 pb-6 text-center">
                  <Loader2 className="mx-auto h-8 w-8 animate-spin text-primary mb-4" />
                  <h3 className="text-lg font-medium mb-2">Analysis in Progress</h3>
                  <p className="text-muted-foreground">
                    We're currently analyzing your idea. This process may take a few minutes.
                    This page will automatically update when the analysis is complete.
                  </p>
                </CardContent>
              </Card>
            )}

            {validationData.status === 'completed' && (
              <>
                {planningData && (
                  <ProjectPlanningCard 
                    planningData={planningData} 
                    validationId={validationId}
                    navigate={navigate}
                  />
                )}

                <div>
                  <Tabs defaultValue="concept" value={activeTab} onValueChange={setActiveTab} className="space-y-6">
                    <TabsList className="w-full bg-slate-50 dark:bg-slate-900/50 p-1 rounded-xl overflow-hidden shadow-sm border border-slate-200 dark:border-slate-800">
                      <TabsTrigger 
                        value="concept" 
                        className="flex-1 data-[state=active]:bg-white dark:data-[state=active]:bg-slate-800 data-[state=active]:shadow-sm rounded-lg py-3"
                      >
                        <div className="flex flex-col sm:flex-row items-center gap-2 justify-center">
                          <Lightbulb className="h-5 w-5 text-violet-500" />
                          <span>Project Concept</span>
                        </div>
                      </TabsTrigger>
                      <TabsTrigger 
                        value="users" 
                        className="flex-1 data-[state=active]:bg-white dark:data-[state=active]:bg-slate-800 data-[state=active]:shadow-sm rounded-lg py-3"
                      >
                        <div className="flex flex-col sm:flex-row items-center gap-2 justify-center">
                          <Users className="h-5 w-5 text-blue-500" />
                          <span>Target Users</span>
                        </div>
                      </TabsTrigger>
                      <TabsTrigger 
                        value="features" 
                        className="flex-1 data-[state=active]:bg-white dark:data-[state=active]:bg-slate-800 data-[state=active]:shadow-sm rounded-lg py-3"
                      >
                        <div className="flex flex-col sm:flex-row items-center gap-2 justify-center">
                          <PackageOpen className="h-5 w-5 text-emerald-500" />
                          <span>Features</span>
                        </div>
                      </TabsTrigger>
                      <TabsTrigger
                        value="market"
                        className="flex-1 data-[state=active]:bg-white dark:data-[state=active]:bg-slate-800 data-[state=active]:shadow-sm rounded-lg py-3"
                      >
                        <div className="flex flex-col sm:flex-row items-center gap-2 justify-center">
                          <BarChart3 className="h-5 w-5 text-amber-500" />
                          <span>Market</span>
                        </div>
                      </TabsTrigger>
                      <TabsTrigger
                        value="citations"
                        className="flex-1 data-[state=active]:bg-white dark:data-[state=active]:bg-slate-800 data-[state=active]:shadow-sm rounded-lg py-3"
                      >
                        <div className="flex flex-col sm:flex-row items-center gap-2 justify-center">
                          <ExternalLink className="h-5 w-5 text-purple-500" />
                          <span>Citations</span>
                        </div>
                      </TabsTrigger>
                      <TabsTrigger
                        value="pitchdeck"
                        className="flex-1 data-[state=active]:bg-white dark:data-[state=active]:bg-slate-800 data-[state=active]:shadow-sm rounded-lg py-3"
                      >
                        <div className="flex flex-col sm:flex-row items-center gap-2 justify-center">
                          <PresentationIcon className="h-5 w-5 text-indigo-500" />
                          <span>Pitch Deck</span>
                        </div>
                      </TabsTrigger>
                      <TabsTrigger
                        value="diagrams"
                        className="flex-1 data-[state=active]:bg-white dark:data-[state=active]:bg-slate-800 data-[state=active]:shadow-sm rounded-lg py-3 relative"
                      >
                        <div className="flex flex-col sm:flex-row items-center gap-2 justify-center">
                          <GitBranch className="h-5 w-5 text-pink-500" />
                          <span>Diagrams</span>
                          <Badge
                            variant="feature"
                            className="absolute -top-1 -right-1 transform translate-x-1/2 -translate-y-1/2 px-2 py-1 text-[11px] font-bold shadow-lg border-2 border-white dark:border-slate-800 animate-bounce"
                          >
                            <span className="relative inline-flex">
                              <span className="absolute -top-px -right-px w-2 h-2 rounded-full bg-violet-400 animate-ping"></span>
                              <span className="relative">BETA</span>
                            </span>
                          </Badge>
                        </div>
                      </TabsTrigger>
                    </TabsList>

                    <TabsContent value="concept" className="space-y-6">
                      <Card>
                        <CardHeader>
                          <CardTitle>Project Overview</CardTitle>
                          <CardDescription>High-level description of the project concept</CardDescription>
                        </CardHeader>
                        <CardContent>
                          <p className="text-sm whitespace-pre-wrap">{validationData.projectConcept?.overview}</p>
                        </CardContent>
                      </Card>


                      <Card>
                        <CardHeader>
                          <CardTitle>User Roles</CardTitle>
                          <CardDescription>Key users and their responsibilities</CardDescription>
                        </CardHeader>
                        <CardContent>
                          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                            {validationData.userRoles?.map((role, i) => (
                              <Card key={i} className="border shadow-sm">
                                <CardHeader className="pb-2">
                                  <CardTitle className="text-base">{role.title}</CardTitle>
                                  <CardDescription>{role.focus}</CardDescription>
                                </CardHeader>
                                <CardContent className="pt-0">
                                  <ul className="list-disc list-inside space-y-1">
                                    {role.responsibilities?.map((resp, j) => (
                                      <li key={j} className="text-sm">{resp}</li>
                                    ))}
                                  </ul>
                                </CardContent>
                              </Card>
                            ))}
                          </div>
                        </CardContent>
                      </Card>
                    </TabsContent>

                    <TabsContent value="users" className="space-y-4">
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                        {validationData.targetUsers?.map((user, i) => (
                          <Card key={i} className="border shadow-sm hover:shadow-md transition-all duration-300">
                            <CardHeader className="pb-3 bg-gradient-to-r from-blue-50 to-transparent dark:from-blue-900/20 dark:to-transparent">
                              <CardTitle className="text-base">{user.description}</CardTitle>
                              <div className="mt-2 flex flex-wrap gap-2">
                                <Badge variant="outline" className="bg-blue-50 dark:bg-blue-900/20 border-blue-200 dark:border-blue-700">
                                  Age: {user.demographics?.age}
                                </Badge>
                                <Badge variant="outline" className="bg-blue-50 dark:bg-blue-900/20 border-blue-200 dark:border-blue-700">
                                  {user.demographics?.occupation}
                                </Badge>
                                <Badge variant="outline" className="bg-blue-50 dark:bg-blue-900/20 border-blue-200 dark:border-blue-700 capitalize">
                                  {user.demographics?.technicalLevel}
                                </Badge>
                              </div>
                            </CardHeader>
                            <CardContent className="space-y-4">
                              <div>
                                <h4 className="font-medium text-sm mb-2 flex items-center gap-2">
                                  <TrendingDown className="h-4 w-4 text-red-500" />
                                  Pain Points
                                </h4>
                                <ul className="space-y-2">
                                  {user.painPoints?.map((point, j) => (
                                    <li key={j} className="text-sm flex items-start gap-2">
                                      <span className="inline-block h-1.5 w-1.5 bg-red-400 rounded-full mt-1.5 flex-shrink-0"></span>
                                      <span>{point}</span>
                                    </li>
                                  ))}
                                </ul>
                              </div>
                              
                              <div>
                                <h4 className="font-medium text-sm mb-2 flex items-center gap-2">
                                  <TrendingUp className="h-4 w-4 text-green-500" />
                                  Goals
                                </h4>
                                <ul className="space-y-2">
                                  {user.goals?.map((goal, j) => (
                                    <li key={j} className="text-sm flex items-start gap-2">
                                      <span className="inline-block h-1.5 w-1.5 bg-green-400 rounded-full mt-1.5 flex-shrink-0"></span>
                                      <span>{goal}</span>
                                    </li>
                                  ))}
                                </ul>
                              </div>
                            </CardContent>
                          </Card>
                        ))}
                      </div>
                      
                    </TabsContent>

                    <TabsContent value="features" className="space-y-6">
                      {validationData.projectConcept?.features && (
                        <Tabs defaultValue="core" className="w-full">
                          <TabsList className="w-full bg-slate-50 dark:bg-slate-900/50 p-1 rounded-xl overflow-hidden shadow-sm border border-slate-200 dark:border-slate-800 grid grid-cols-5">
                            <TabsTrigger 
                              value="core" 
                              className="data-[state=active]:bg-white dark:data-[state=active]:bg-slate-800 data-[state=active]:shadow-sm rounded-lg py-2.5"
                            >
                              Core
                            </TabsTrigger>
                            <TabsTrigger 
                              value="mustHave" 
                              className="data-[state=active]:bg-white dark:data-[state=active]:bg-slate-800 data-[state=active]:shadow-sm rounded-lg py-2.5"
                            >
                              Must-Have
                            </TabsTrigger>
                            <TabsTrigger 
                              value="shouldHave" 
                              className="data-[state=active]:bg-white dark:data-[state=active]:bg-slate-800 data-[state=active]:shadow-sm rounded-lg py-2.5"
                            >
                              Should-Have
                            </TabsTrigger>
                            <TabsTrigger 
                              value="niceToHave" 
                              className="data-[state=active]:bg-white dark:data-[state=active]:bg-slate-800 data-[state=active]:shadow-sm rounded-lg py-2.5"
                            >
                              Nice-to-Have
                            </TabsTrigger>
                            <TabsTrigger 
                              value="future" 
                              className="data-[state=active]:bg-white dark:data-[state=active]:bg-slate-800 data-[state=active]:shadow-sm rounded-lg py-2.5"
                            >
                              Future
                            </TabsTrigger>
                          </TabsList>
                          
                          <TabsContent value="core">
                            <FeatureList features={validationData.projectConcept?.features.core} />
                          </TabsContent>
                          <TabsContent value="mustHave">
                            <FeatureList features={validationData.projectConcept?.features.mustHave} />
                          </TabsContent>
                          <TabsContent value="shouldHave">
                            <FeatureList features={validationData.projectConcept?.features.shouldHave} />
                          </TabsContent>
                          <TabsContent value="niceToHave">
                            <FeatureList features={validationData.projectConcept?.features.niceToHave} />
                          </TabsContent>
                          <TabsContent value="future">
                            <FeatureList features={validationData.projectConcept?.features.future} />
                          </TabsContent>
                        </Tabs>
                      )}
                    </TabsContent>

                    <TabsContent value="market" className="space-y-6">
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <Card>
                          <CardHeader>
                            <CardTitle className="text-base">Market Size Analysis</CardTitle>
                            <CardDescription>Potential market scope and opportunity size</CardDescription>
                          </CardHeader>
                          <CardContent className="space-y-6">
                            {validationData.marketValidation?.marketSize ? (
                              <div className="space-y-6">
                                <div className="space-y-3">
                                  <div className="border-l-4 border-blue-500 pl-4">
                                    <h3 className="text-sm font-medium flex items-center">
                                      <Globe className="h-4 w-4 text-blue-500 mr-2" />
                                      Total Addressable Market (TAM)
                                    </h3>
                                    <p className="text-sm text-muted-foreground mt-1 whitespace-pre-wrap">
                                      {typeof validationData.marketValidation.marketSize === 'object' 
                                        ? validationData.marketValidation.marketSize.totalAddressableMarket 
                                        : validationData.marketValidation.marketSize}
                                    </p>
                                  </div>
                                </div>
                                
                                <div className="space-y-3">
                                  <div className="border-l-4 border-indigo-500 pl-4">
                                    <h3 className="text-sm font-medium flex items-center">
                                      <Users className="h-4 w-4 text-indigo-500 mr-2" />
                                      Serviceable Addressable Market (SAM)
                                    </h3>
                                    <p className="text-sm text-muted-foreground mt-1 whitespace-pre-wrap">
                                      {typeof validationData.marketValidation.marketSize === 'object' 
                                        ? validationData.marketValidation.marketSize.serviceableAddressableMarket 
                                        : "No SAM information available"}
                                    </p>
                                  </div>
                                </div>
                                
                                <div className="space-y-3">
                                  <div className="border-l-4 border-purple-500 pl-4">
                                    <h3 className="text-sm font-medium flex items-center">
                                      <BarChart3 className="h-4 w-4 text-purple-500 mr-2" />
                                      Serviceable Obtainable Market (SOM)
                                    </h3>
                                    <p className="text-sm text-muted-foreground mt-1 whitespace-pre-wrap">
                                      {typeof validationData.marketValidation.marketSize === 'object' 
                                        ? validationData.marketValidation.marketSize.serviceableObtainableMarket 
                                        : "No SOM information available"}
                                    </p>
                                  </div>
                                </div>
                              </div>
                            ) : (
                              <p className="text-sm text-muted-foreground">No market size data available</p>
                            )}
                          </CardContent>
                        </Card>
                        
                        <Card>
                          <CardHeader>
                            <CardTitle className="text-base">Target Market</CardTitle>
                            <CardDescription>Key market segments and opportunities</CardDescription>
                          </CardHeader>
                          <CardContent>
                            <p className="text-sm whitespace-pre-wrap">{validationData.marketValidation?.targetMarket || "No target market data available"}</p>
                          </CardContent>
                        </Card>
                      </div>
                      
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <Card>
                          <CardHeader>
                            <CardTitle className="text-base">Unique Value Proposition</CardTitle>
                            <CardDescription>What makes this product unique</CardDescription>
                          </CardHeader>
                          <CardContent>
                            <p className="text-sm whitespace-pre-wrap">{validationData.marketValidation?.uniqueValue || "No value proposition data available"}</p>
                          </CardContent>
                        </Card>
                        
                        <Card>
                          <CardHeader>
                            <CardTitle className="text-base">Monetization Strategies</CardTitle>
                            <CardDescription>Revenue generation approaches</CardDescription>
                          </CardHeader>
                          <CardContent>
                            {validationData.marketValidation?.monetizationStrategies && validationData.marketValidation.monetizationStrategies.length > 0 ? (
                              <ul className="space-y-2">
                                {validationData.marketValidation.monetizationStrategies.map((strategy, i) => (
                                  <li key={i} className="text-sm flex items-start gap-2">
                                    <span className="inline-block h-1.5 w-1.5 bg-green-400 rounded-full mt-1.5 flex-shrink-0"></span>
                                    <span>{strategy}</span>
                                  </li>
                                ))}
                              </ul>
                            ) : (
                              <p className="text-sm text-muted-foreground">No monetization strategies available</p>
                            )}
                          </CardContent>
                        </Card>
                      </div>
                      
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <Card>
                          <CardHeader>
                            <CardTitle className="text-base">Market Challenges</CardTitle>
                            <CardDescription>Potential obstacles to success</CardDescription>
                          </CardHeader>
                          <CardContent>
                            {validationData.marketValidation?.challenges && validationData.marketValidation.challenges.length > 0 ? (
                              <ul className="space-y-2">
                                {validationData.marketValidation.challenges.map((challenge, i) => (
                                  <li key={i} className="text-sm flex items-start gap-2">
                                    <span className="inline-block h-1.5 w-1.5 bg-red-400 rounded-full mt-1.5 flex-shrink-0"></span>
                                    <span>{challenge}</span>
                                  </li>
                                ))}
                              </ul>
                            ) : (
                              <p className="text-sm text-muted-foreground">No market challenges available</p>
                            )}
                          </CardContent>
                        </Card>
                        
                        <Card>
                          <CardHeader>
                            <CardTitle className="text-base">Market Opportunities</CardTitle>
                            <CardDescription>Potential growth areas</CardDescription>
                          </CardHeader>
                          <CardContent>
                            {validationData.marketValidation?.opportunities && validationData.marketValidation.opportunities.length > 0 ? (
                              <ul className="space-y-2">
                                {validationData.marketValidation.opportunities.map((opportunity, i) => (
                                  <li key={i} className="text-sm flex items-start gap-2">
                                    <span className="inline-block h-1.5 w-1.5 bg-green-400 rounded-full mt-1.5 flex-shrink-0"></span>
                                    <span>{opportunity}</span>
                                  </li>
                                ))}
                              </ul>
                            ) : (
                              <p className="text-sm text-muted-foreground">No market opportunities available</p>
                            )}
                          </CardContent>
                        </Card>
                      </div>
                      
                      
                      <Card>
                        <CardHeader>
                          <CardTitle className="text-base">Competitors</CardTitle>
                          <CardDescription>Key market players and their analysis</CardDescription>
                        </CardHeader>
                        <CardContent>
                          {validationData.marketValidation?.competitors && validationData.marketValidation.competitors.length > 0 ? (
                            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                              {validationData.marketValidation.competitors.map((competitor, i) => (
                                <Card key={i} className="border shadow-sm">
                                  <CardHeader className="pb-2">
                                    <CardTitle className="text-base">{competitor.name}</CardTitle>
                                  </CardHeader>
                                  <CardContent className="pt-2">
                                    <div className="space-y-3">
                                      <div>
                                        <h4 className="text-sm font-medium mb-1 flex items-center">
                                          <TrendingUp className="h-3.5 w-3.5 text-green-500 mr-1" />
                                          Strengths
                                        </h4>
                                        <ul className="pl-5 list-disc text-sm">
                                          {competitor.strengths?.map((strength, j) => (
                                            <li key={j}>{strength}</li>
                                          ))}
                                        </ul>
                                      </div>
                                      <div>
                                        <h4 className="text-sm font-medium mb-1 flex items-center">
                                          <TrendingDown className="h-3.5 w-3.5 text-red-500 mr-1" />
                                          Weaknesses
                                        </h4>
                                        <ul className="pl-5 list-disc text-sm">
                                          {competitor.weaknesses?.map((weakness, j) => (
                                            <li key={j}>{weakness}</li>
                                          ))}
                                        </ul>
                                      </div>
                                    </div>
                                  </CardContent>
                                </Card>
                              ))}
                            </div>
                          ) : (
                            <p className="text-sm text-muted-foreground">No competitor data available</p>
                          )}
                        </CardContent>
                      </Card>
                    </TabsContent>

                    <TabsContent value="citations" className="space-y-6">
                      {(() => {
                        const allCitations = [];

                        // Collect citations from different sources
                        if (validationData.projectConcept?.citations) {
                          allCitations.push(...validationData.projectConcept.citations.map(c => ({
                            ...c,
                            source_section: 'Project Concept'
                          })));
                        }

                        if (validationData.targetUsers && Array.isArray(validationData.targetUsers)) {
                          const userCitations = validationData.targetUsers.find(user => user.citations);
                          if (userCitations?.citations) {
                            allCitations.push(...userCitations.citations.map(c => ({
                              ...c,
                              source_section: 'Target Users'
                            })));
                          }
                        } else if (validationData.targetUsers?.citations) {
                          allCitations.push(...validationData.targetUsers.citations.map(c => ({
                            ...c,
                            source_section: 'Target Users'
                          })));
                        }

                        if (validationData.marketValidation?.citations) {
                          allCitations.push(...validationData.marketValidation.citations.map(c => ({
                            ...c,
                            source_section: 'Market Analysis'
                          })));
                        }

                        // Add real citations from enhanced validation
                        if (validationData.real_citations) {
                          allCitations.push(...validationData.real_citations.map(c => ({
                            ...c,
                            source_section: 'Market Research'
                          })));
                        }

                        if (allCitations.length === 0) {
                          return (
                            <Card>
                              <CardContent className="p-6 text-center">
                                <ExternalLink className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                                <p className="text-muted-foreground">No citations available for this validation.</p>
                              </CardContent>
                            </Card>
                          );
                        }

                        // Group citations by source section
                        const groupedCitations = allCitations.reduce((groups, citation) => {
                          const section = citation.source_section || 'Other';
                          if (!groups[section]) groups[section] = [];
                          groups[section].push(citation);
                          return groups;
                        }, {});

                        return (
                          <div className="space-y-6">
                            <Card>
                              <CardHeader>
                                <CardTitle className="flex items-center gap-2">
                                  <ExternalLink className="h-5 w-5" />
                                  All Citations
                                </CardTitle>
                                <CardDescription>
                                  Sources and references used in the validation analysis
                                </CardDescription>
                              </CardHeader>
                            </Card>

                            {Object.entries(groupedCitations).map(([section, citations]) => (
                              <Card key={section}>
                                <CardHeader>
                                  <CardTitle className="text-lg">{section}</CardTitle>
                                  <CardDescription>
                                    {citations.length} citation{citations.length !== 1 ? 's' : ''}
                                  </CardDescription>
                                </CardHeader>
                                <CardContent>
                                  <div className="space-y-4">
                                    {citations.map((citation, index) => (
                                      <div key={index} className="p-4 border rounded-lg hover:bg-muted/50 transition-colors">
                                        <div className="flex items-start justify-between gap-4">
                                          <div className="flex-1 space-y-2">
                                            <h4 className="font-medium text-sm leading-tight">
                                              {citation.title || 'Untitled Source'}
                                            </h4>
                                            <div className="flex items-center gap-4 text-xs text-muted-foreground">
                                              <span>{citation.source || new URL(citation.url || '#').hostname.replace('www.', '')}</span>
                                              {citation.date && <span>{citation.date}</span>}
                                              {citation.relevance && <span>Relevance: {citation.relevance}</span>}
                                            </div>
                                            {citation.snippet && (
                                              <p className="text-xs text-muted-foreground leading-relaxed">
                                                {citation.snippet}
                                              </p>
                                            )}
                                            {citation.relevance_text && (
                                              <p className="text-xs text-muted-foreground leading-relaxed">
                                                {citation.relevance_text}
                                              </p>
                                            )}
                                          </div>
                                          {citation.url && (
                                            <Button
                                              variant="ghost"
                                              size="sm"
                                              className="h-8 w-8 p-0 flex-shrink-0"
                                              onClick={() => window.open(citation.url, '_blank')}
                                            >
                                              <ExternalLink className="h-4 w-4" />
                                            </Button>
                                          )}
                                        </div>
                                      </div>
                                    ))}
                                  </div>
                                </CardContent>
                              </Card>
                            ))}
                          </div>
                        );
                      })()}
                    </TabsContent>

                    <TabsContent value="pitchdeck" className="space-y-6">
                      <div className="flex justify-between items-center mb-4">
                        <div>
                          <h2 className="text-xl font-semibold mb-1">AI-Powered Investor Presentation</h2>
                          <p className="text-sm text-muted-foreground">
                            Professional slides generated from your validation data
                          </p>
                        </div>
                        <Button variant="outline" size="sm" className="gap-2">
                          <FileText className="h-4 w-4" />
                          <span>Export Slides</span>
                        </Button>
                      </div>
                      
                      <InvestorSlidesSection validationData={validationData} />
                    </TabsContent>

                    <TabsContent value="diagrams" className="space-y-6">
                      <Card>
                        <CardHeader>
                          <CardTitle>Market Opportunity Diagram</CardTitle>
                          <CardDescription>Visualization of market opportunity and positioning</CardDescription>
                        </CardHeader>
                        <CardContent>
                          <MermaidDiagram 
                            definition={generateMarketOpportunityDiagram(validationData.marketValidation)} 
                            className="w-full"
                          />
                        </CardContent>
                      </Card>
                      
                      <Card>
                        <CardHeader>
                          <CardTitle>Feature Sequence Diagram</CardTitle>
                          <CardDescription>Feature implementation flow by category</CardDescription>
                        </CardHeader>
                        <CardContent>
                          {validationData.projectConcept?.features ? (
                            <MermaidDiagram 
                              definition={generateCategorySequenceDiagram(validationData.projectConcept.features)} 
                              className="w-full"
                            />
                          ) : (
                            <p className="text-muted-foreground text-sm">No feature data available to generate diagram.</p>
                          )}
                        </CardContent>
                      </Card>
                    </TabsContent>

                  </Tabs>
                </div>
              </>
            )}
          </div>
        </div>
      </div>
    </SidebarProvider>
  );
}
