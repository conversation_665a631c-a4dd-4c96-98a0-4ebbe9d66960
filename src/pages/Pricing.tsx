
import { useEffect, useState } from "react";
import { useNavigate } from "react-router-dom";
import { Check, ChevronRight, LightbulbIcon, MapIcon, InfoIcon } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { UserSidebar } from "@/components/user/UserSidebar";
import { SidebarProvider } from "@/components/ui/sidebar";
import { useSidebarStore } from "@/store/sidebarStore";
import { useSubscriptionStore } from "@/store/subscriptionStore";
import { supabase } from "@/integrations/supabase/client";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { Info } from "lucide-react";
import { toast } from "sonner";

interface Project {
  id: string;
  name: string;
  description?: string;
}

export default function Pricing() {
  const navigate = useNavigate();
  const { open, setOpen } = useSidebarStore();
  const [selectedIdeaCount, setSelectedIdeaCount] = useState("1");
  const [selectedRoadmapPack, setSelectedRoadmapPack] = useState("poc");
  const [selectedProject, setSelectedProject] = useState<string>("");
  const [projects, setProjects] = useState<Project[]>([]);
  const [loadingProjects, setLoadingProjects] = useState(false);
  
  const { 
    plans, 
    fetchPlans, 
    isLoading, 
    currentSubscription,
    fetchCurrentSubscription,
    isPro,
    freePlanLimits,
    fetchFreePlanLimits,
    clearAccessCache,
    checkRoadmapAccess
  } = useSubscriptionStore();
  
  // Track which roadmap phases the user already has access to
  const [accessStatus, setAccessStatus] = useState({
    poc: false,
    mvp: false,
    production: false,
    checked: false
  });

  // Fetch user's projects for roadmap pack selection
  const fetchUserProjects = async () => {
    try {
      setLoadingProjects(true);
      
      const { data: validations, error } = await supabase
        .from('idea_validations')
        .select('id, originalIdea, projectConcept')
        .order('created_at', { ascending: false });
      
      if (error) {
        throw error;
      }
      
      // Map to Project interface
      const projectsList: Project[] = validations.map(validation => ({
        id: validation.id,
        name: validation.originalIdea.substring(0, 50), // Use originalIdea as the name, limit to 50 chars
        description: typeof validation.projectConcept === 'object' ? 
                    JSON.stringify(validation.projectConcept).substring(0, 100) : 
                    undefined // Use projectConcept as description if it exists
      }));
      
      setProjects(projectsList);
      
      // If we have projects, select the first one by default
      if (projectsList.length > 0 && !selectedProject) {
        setSelectedProject(projectsList[0].id);
      }
    } catch (error) {
      console.error("Error fetching projects:", error);
      toast.error("Failed to load your projects");
    } finally {
      setLoadingProjects(false);
    }
  };

  useEffect(() => {
    fetchPlans();
    fetchCurrentSubscription();
    fetchFreePlanLimits();
    fetchUserProjects();
    checkAccess();
  }, [fetchPlans, fetchCurrentSubscription, fetchFreePlanLimits]);
  
  // Check which roadmap phases the user already has access to
  const checkAccess = async () => {
    try {
      // Clear cache to ensure fresh checks
      clearAccessCache();
      
      // If a project is selected, check access for that specific project
      if (selectedProject) {
        // Check access to each phase for the selected project
        const pocAccess = await checkRoadmapAccess('poc', selectedProject);
        const mvpAccess = await checkRoadmapAccess('mvp', selectedProject);
        const productionAccess = await checkRoadmapAccess('production', selectedProject);
        
        console.log("Roadmap access status for project", selectedProject, ":", {
          poc: pocAccess,
          mvp: mvpAccess,
          production: productionAccess
        });
        
        setAccessStatus({
          poc: pocAccess,
          mvp: mvpAccess,
          production: productionAccess,
          checked: true
        });
      } else {
        // If no project is selected, just check for global access
        const pocAccess = await checkRoadmapAccess('poc');
        const mvpAccess = await checkRoadmapAccess('mvp');
        const productionAccess = await checkRoadmapAccess('production');
        
        setAccessStatus({
          poc: pocAccess,
          mvp: mvpAccess,
          production: productionAccess,
          checked: true
        });
      }
    } catch (error) {
      console.error("Error checking access status:", error);
    }
  };

  // When project selection changes, recheck access
  useEffect(() => {
    if (selectedProject) {
      checkAccess();
    }
  }, [selectedProject]);

  // Update the roadmap pack selection based on what the user already has access to
  useEffect(() => {
    if (accessStatus.checked) {
      if (accessStatus.production) {
        // If user has access to production, they have access to all
        setSelectedRoadmapPack("all");
      } else if (accessStatus.mvp) {
        // If user has access to MVP but not production, set to poc-mvp
        setSelectedRoadmapPack("poc-mvp");
      } else if (accessStatus.poc) {
        // If user has access to POC only, set to poc
        setSelectedRoadmapPack("poc");
      }
    }
  }, [accessStatus]);

  const handleSelectPlan = (planId: string) => {
    if (planId === 'free') {
      toast.info("You already have access to the Free plan");
      return;
    }
    
    if (currentSubscription?.plan_id === planId && planId !== 'roadmap-pack') {
      toast.info("You are already subscribed to this plan");
      return;
    }
    
    // For roadmap pack, check if user already has access to the selected package
    if (planId === 'roadmap-pack') {
      // For roadmap packs, we now require a project selection
      if (!selectedProject) {
        toast.error("Please select a project for the roadmap package");
        return;
      }
      
      // Fix the check for already unlocked roadmap packs
      const hasExistingAccess = isRoadmapPackUnlocked(selectedRoadmapPack);
      
      if (hasExistingAccess) {
        toast.info(`You already have access to ${getRoadmapPackName(selectedRoadmapPack)} for this project`);
        return;
      }
    }
    
    // Handle different plan types
    if (planId === 'multi-idea') {
      navigate(`/checkout/${planId}?count=${selectedIdeaCount}`);
    } else if (planId === 'roadmap-pack') {
      navigate(`/checkout/${planId}?pack=${selectedRoadmapPack}&project=${selectedProject}`);
    } else {
      navigate(`/checkout/${planId}`);
    }
  };

  // Get price based on idea count selection
  const getMultiIdeaPrice = (count: string) => {
    switch (count) {
      case "1": return 5.00;
      case "2": return 9.99;
      case "5": return 19.99;
      case "10": return 45.00;
      default: return 5.00;
    }
  };

  // Get roadmap pack price based on selected pack
  const getRoadmapPackPrice = (pack: string) => {
    switch (pack) {
      case "poc": return 10.00;
      case "poc-mvp": return 17.00;
      case "all": return 20.00;
      default: return 10.00;
    }
  };

  // Get description based on idea count selection
  const getMultiIdeaDescription = (count: string) => {
    return `Validate ${count} additional idea${count === "1" ? "" : "s"} and expand your project portfolio`;
  };

  // Get description based on roadmap pack selection
  const getRoadmapPackDescription = (pack: string) => {
    switch (pack) {
      case "poc": 
        return "Access to Proof of Concept roadmap";
      case "poc-mvp": 
        return "Access to Proof of Concept and MVP roadmaps";
      case "all": 
        return "Access to all roadmaps (POC, MVP, and Production)";
      default: 
        return "Access to roadmap features";
    }
  };

  // Get readable name for roadmap pack
  const getRoadmapPackName = (pack: string) => {
    switch (pack) {
      case "poc": return "the POC roadmap";
      case "poc-mvp": return "the POC and MVP roadmaps";
      case "all": return "all roadmap phases";
      default: return "roadmap features";
    }
  };

  // Get roadmap pack features
  const getRoadmapPackFeatures = (pack: string) => {
    const features = {
      "poc": [
        "Complete POC roadmap with timeline",
        "Technical implementation details",
        "Resource requirements"
      ],
      "poc-mvp": [
        "Complete POC roadmap with timeline",
        "MVP feature prioritization guide",
        "User onboarding flow design",
        "Technical architecture diagram"
      ],
      "all": [
        "Complete POC & MVP roadmaps",
        "Production readiness checklist",
        "Scaling strategy recommendations",
        "Maintenance and monitoring plan",
        "Deployment guides"
      ]
    };
    
    return features[pack as keyof typeof features] || [];
  };

  // Fix: Correctly check if a roadmap pack is already unlocked based on access status
  const isRoadmapPackUnlocked = (pack: string): boolean => {
    if (!accessStatus.checked || !selectedProject) return false;
    
    switch (pack) {
      case "poc":
        // User has POC access if they can access the POC phase
        return accessStatus.poc;
      case "poc-mvp":
        // User has POC-MVP pack if they can access both POC and MVP phases
        return accessStatus.poc && accessStatus.mvp;
      case "all":
        // User has All pack if they can access POC, MVP, and Production phases
        return accessStatus.poc && accessStatus.mvp && accessStatus.production;
      default:
        return false;
    }
  };

  return (
    <SidebarProvider open={open} setOpen={setOpen}>
      <div className="min-h-screen flex bg-gradient-to-b from-background via-background/95 to-background">
        <UserSidebar open={open} setOpen={setOpen} />
        <div className="flex-1 p-6 md:p-8">
          <div className="max-w-5xl mx-auto">
            <div className="text-center mb-10">
              <h1 className="text-3xl font-extrabold tracking-tight lg:text-5xl mb-4">
                Plans & Pricing
              </h1>
              <p className="text-muted-foreground text-lg max-w-2xl mx-auto">
                Choose the best package for your project needs
              </p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-8 mb-16">
              {/* Idea Validation Packs Card */}
              <Card className="border hover:shadow-md transition-all duration-200">
                <CardHeader className="pb-4">
                  <div className="flex items-center gap-2 mb-1">
                    <LightbulbIcon className="h-5 w-5 text-amber-500" />
                    <CardTitle className="text-lg">
                      Idea Validation Packs
                      {currentSubscription?.plan_id === 'multi-idea' && (
                        <span className="ml-2 text-xs px-1.5 py-0.5 bg-blue-100 text-blue-800 rounded-full font-normal">
                          Unlocked
                        </span>
                      )}
                    </CardTitle>
                  </div>
                  <CardDescription className="text-sm">
                    {getMultiIdeaDescription(selectedIdeaCount)}
                  </CardDescription>
                </CardHeader>
                <CardContent className="pb-4">
                  <div className="flex flex-col space-y-4">
                    <div>
                      <label htmlFor="idea-count" className="block text-sm font-medium text-muted-foreground mb-1">
                        Select number of ideas:
                      </label>
                      <Select 
                        value={selectedIdeaCount} 
                        onValueChange={setSelectedIdeaCount}
                      >
                        <SelectTrigger id="idea-count" className="w-full">
                          <SelectValue placeholder="Select idea count" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="1">1 Idea</SelectItem>
                          <SelectItem value="2">2 Ideas</SelectItem>
                          <SelectItem value="5">5 Ideas Pack</SelectItem>
                          <SelectItem value="10">10 Ideas Pack</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                    
                    <div className="flex items-center mb-2">
                      <span className="text-lg font-bold">${getMultiIdeaPrice(selectedIdeaCount).toFixed(2)}</span>
                      <span className="text-xs text-muted-foreground ml-1">one-time purchase</span>
                    </div>
                  </div>
                </CardContent>
                <CardFooter>
                  <Button 
                    className="w-full" 
                    onClick={() => handleSelectPlan('multi-idea')}
                    variant="default"
                    disabled={currentSubscription?.plan_id === 'multi-idea'}
                  >
                    {currentSubscription?.plan_id === 'multi-idea'
                      ? 'Already Unlocked'
                      : 'Checkout'}
                  </Button>
                </CardFooter>
              </Card>

              {/* Roadmap Packs Card */}
              <Card className="border hover:shadow-md transition-all duration-200">
                <CardHeader className="pb-4">
                  <div className="flex items-center gap-2 mb-1">
                    <MapIcon className="h-5 w-5 text-indigo-500" />
                    <CardTitle className="text-lg">
                      Roadmap Packs
                      {selectedProject && isRoadmapPackUnlocked(selectedRoadmapPack) && (
                        <span className="ml-2 text-xs px-1.5 py-0.5 bg-blue-100 text-blue-800 rounded-full font-normal">
                          Unlocked
                        </span>
                      )}
                    </CardTitle>
                  </div>
                  <CardDescription className="text-sm">
                    {getRoadmapPackDescription(selectedRoadmapPack)}
                  </CardDescription>
                </CardHeader>
                <CardContent className="pb-4">
                  <div className="flex flex-col space-y-4">
                    {/* Project Selection - New addition */}
                    <div>
                      <label htmlFor="project-selection" className="block text-sm font-medium text-muted-foreground mb-1">
                        Select project:
                      </label>
                      <Select 
                        value={selectedProject} 
                        onValueChange={setSelectedProject}
                        disabled={loadingProjects}
                      >
                        <SelectTrigger id="project-selection" className="w-full">
                          <SelectValue placeholder={loadingProjects ? "Loading projects..." : "Select a project"} />
                        </SelectTrigger>
                        <SelectContent>
                          {projects.length > 0 ? (
                            projects.map(project => (
                              <SelectItem key={project.id} value={project.id}>
                                {project.name}
                              </SelectItem>
                            ))
                          ) : (
                            <SelectItem value="no-projects" disabled>
                              No projects found
                            </SelectItem>
                          )}
                        </SelectContent>
                      </Select>
                      
                      {!selectedProject && (
                        <p className="text-xs text-amber-600 mt-1">
                          Please select a project to continue
                        </p>
                      )}
                    </div>

                    <div>
                      <label htmlFor="roadmap-pack" className="block text-sm font-medium text-muted-foreground mb-1">
                        Select roadmap package:
                      </label>
                      <Select 
                        value={selectedRoadmapPack} 
                        onValueChange={setSelectedRoadmapPack}
                        disabled={!selectedProject}
                      >
                        <SelectTrigger id="roadmap-pack" className="w-full">
                          <SelectValue placeholder="Select roadmap package" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="poc">
                            POC Only (${getRoadmapPackPrice("poc").toFixed(2)})
                            {selectedProject && accessStatus.poc && !accessStatus.mvp && !accessStatus.production && " ✓"}
                          </SelectItem>
                          <SelectItem value="poc-mvp">
                            POC & MVP (${getRoadmapPackPrice("poc-mvp").toFixed(2)})
                            {selectedProject && accessStatus.poc && accessStatus.mvp && !accessStatus.production && " ✓"}
                          </SelectItem>
                          <SelectItem value="all">
                            All Roadmaps (${getRoadmapPackPrice("all").toFixed(2)})
                            {selectedProject && accessStatus.poc && accessStatus.mvp && accessStatus.production && " ✓"}
                          </SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                    
                    {selectedProject && (
                      <Alert className="bg-blue-50 border-blue-200">
                        <Info className="h-4 w-4 text-blue-600" />
                        <AlertTitle className="text-blue-700">Project-Specific Access</AlertTitle>
                        <AlertDescription className="text-blue-600">
                          This purchase will unlock the selected roadmap(s) for "{projects.find(p => p.id === selectedProject)?.name}" only.
                        </AlertDescription>
                      </Alert>
                    )}
                    
                    <div className="flex items-center mb-2">
                      <span className="text-lg font-bold">${getRoadmapPackPrice(selectedRoadmapPack).toFixed(2)}</span>
                      <span className="text-xs text-muted-foreground ml-1">one-time purchase</span>
                    </div>
                    
                    {getRoadmapPackFeatures(selectedRoadmapPack).map((feature, index) => (
                      <div key={index} className="flex items-start">
                        <Check className="text-green-500 mr-2 h-4 w-4 flex-shrink-0 mt-0.5" />
                        <span className="text-sm">{feature}</span>
                      </div>
                    ))}
                  </div>
                </CardContent>
                <CardFooter>
                  <Button 
                    className="w-full" 
                    onClick={() => handleSelectPlan('roadmap-pack')}
                    variant="default"
                    disabled={!selectedProject || (selectedProject && isRoadmapPackUnlocked(selectedRoadmapPack))}
                  >
                    {!selectedProject
                      ? 'Select a Project'
                      : isRoadmapPackUnlocked(selectedRoadmapPack)
                        ? 'Already Unlocked'
                        : 'Checkout'}
                  </Button>
                </CardFooter>
              </Card>
            </div>

            <div className="mt-12 text-center">
              <h2 className="text-xl font-bold mb-4">Need custom development support?</h2>
              <p className="text-muted-foreground mb-6 max-w-xl mx-auto">
                Contact us for enterprise pricing, custom features, or if you have specific development needs for your project.
              </p>
              <Button variant="outline" size="lg">
                Contact Sales
              </Button>
            </div>
          </div>
        </div>
      </div>
    </SidebarProvider>
  );
}
