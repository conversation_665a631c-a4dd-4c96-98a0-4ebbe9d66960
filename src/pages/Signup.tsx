
import { useEffect } from "react";
import { useNavigate } from "react-router-dom";
import { <PERSON><PERSON><PERSON>, ArrowLeft } from "lucide-react";
import { supabase } from "@/integrations/supabase/client";
import { SignupForm } from "@/components/auth/SignupForm";
import { Button } from "@/components/ui/button";

export default function Signup() {
  const navigate = useNavigate();

  useEffect(() => {
    const checkAuth = async () => {
      const { data: { session } } = await supabase.auth.getSession();
      if (session) {
        navigate("/dashboard");
      }
    };
    checkAuth();
  }, [navigate]);

  return (
    <div className="min-h-screen flex items-center bg-gradient-to-br from-white via-indigo-50/30 to-white dark:from-gray-900 dark:via-indigo-900/20 dark:to-gray-900 overflow-hidden relative">
      {/* Grid pattern overlay */}
      <div className="absolute inset-0 bg-grid-pattern opacity-[0.02] dark:opacity-[0.05] pointer-events-none" />
      
      {/* Gradient orbs */}
      <div className="absolute -top-40 -left-40 w-80 h-80 bg-primary/30 rounded-full filter blur-3xl opacity-50 animate-pulse" />
      <div className="absolute -bottom-40 -right-40 w-80 h-80 bg-primary/30 rounded-full filter blur-3xl opacity-50 animate-pulse delay-1000" />
      
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-20 lg:py-32 w-full relative z-10">
        <Button
          variant="ghost"
          className="absolute top-4 left-4 flex items-center gap-2 text-primary hover:text-primary-dark transition-colors"
          onClick={() => navigate('/')}
        >
          <ArrowLeft className="h-4 w-4" />
          Back to Home
        </Button>
        <div className="grid lg:grid-cols-2 gap-12 items-center">
          <div className="relative z-10">
            <div className="mb-8">
              <img 
                src="/Incepta.png" 
                alt="Incepta Logo" 
                className="h-16 w-auto mb-6"
              />
            </div>
            <div className="inline-flex items-center px-4 py-2 bg-primary/10 rounded-full mb-8 animate-fade-in backdrop-blur-sm">
              <Sparkles className="h-5 w-5 text-primary mr-2" />
              <span className="text-primary font-medium">Join Incepta</span>
            </div>
            <h1 className="text-5xl lg:text-6xl font-bold tracking-tight mb-6">
              <span className="text-foreground">Create your</span>{' '}
              <span className="bg-clip-text text-transparent bg-gradient-to-r from-primary to-primary-dark">
                account
              </span>
            </h1>
            
            <p className="text-xl text-foreground/80 leading-relaxed max-w-2xl">
              Join our community of innovators and start bringing your ideas to life. Get started with Incepta today.
            </p>
          </div>
          <div className="relative">
            <div className="absolute inset-0 bg-background/80 dark:bg-background/40 rounded-2xl blur-xl opacity-50" />
            <div className="relative bg-background/80 dark:bg-background/60 p-8 rounded-2xl shadow-xl animate-fade-in backdrop-blur-xl border border-primary/10">
              <SignupForm />
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
