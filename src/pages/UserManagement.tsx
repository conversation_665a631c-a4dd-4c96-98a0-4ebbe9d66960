import { useQuery } from "@tanstack/react-query";
import { supabase } from "@/integrations/supabase/client";
import { Skeleton } from "@/components/ui/skeleton";
import AdminSidebar from "@/components/admin/AdminSidebar";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { useToast } from "@/components/ui/use-toast";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { AlertCircle } from "lucide-react";
import { useSidebarStore } from "@/store/sidebarStore";
import { SidebarProvider } from "@/components/ui/sidebar";

interface Profile {
  id: string;
  firstName: string | null;
  lastName: string | null;
  created_at: string;
  updated_at: string;
  avatarUrl: string | null;
}

interface UserRole {
  id: string;
  user_id: string;
  created_at: string;
}

export default function UserManagement() {
  const { toast } = useToast();
  const { open, setOpen } = useSidebarStore();

  const { data: profiles, isLoading, error } = useQuery({
    queryKey: ['admin-users'],
    queryFn: async () => {
      // First, get all profiles
      const { data: profilesData, error: profilesError } = await supabase
        .from('profiles')
        .select('*');
      
      if (profilesError) {
        toast({
          variant: "destructive",
          title: "Error",
          description: "Failed to load users. Please check your admin permissions.",
        });
        throw profilesError;
      }

      // Then, get all user roles
      const { data: rolesData, error: rolesError } = await supabase
        .from('user_roles')
        .select('*');

      if (rolesError) {
        toast({
          variant: "destructive",
          title: "Error",
          description: "Failed to load user roles. Please check your admin permissions.",
        });
        throw rolesError;
      }

      // Combine the data
      const profilesWithRoles = profilesData.map((profile: Profile) => ({
        ...profile,
        user_roles: rolesData.filter((role: UserRole) => role.user_id === profile.id)
      }));

      return profilesWithRoles as (Profile & { user_roles: UserRole[] })[];
    },
  });

  return (
    <SidebarProvider open={open} setOpen={setOpen}>
      <div className="min-h-screen flex bg-gradient-to-b from-background via-background/95 to-background">
        <AdminSidebar open={open} setOpen={setOpen} />
        <div className="flex-1 p-8">
          <div className="max-w-6xl mx-auto">
            <div className="mb-8">
              <h1 className="text-3xl font-bold text-primary">User Management</h1>
              <p className="text-muted-foreground mt-2">Manage user roles and permissions</p>
            </div>

            <div className="relative z-10">
              {isLoading ? (
                
                <div className="space-y-4">
                  {[...Array(3)].map((_, i) => (
                    <div key={i} className="w-full h-16">
                      <Skeleton className="h-full w-full" />
                    </div>
                  ))}
                </div>
              ) : error ? (
                
                <Alert variant="destructive" className="mb-4">
                  <AlertCircle className="h-4 w-4" />
                  <AlertTitle>Access Denied</AlertTitle>
                  <AlertDescription>
                    You do not have permission to view this page. Please contact an administrator.
                  </AlertDescription>
                </Alert>
              ) : profiles?.length === 0 ? (
                <div className="text-center py-8 text-muted-foreground">
                  No users found.
                </div>
              ) : (
                <div className="bg-card/60 backdrop-blur-sm rounded-xl border border-primary/10 shadow-lg">
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead className="text-primary">Name</TableHead>
                        <TableHead className="text-primary">Is Admin</TableHead>
                        <TableHead className="text-primary">Created At</TableHead>
                        <TableHead className="text-primary">Last Updated</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {profiles?.map((profile) => (
                        <TableRow key={profile.id}>
                          <TableCell className="font-medium">
                            {profile.firstName} {profile.lastName}
                          </TableCell>
                          <TableCell>
                            <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                              profile.user_roles.length > 0 
                                ? "bg-green-100 text-green-800" 
                                : "bg-gray-100 text-gray-800"
                            }`}>
                              {profile.user_roles.length > 0 ? "Yes" : "No"}
                            </span>
                          </TableCell>
                          <TableCell>{new Date(profile.created_at).toLocaleDateString()}</TableCell>
                          <TableCell>{new Date(profile.updated_at).toLocaleDateString()}</TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                </div>
              )}
            </div>
          </div>
        </div>
      </div>
    </SidebarProvider>
  );
}
