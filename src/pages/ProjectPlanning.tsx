
import { useParams, useNavigate } from "react-router-dom";
import { useQuery } from "@tanstack/react-query";
import { SidebarProvider } from "@/components/ui/sidebar";
import { useSidebarStore } from "@/store/sidebarStore";
import { UserSidebar } from "@/components/user/UserSidebar";
import PlanningWizard from "@/components/planning/PlanningWizard";
import { Button } from "@/components/ui/button";
import { ChevronLeft, Loader2 } from "lucide-react";
import { supabase } from "@/integrations/supabase/client";

export default function ProjectPlanning() {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const { open, setOpen } = useSidebarStore();

  const { data: validation, isLoading: validationLoading } = useQuery({
    queryKey: ['validation', id],
    queryFn: async () => {
      if (!id) return null;
      
      const { data, error } = await supabase
        .from('idea_validations')
        .select('*')
        .eq('id', id)
        .single();
      
      if (error) {
        console.error("Error loading validation:", error);
        return null;
      }
      return data;
    },
    enabled: !!id,
    // Disable caching for this query to always get fresh data
    staleTime: 0,
    gcTime: 0
  });

  if (!id) {
    console.log("No validation ID found, redirecting to dashboard");
    navigate('/dashboard');
    return null;
  }

  console.log("ProjectPlanning page loaded with ID:", id);

  if (validationLoading) {
    return (
      <SidebarProvider open={open} setOpen={setOpen}>
        <div className="min-h-screen flex bg-gradient-to-b from-background via-background/95 to-background">
          <UserSidebar open={open} setOpen={setOpen} />
          <div className="flex-1 p-6 md:p-8 flex items-center justify-center">
            <div className="text-center">
              <Loader2 className="mx-auto h-8 w-8 animate-spin text-primary" />
              <p className="mt-4 text-muted-foreground">Loading project data...</p>
            </div>
          </div>
        </div>
      </SidebarProvider>
    );
  }

  return (
    <SidebarProvider open={open} setOpen={setOpen}>
      <div className="min-h-screen flex bg-gradient-to-b from-background via-background/95 to-background">
        <UserSidebar open={open} setOpen={setOpen} />
        <div className="flex-1 p-6 md:p-8">
          <div className="max-w-7xl mx-auto">
            <Button 
              variant="ghost" 
              className="mb-6 px-0 text-muted-foreground hover:text-foreground group transition-all duration-200"
              onClick={() => navigate(`/validate-idea/results/${id}`)}
            >
              <ChevronLeft className="mr-2 h-4 w-4 group-hover:-translate-x-0.5 transition-transform" />
              <span className="group-hover:underline">Back to Validation Results</span>
            </Button>
            
            <PlanningWizard validationId={id} />
          </div>
        </div>
      </div>
    </SidebarProvider>
  );
}
