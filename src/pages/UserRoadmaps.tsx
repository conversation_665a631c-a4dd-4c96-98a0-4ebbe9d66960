
import { useNavigate } from "react-router-dom";
import { useQuery } from "@tanstack/react-query";
import { supabase } from "@/integrations/supabase/client";
import { UserSidebar } from "@/components/user/UserSidebar";
import { SidebarProvider } from "@/components/ui/sidebar";
import { useSidebarStore } from "@/store/sidebarStore";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Loader2, FileText } from "lucide-react";

interface UserRoadmap {
  id: string;
  phase: string;
  status: 'pending' | 'published';
  created_at: string;
  validation_id: string;
  idea_validations: {
    id: string;
    originalIdea: string;
    projectConcept: any;
  };
}

export default function UserRoadmaps() {
  const navigate = useNavigate();
  const { open, setOpen } = useSidebarStore();

  // Fetch user's roadmaps
  const { data: roadmaps, isLoading } = useQuery({
    queryKey: ['user-roadmaps'],
    queryFn: async () => {
      const { data: session } = await supabase.auth.getSession();
      if (!session.session) return [];
      
      const { data, error } = await supabase
        .from('roadmaps')
        .select(`
          id, 
          phase, 
          status, 
          created_at,
          validation_id,
          idea_validations (
            id,
            originalIdea,
            projectConcept
          )
        `)
        .eq('user_id', session.session.user.id)
        .order('created_at', { ascending: false });
      
      if (error) throw error;
      
      // Cast the data to the correct type with as unknown as intermediate step
      return (data as unknown) as UserRoadmap[];
    }
  });

  return (
    <SidebarProvider open={open} setOpen={setOpen}>
      <div className="min-h-screen flex bg-gradient-to-b from-background via-background/95 to-background">
        <UserSidebar open={open} setOpen={setOpen} />
        <div className="flex-1 p-6 md:p-8">
          <div className="max-w-7xl mx-auto">
            <h1 className="text-3xl font-bold mb-2">My Roadmaps</h1>
            <p className="text-muted-foreground mb-8">
              View all your project roadmaps across different development phases
            </p>
            
            {isLoading ? (
              <div className="flex items-center justify-center py-16">
                <Loader2 className="h-8 w-8 animate-spin text-primary" />
              </div>
            ) : roadmaps && roadmaps.length > 0 ? (
              <div className="grid gap-4">
                {roadmaps.map((roadmap) => (
                  <Card key={roadmap.id} className="overflow-hidden">
                    <CardContent className="p-0">
                      <div className="grid grid-cols-1 md:grid-cols-3 divide-y md:divide-y-0 md:divide-x">
                        <div className="p-4 md:p-6">
                          <p className="text-sm text-muted-foreground mb-1">Project Idea</p>
                          <p className="font-medium truncate">
                            {roadmap.idea_validations?.originalIdea || "Untitled Project"}
                          </p>
                          
                          <div className="flex flex-wrap gap-2 mt-4">
                            <Badge variant={roadmap.status === 'pending' ? "outline" : "default"}>
                              {roadmap.status === 'pending' ? "In Review" : "Published"}
                            </Badge>
                            <Badge variant="secondary">{roadmap.phase}</Badge>
                          </div>
                        </div>
                        
                        <div className="p-4 md:p-6">
                          <p className="text-sm text-muted-foreground mb-1">Date Requested</p>
                          <p className="font-medium">
                            {new Date(roadmap.created_at).toLocaleString()}
                          </p>
                          
                          <p className="text-sm text-muted-foreground mt-3 mb-1">Status</p>
                          <p className="font-medium">
                            {roadmap.status === 'pending' ? 
                              'Waiting for admin to generate roadmap' : 
                              'Ready for viewing'}
                          </p>
                        </div>
                        
                        <div className="p-4 md:p-6 flex items-center">
                          <Button 
                            variant="default" 
                            className="w-full"
                            onClick={() => navigate(`/validate-idea/results/${roadmap.validation_id}/roadmap`)}
                          >
                            <FileText className="mr-2 h-4 w-4" />
                            View Roadmap
                          </Button>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>
            ) : (
              <Card className="border border-dashed">
                <CardContent className="py-10 text-center">
                  <div className="flex flex-col items-center max-w-md mx-auto">
                    <div className="p-3 rounded-full bg-primary/10 mb-4">
                      <FileText className="h-6 w-6 text-primary" />
                    </div>
                    <h3 className="text-xl font-medium mb-2">No roadmaps yet</h3>
                    <p className="text-muted-foreground mb-6">
                      Start by validating an idea and then request a roadmap for your project.
                    </p>
                    <Button 
                      onClick={() => navigate('/validate-idea')}
                    >
                      Validate New Idea
                    </Button>
                  </div>
                </CardContent>
              </Card>
            )}
          </div>
        </div>
      </div>
    </SidebarProvider>
  );
}
