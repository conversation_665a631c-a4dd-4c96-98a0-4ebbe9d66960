
import { useState, useEffect } from "react";
import { useNavigate } from "react-router-dom";
import { useQuery } from "@tanstack/react-query";
import { ArrowLeft, ArrowRight, Loader2, BarChart3, PackageOpen, Users } from "lucide-react";
import { supabase } from "@/integrations/supabase/client";
import { Json } from "@/integrations/supabase/types";
import { Button } from "@/components/ui/button";
import { Skeleton } from "@/components/ui/skeleton";
import { Textarea } from "@/components/ui/textarea";
import { Card, CardHeader, CardTitle, CardDescription, CardContent } from "@/components/ui/card";
import { useToast } from "@/components/ui/use-toast";
import { UserSidebar } from "@/components/user/UserSidebar";
import { SidebarProvider } from "@/components/ui/sidebar";
import { useSidebarStore } from "@/store/sidebarStore";
import { Progress } from "@/components/ui/progress";
import { Badge } from "@/components/ui/badge";
import { AlertBanner } from "@/components/ui/AlertBanner";
import { useSubscriptionStore } from "@/store/subscriptionStore";
import { useAuthStore } from "@/store/authStore";

interface ProjectConcept {
  description?: string;
  overview?: string;
  keyFeatures?: string[];
  goals?: string[];
  userRoles?: any[];
  features?: {
    core: any[];
    mustHave: any[];
    shouldHave: any[];
    niceToHave: any[];
    future: any[];
  };
}

interface IdeaValidation {
  id: string;
  original_idea: string;
  title: string | null;
  status: 'pending' | 'analyzing' | 'completed' | 'error';
  created_at: string;
  project_concept: ProjectConcept | null;
  market_validation: Json | null;
  target_users: Json | null;
  user_roles: Json | null;
  status_detail: string | null;
}

export default function Dashboard() {
  const { open, setOpen } = useSidebarStore();
  const navigate = useNavigate();
  const { toast } = useToast();
  const { isAuthenticated, userId } = useAuthStore();
  const { isPro, freePlanLimits, fetchFreePlanLimits } = useSubscriptionStore();
  const [ideaInput, setIdeaInput] = useState("");
  const [isAnalyzing, setIsAnalyzing] = useState(false);
  const [analysisStage, setAnalysisStage] = useState(0);
  const [inputError, setInputError] = useState({ show: false, message: "" });
  const [completedValidationsCount, setCompletedValidationsCount] = useState(0);
  const [isCheckingLimits, setIsCheckingLimits] = useState(true);

  const validationsQueryKey = ['validations', userId];

  useEffect(() => {
    fetchFreePlanLimits();
    
    if (isAuthenticated && userId) {
      checkCompletedValidations();
    }
  }, [fetchFreePlanLimits, isAuthenticated, userId]);

  const checkCompletedValidations = async () => {
    if (!isAuthenticated || !userId) return;
    
    setIsCheckingLimits(true);
    try {
      const { count, error } = await supabase
        .from('idea_validations')
        .select('*', { count: 'exact' })
        .eq('status', 'completed');
      
      if (error) {
        console.error('Error checking validation count:', error);
        throw error;
      }
      
      console.log('User has completed', count, 'validations');
      setCompletedValidationsCount(count || 0);
    } catch (err) {
      console.error('Failed to check validation limits:', err);
    } finally {
      setIsCheckingLimits(false);
    }
  };

  const { data: validations, isLoading, refetch } = useQuery({
    queryKey: validationsQueryKey,
    queryFn: async () => {
      console.log("Fetching validations data for user:", userId);
      
      if (!isAuthenticated || !userId) {
        console.log("User not authenticated, returning empty array");
        return [];
      }
      
      const { data, error } = await supabase
        .from('idea_validations')
        .select('*')
        .order('created_at', { ascending: false });
      
      if (error) {
        console.error("Error fetching validations:", error);
        throw error;
      }
      
      console.log("Fetched validations:", data);
      
      const transformedData = data.map(item => ({
        id: item.id,
        original_idea: item.originalIdea,
        title: item.title,
        status: item.status,
        created_at: item.created_at,
        project_concept: item.projectConcept,
        market_validation: item.marketValidation,
        target_users: item.targetUsers,
        user_roles: item.userRoles,
        status_detail: item.statusDetail
      }));
      
      return transformedData as IdeaValidation[];
    },
    enabled: isAuthenticated && !!userId
  });

  useEffect(() => {
    if (isAuthenticated && userId) {
      refetch();
    }
  }, [isAuthenticated, userId, refetch]);

  useEffect(() => {
    if (validations && isAuthenticated) {
      checkCompletedValidations();
    }
  }, [validations, isAuthenticated]);

  const renderValidationStatus = (status: IdeaValidation['status']) => {
    const statusStyles = {
      pending: "bg-amber-100 text-amber-800",
      analyzing: "bg-blue-100 text-blue-800",
      completed: "bg-green-100 text-green-800",
      error: "bg-red-100 text-red-800",
    };

    return (
      <Badge className={`px-2 py-1 ${statusStyles[status]}`}>
        {status.toUpperCase()}
      </Badge>
    );
  };

  const renderProgressIndicator = (status: IdeaValidation['status']) => {
    if (status === 'pending') return 10;
    if (status === 'analyzing') return 50;
    if (status === 'completed') return 100;
    return 0;
  };

  const analysisStages = [
    "Step 1/3: Analyzing your idea and generating project concept...",
    "Step 2/3: Identifying target users and creating user personas...",
    "Step 3/3: Performing market validation and competitor analysis..."
  ];

  const simulateAnalysisProgress = () => {
    let currentStage = 0;
    
    const interval = setInterval(() => {
      currentStage++;
      
      if (currentStage <= 2) {
        setAnalysisStage(currentStage);
      } else {
        clearInterval(interval);
      }
    }, 8000);
    
    return () => clearInterval(interval);
  };

  const handleCardClick = (validation: IdeaValidation) => {
    if (validation.status === 'completed') {
      navigate(`/validate-idea/results/${validation.id}`, { 
        state: { 
          validationId: validation.id,
          validation: validation 
        } 
      });
    } else if (validation.status === 'error') {
      toast({
        variant: "destructive",
        title: "Processing Error",
        description: "There was an error processing this idea. Please try again with a new submission."
      });
    } else {
      toast({
        title: "Processing in Progress",
        description: "This idea is still being analyzed. View the progress on the validation page."
      });
      navigate(`/validate-idea`);
    }
  };

  const handleSubmitIdea = async () => {
    if (ideaInput.trim().length < 50) {
      setInputError({
        show: true,
        message: "Please provide at least 50 characters to properly analyze your idea."
      });
      return;
    }

    setInputError({ show: false, message: "" });

    if (!isPro && (completedValidationsCount >= (freePlanLimits?.idea_validations || 0))) {
      console.log('User has reached validation limit', {
        completedCount: completedValidationsCount,
        freePlanLimit: freePlanLimits?.idea_validations
      });
      
      toast({
        title: "Validation limit reached",
        description: "Please upgrade to our Pro plan to validate more ideas.",
      });
      
      navigate('/pricing');
      return;
    }

    navigate('/validate-idea');
  };

  const hasNoIdeas = !isLoading && (!validations || validations.length === 0);

  const handleNewValidation = async () => {
    console.log('handleNewValidation called with:', {
      isPro,
      completedValidationsCount,
      limit: freePlanLimits?.idea_validations
    });
    
    if (isPro) {
      console.log('Pro user can validate unlimited ideas');
      navigate('/validate-idea');
      return;
    }

    if (!freePlanLimits) {
      await fetchFreePlanLimits();
    }
    
    await checkCompletedValidations();
    
    console.log('After refresh check:', {
      completedCount: completedValidationsCount,
      limit: freePlanLimits?.idea_validations
    });
    
    if (freePlanLimits && completedValidationsCount >= freePlanLimits.idea_validations) {
      console.log('Free user has reached validation limit, redirecting to pricing');
      toast({
        title: "Validation limit reached",
        description: "Please upgrade to our Pro plan to validate more ideas.",
      });
      navigate('/pricing');
      return;
    }

    console.log('Free user still has validations available');
    navigate('/validate-idea');
  };

  return (
    <SidebarProvider open={open} setOpen={setOpen}>
      <AlertBanner 
        variant="error"
        message={inputError.message}
        show={inputError.show}
        onClose={() => setInputError({ ...inputError, show: false })}
      />
      
      <div className="min-h-screen flex bg-gradient-to-b from-background via-background/95 to-background">
        <UserSidebar open={open} setOpen={setOpen} />
        
        {isLoading ? (
          <div className="flex-1 p-8 flex items-center justify-center">
            <Loader2 className="h-8 w-8 animate-spin text-primary" />
          </div>
        ) : hasNoIdeas ? (
          <div className="flex-1 p-8 flex flex-col items-center">
            <div className="w-full max-w-5xl">
            </div>
            
            <div className="flex flex-col items-center justify-center max-w-3xl mx-auto text-center mt-8 mb-12">
              <div className="h-20 w-20 rounded-full bg-primary/10 flex items-center justify-center mb-8">
                <img src="/Incepta.png" alt="Incepta Logo" className="h-12 w-12" />
              </div>
              
              <h1 className="text-4xl font-bold text-primary mb-6">
                From Idea to Implementation
              </h1>
              
              <p className="text-lg text-gray-700 dark:text-gray-300 mb-12">
                Incepta validates your idea across 10 key metrics, delivering comprehensive
                roadmaps for interactive prototype development.
              </p>
              
              <Textarea 
                placeholder="AI Legal Assistant - automated contract review and risk analysis for small businesses"
                className="min-h-[150px] mb-4 w-full"
                value={ideaInput}
                onChange={(e) => setIdeaInput(e.target.value)}
              />
              
              <div className="flex items-center justify-between w-full">
                <div className="text-sm text-gray-500">
                  {isAnalyzing ? analysisStages[analysisStage] : "🚀 Submit your idea to get started with validation"}
                </div>
                
                <Button 
                  className="bg-primary/80 hover:bg-primary"
                  onClick={handleSubmitIdea}
                  disabled={isAnalyzing}
                >
                  {isAnalyzing ? (
                    <>
                      <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                      Analyzing
                    </>
                  ) : (
                    "Validate Idea"
                  )}
                </Button>
              </div>
            </div>
          </div>
        ) : (
          <div className="flex-1 p-8">
            <div className="max-w-5xl mx-auto">
              <div className="flex justify-between items-center mb-8">
                <h1 className="text-3xl font-bold">My Validated Ideas</h1>
                <Button 
                  onClick={handleNewValidation}
                  disabled={isCheckingLimits}
                >
                  {isCheckingLimits ? (
                    <>
                      <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                      Checking...
                    </>
                  ) : (
                    <>
                      <span className="mr-2">+</span>
                      Validate New Idea
                    </>
                  )}
                </Button>
              </div>

              <div className="relative z-10">
                {isLoading ? (
                  <div className="space-y-4">
                    {[...Array(3)].map((_, i) => (
                      <div key={i} className="p-6 bg-white/60 dark:bg-gray-800/60 backdrop-blur-sm rounded-xl border border-gray-100 dark:border-gray-700">
                        <Skeleton className="h-6 w-1/4 mb-4" />
                        <Skeleton className="h-4 w-3/4" />
                      </div>
                    ))}
                  </div>
                ) : (
                  <div className="grid gap-6">
                    {validations?.map((validation) => (
                      <Card 
                        key={validation.id} 
                        className={`border ${
                          validation.status === 'completed' 
                            ? 'border-primary/10 hover:border-primary/30 hover:shadow-lg hover:shadow-primary/5 cursor-pointer' 
                            : 'border-gray-100 dark:border-gray-700'
                        } transition-all duration-200 cursor-pointer`}
                        onClick={() => handleCardClick(validation)}
                      >
                        <CardContent className="p-6">
                          <div className="flex justify-between items-start mb-4">
                            <div className="space-y-2 flex-1">
                              <div className="flex items-center justify-between mb-2">
                                <h3 className="font-medium">{new Date(validation.created_at).toLocaleDateString()}</h3>
                                {renderValidationStatus(validation.status)}
                              </div>
                              
                              <h4 className="font-semibold text-lg line-clamp-1">
                                {validation.title || validation.original_idea.substring(0, 60)}
                              </h4>
                              
                              <p className="text-sm text-muted-foreground line-clamp-2">
                                {validation.original_idea}
                              </p>
                              
                              {validation.status === 'completed' && validation.project_concept?.overview && (
                                <p className="text-sm line-clamp-1">
                                  {validation.project_concept.overview.substring(0, 100)}...
                                </p>
                              )}
                              
                              {validation.status_detail && validation.status !== 'completed' && (
                                <p className="text-xs text-muted-foreground">
                                  {validation.status_detail}
                                </p>
                              )}
                            </div>
                          </div>
                          
                          {(validation.status === 'pending' || validation.status === 'analyzing') && (
                            <div className="space-y-2">
                              <Progress value={renderProgressIndicator(validation.status)} className="h-1.5" />
                              <div className="flex justify-between">
                                <p className="text-xs text-muted-foreground">
                                  {validation.status === 'pending' ? 'Waiting to start analysis...' : 'Analyzing your idea...'}
                                </p>
                                <p className="text-xs text-primary">
                                  Click to view progress
                                </p>
                              </div>
                            </div>
                          )}
                          
                          {validation.status === 'completed' && (
                            <div className="flex items-center justify-between mt-4">
                              <div className="flex gap-6">
                                <div className="flex items-center text-xs text-muted-foreground">
                                  <Users className="h-4 w-4 mr-1" />
                                  <span>{validation.user_roles ? (Array.isArray(validation.user_roles) ? validation.user_roles.length : 0) : 0} Roles</span>
                                </div>
                                <div className="flex items-center text-xs text-muted-foreground">
                                  <PackageOpen className="h-4 w-4 mr-1" />
                                  <span>{validation.project_concept?.features?.core?.length || 0} Core Features</span>
                                </div>
                                <div className="flex items-center text-xs text-muted-foreground">
                                  <BarChart3 className="h-4 w-4 mr-1" />
                                  <span>Market Analysis</span>
                                </div>
                              </div>
                              <Button variant="ghost" size="sm">
                                View Results <ArrowRight className="ml-2 h-4 w-4" />
                              </Button>
                            </div>
                          )}
                          
                          {validation.status === 'error' && (
                            <p className="text-sm text-red-500 mt-2">
                              There was an error processing this idea. Please try again.
                            </p>
                          )}
                        </CardContent>
                      </Card>
                    ))}
                  </div>
                )}
              </div>
            </div>
          </div>
        )}
      </div>
    </SidebarProvider>
  );
}
