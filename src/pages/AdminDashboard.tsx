
import { useQuery } from "@tanstack/react-query";
import { useNavigate } from "react-router-dom";
import { supabase } from "@/integrations/supabase/client";
import { Skeleton } from "@/components/ui/skeleton";
import AdminSidebar from "@/components/admin/AdminSidebar";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { useToast } from "@/components/ui/use-toast";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { AlertCircle, ExternalLink } from "lucide-react";
import { useSidebarStore } from "@/store/sidebarStore";
import { SidebarProvider } from "@/components/ui/sidebar";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";

interface IdeaValidation {
  id: string;
  originalIdea: string;
  status: 'pending' | 'analyzing' | 'completed' | 'error';
  created_at: string;
  updated_at: string;
  userId: string | null;
  projectConcept: any | null;
  targetUsers: any | null;
  userRoles: any | null;
  marketValidation: any | null;
  statusDetail: string | null;
}

export default function AdminDashboard() {
  const { toast } = useToast();
  const { open, setOpen } = useSidebarStore();
  const navigate = useNavigate();

  const { data: validations, isLoading, error } = useQuery({
    queryKey: ['admin-validations'],
    queryFn: async () => {
      const { data, error } = await supabase
        .from('idea_validations')
        .select('*')
        .order('created_at', { ascending: false });
      
      if (error) {
        toast({
          variant: "destructive",
          title: "Error",
          description: "Failed to load validations. Please check your admin permissions.",
        });
        throw error;
      }
      return data as IdeaValidation[];
    },
  });

  const renderValidationStatus = (status: IdeaValidation['status']) => {
    const statusStyles = {
      pending: "bg-gray-100 text-gray-800",
      analyzing: "bg-primary-100 text-primary-800",
      completed: "bg-green-100 text-green-800",
      error: "bg-red-100 text-red-800",
    };

    return (
      <Badge className={`px-2 py-1 ${statusStyles[status]}`}>
        {status.replace('_', ' ').toUpperCase()}
      </Badge>
    );
  };

  const handleViewValidation = (validation: IdeaValidation) => {
    // Navigate to the validation results page to view full details
    navigate(`/validate-idea/results/${validation.id}`, { 
      state: { 
        validationId: validation.id,
        validation: validation,
        fromAdmin: true  // Flag to indicate admin is viewing
      } 
    });
  };

  return (
    <SidebarProvider open={open} setOpen={setOpen}>
      <div className="min-h-screen flex bg-gradient-to-b from-background via-background/95 to-background">
        <AdminSidebar open={open} setOpen={setOpen} />
        <div className="flex-1 p-8">
          <div className="max-w-6xl mx-auto">
            <div className="mb-8">
              <h1 className="text-3xl font-bold text-primary">Admin Dashboard</h1>
              <p className="text-muted-foreground mt-2">Monitor all idea validations</p>
            </div>

            <div className="relative z-10">
              {isLoading ? (
                
                <div className="space-y-4">
                  {[...Array(3)].map((_, i) => (
                    <div key={i} className="w-full h-16">
                      <Skeleton className="h-full w-full" />
                    </div>
                  ))}
                </div>
              ) : error ? (
                
                <Alert variant="destructive" className="mb-4">
                  <AlertCircle className="h-4 w-4" />
                  <AlertTitle>Access Denied</AlertTitle>
                  <AlertDescription>
                    You do not have permission to view this page. Please contact an administrator.
                  </AlertDescription>
                </Alert>
              ) : validations?.length === 0 ? (
                <div className="text-center py-8 text-muted-foreground">
                  No validations found.
                </div>
              ) : (
                <div className="bg-card/60 backdrop-blur-sm rounded-xl border border-primary/10 shadow-lg">
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead className="text-primary">Idea Summary</TableHead>
                        <TableHead className="text-primary">Status</TableHead>
                        <TableHead className="text-primary">User ID</TableHead>
                        <TableHead className="text-primary">Created At</TableHead>
                        <TableHead className="text-primary w-[100px]">Actions</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {validations?.map((validation) => (
                        <TableRow key={validation.id} className="hover:bg-muted/50 cursor-pointer">
                          <TableCell className="font-medium" onClick={() => handleViewValidation(validation)}>
                            {validation.originalIdea.length > 50 
                              ? validation.originalIdea.substring(0, 50) + '...' 
                              : validation.originalIdea}
                          </TableCell>
                          <TableCell onClick={() => handleViewValidation(validation)}>
                            {renderValidationStatus(validation.status)}
                          </TableCell>
                          <TableCell className="font-mono text-sm" onClick={() => handleViewValidation(validation)}>
                            {validation.userId}
                          </TableCell>
                          <TableCell onClick={() => handleViewValidation(validation)}>
                            {new Date(validation.created_at).toLocaleDateString()}
                          </TableCell>
                          <TableCell>
                            <Button
                              variant="ghost"
                              size="icon"
                              onClick={() => handleViewValidation(validation)}
                              title="View Complete Details"
                            >
                              <ExternalLink className="h-4 w-4" />
                            </Button>
                          </TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                </div>
              )}
            </div>
          </div>
        </div>
      </div>
    </SidebarProvider>
  );
}
