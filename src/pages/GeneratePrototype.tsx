import { ProgressTracker } from "@/components/prototype/ProgressTracker";
import { ProjectConfigurationForm } from "@/components/prototype/ProjectConfigurationForm";
import { PrototypePreview } from "@/components/prototype/PrototypePreview";
import { Badge } from "@/components/ui/badge";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { useToast } from "@/hooks/use-toast";
import { supabase } from "@/integrations/supabase/client";
import { ProjectConfig, Prototype, PrototypeTask } from "@/types";
import { generateTaskPlan } from "@/utils/taskPlanner";
import {
    ArrowLeft,
    BarChart3,
    Eye,
    Settings
} from "lucide-react";
import { useEffect, useState } from 'react';
import { useNavigate, useParams } from 'react-router-dom';

export default function GeneratePrototype() {
  const { validationId } = useParams();
  const navigate = useNavigate();
  const { toast } = useToast();
  
  const [validationData, setValidationData] = useState<any>(null);
  const [prototype, setPrototype] = useState<Prototype | null>(null);
  const [tasks, setTasks] = useState<PrototypeTask[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [isGenerating, setIsGenerating] = useState(false);
  const [activeTab, setActiveTab] = useState('configure');

  // Debug logging
  console.log('Current prototype:', prototype);
  console.log('Prototype status:', prototype?.status);
  console.log('Preview tab should be enabled:', prototype?.status === 'completed');

  useEffect(() => {
    if (validationId) {
      fetchValidationData();
      checkExistingPrototype();
    }
  }, [validationId]);

  // Set up real-time subscription for prototype updates
  useEffect(() => {
    if (!prototype?.id) return;

    console.log('Setting up real-time subscription for prototype:', prototype.id);

    const channel = supabase
      .channel('prototype-updates')
      .on(
        'postgres_changes',
        {
          event: 'UPDATE',
          schema: 'public',
          table: 'prototypes',
          filter: `id=eq.${prototype.id}`
        },
        (payload) => {
          console.log('Prototype updated:', payload.new);
          setPrototype(payload.new as Prototype);
        }
      )
      .subscribe();

    return () => {
      console.log('Cleaning up prototype subscription');
      supabase.removeChannel(channel);
    };
  }, [prototype?.id]);

  // Set up real-time subscription for task updates
  useEffect(() => {
    if (!prototype?.id) return;

    console.log('Setting up real-time subscription for tasks');

    const channel = supabase
      .channel('task-updates')
      .on(
        'postgres_changes',
        {
          event: '*',
          schema: 'public',
          table: 'prototype_tasks',
          filter: `prototype_id=eq.${prototype.id}`
        },
        (payload) => {
          console.log('Task updated:', payload);

          if (payload.eventType === 'UPDATE') {
            setTasks(prevTasks =>
              prevTasks.map(task =>
                task.id === payload.new.id ? payload.new as PrototypeTask : task
              )
            );
          }
        }
      )
      .subscribe();

    return () => {
      console.log('Cleaning up task subscription');
      supabase.removeChannel(channel);
    };
  }, [prototype?.id]);

  const fetchValidationData = async () => {
    try {
      const { data, error } = await supabase
        .from('idea_validations')
        .select('*')
        .eq('id', validationId)
        .single();

      if (error) throw error;
      setValidationData(data);
    } catch (error) {
      console.error('Error fetching validation data:', error);
      toast({
        title: "Error",
        description: "Failed to load validation data",
        variant: "destructive"
      });
    } finally {
      setIsLoading(false);
    }
  };

  const checkExistingPrototype = async () => {
    try {
      const { data, error } = await supabase
        .from('prototypes')
        .select('*')
        .eq('validation_id', validationId)
        .maybeSingle();

      if (error && error.code !== 'PGRST116') throw error;
      
      if (data) {
        setPrototype(data);
        setActiveTab('progress');
      }
    } catch (error) {
      console.error('Error checking existing prototype:', error);
    }
  };

  const handleGeneratePrototype = async (config: ProjectConfig) => {
    if (!validationData) return;

    setIsGenerating(true);

    try {
      // Get current user
      const { data: { user }, error: userError } = await supabase.auth.getUser();
      if (userError || !user) {
        console.error('User authentication error:', userError);
        throw new Error('User not authenticated');
      }

      console.log('Creating prototype for user:', user.id);
      console.log('Validation ID:', validationId);
      console.log('Config:', config);

      // Create prototype record
      const { data: prototypeData, error: prototypeError } = await supabase
        .from('prototypes')
        .insert({
          validation_id: validationId,
          user_id: user.id,
          name: config.app_name,
          description: validationData.projectConcept?.overview,
          config: config,
          status: 'planning'
        })
        .select()
        .single();

      if (prototypeError) throw prototypeError;

      // Create project configuration
      const { error: configError } = await supabase
        .from('project_configurations')
        .insert({
          prototype_id: prototypeData.id,
          app_name: config.app_name,
          logo_url: config.logo_url,
          colors: config.colors,
          typography: config.typography,
          selected_features: config.selected_features,
          theme_config: config.theme_config
        });

      if (configError) throw configError;

      // Generate task plan based on actual validation features
      const tasks = generateTaskPlan(config, validationData);
      console.log('Generated tasks:', tasks.length);
      console.log('First task:', tasks[0]);
      console.log('Features being implemented:', validationData?.projectConcept?.features);

      // Insert tasks
      const tasksToInsert = tasks.map(task => ({
        ...task,
        prototype_id: prototypeData.id
      }));

      console.log('Tasks to insert:', tasksToInsert.length);
      console.log('First task to insert:', tasksToInsert[0]);

      const { error: tasksError } = await supabase
        .from('prototype_tasks')
        .insert(tasksToInsert);

      if (tasksError) throw tasksError;

      setPrototype(prototypeData);
      setActiveTab('progress');

      // Start generation process
      await startGeneration(prototypeData.id);

      toast({
        title: "Prototype Generation Started",
        description: "Your prototype is being generated. You can track the progress below."
      });

    } catch (error) {
      console.error('Error generating prototype:', error);
      toast({
        title: "Generation Failed",
        description: "Failed to start prototype generation",
        variant: "destructive"
      });
    } finally {
      setIsGenerating(false);
    }
  };

  const startGeneration = async (prototypeId: string) => {
    try {
      // Skip edge function and update status directly
      const { error } = await supabase
        .from('prototypes')
        .update({ status: 'generating' })
        .eq('id', prototypeId);

      if (error) throw error;

      console.log('Generation started - status updated to generating');

      // Start processing tasks one by one in the frontend
      processTasksSequentially(prototypeId);

    } catch (error) {
      console.error('Error starting generation:', error);
    }
  };

  const processTasksSequentially = async (prototypeId: string) => {
    try {
      // Get all pending tasks for this prototype
      const { data: fetchedTasks, error } = await supabase
        .from('prototype_tasks')
        .select('*')
        .eq('prototype_id', prototypeId)
        .eq('status', 'pending')
        .order('order_index');

      if (error) throw error;

      console.log(`Processing ${fetchedTasks.length} tasks sequentially`);

      // Update local tasks state
      setTasks(fetchedTasks);

      // Process each task one by one
      for (const task of fetchedTasks) {
        try {
          await processTask(task.id);
          // Small delay between tasks
          await new Promise(resolve => setTimeout(resolve, 1000));
        } catch (taskError) {
          console.error(`Error processing task ${task.id}:`, taskError);
          // Continue with next task even if one fails
        }
      }

      // Update prototype status to completed
      await supabase
        .from('prototypes')
        .update({
          status: 'completed',
          completed_at: new Date().toISOString()
        })
        .eq('id', prototypeId);

    } catch (error) {
      console.error('Error in sequential task processing:', error);
      // Update prototype status to failed
      await supabase
        .from('prototypes')
        .update({
          status: 'failed',
          error_message: error.message
        })
        .eq('id', prototypeId);
    }
  };

  const processTask = async (taskId: string) => {
    try {
      // Get task details
      const { data: task, error: taskError } = await supabase
        .from('prototype_tasks')
        .select('*')
        .eq('id', taskId)
        .single();

      if (taskError || !task) {
        throw new Error('Task not found');
      }

      console.log(`Processing task: ${task.title}`);

      // Update task status to in_progress
      await supabase
        .from('prototype_tasks')
        .update({
          status: 'in_progress',
          started_at: new Date().toISOString()
        })
        .eq('id', taskId);

      // Generate actual code based on task type
      const generatedFiles = await generateCodeForTask(task);

      // Store generated files in database
      if (generatedFiles.length > 0) {
        const filesToInsert = generatedFiles.map(file => ({
          prototype_id: task.prototype_id,
          task_id: task.id,
          file_path: file.path,
          file_content: file.content,
          file_type: file.type,
          template_used: 'ai-generated'
        }));

        const { error: filesError } = await supabase
          .from('prototype_files')
          .insert(filesToInsert);

        if (filesError) {
          console.error('Error storing files:', filesError);
        }
      }

      // Update task status to completed
      await supabase
        .from('prototype_tasks')
        .update({
          status: 'completed',
          completed_at: new Date().toISOString(),
          actual_duration: Math.floor(2 + Math.random() * 3),
          generated_files: generatedFiles.map(f => f.path)
        })
        .eq('id', taskId);

      console.log(`Task completed: ${task.title} - Generated ${generatedFiles.length} files`);

    } catch (error) {
      console.error(`Error processing task ${taskId}:`, error);

      // Update task status to failed
      await supabase
        .from('prototype_tasks')
        .update({
          status: 'failed',
          error_message: error.message,
          completed_at: new Date().toISOString()
        })
        .eq('id', taskId);
    }
  };

  const generateCodeForTask = async (task: PrototypeTask): Promise<{path: string, content: string, type: string}[]> => {
    const files: {path: string, content: string, type: string}[] = [];

    // Get validation data for context
    const appName = validationData?.projectConcept?.name || 'AI Assistant';
    const features = validationData?.projectConcept?.features || {};

    switch (task.task_type) {
      case 'setup':
        files.push(...generateSetupFiles(appName));
        break;
      case 'component':
        files.push(...generateComponentFiles(task, appName, features));
        break;
      case 'page':
        files.push(...generatePageFiles(task, appName, features));
        break;
      case 'service':
        files.push(...generateServiceFiles(task, appName, features));
        break;
      case 'styling':
        files.push(...generateStylingFiles(task, appName));
        break;
      default:
        files.push(...generateGenericFiles(task, appName));
    }

    // Add realistic delay for code generation
    await new Promise(resolve => setTimeout(resolve, 1000 + Math.random() * 2000));

    return files;
  };

  const generateSetupFiles = (appName: string) => {
    return [
      {
        path: 'package.json',
        content: `{
  "name": "${appName.toLowerCase().replace(/\s+/g, '-')}",
  "private": true,
  "version": "0.0.0",
  "type": "module",
  "scripts": {
    "dev": "vite",
    "build": "tsc && vite build",
    "lint": "eslint . --ext ts,tsx --report-unused-disable-directives --max-warnings 0",
    "preview": "vite preview"
  },
  "dependencies": {
    "react": "^18.2.0",
    "react-dom": "^18.2.0",
    "react-router-dom": "^6.8.1",
    "lucide-react": "^0.263.1",
    "@radix-ui/react-button": "^1.0.3",
    "@radix-ui/react-card": "^1.0.4"
  },
  "devDependencies": {
    "@types/react": "^18.2.15",
    "@types/react-dom": "^18.2.7",
    "@typescript-eslint/eslint-plugin": "^6.0.0",
    "@typescript-eslint/parser": "^6.0.0",
    "@vitejs/plugin-react": "^4.0.3",
    "autoprefixer": "^10.4.14",
    "eslint": "^8.45.0",
    "eslint-plugin-react-hooks": "^4.6.0",
    "eslint-plugin-react-refresh": "^0.4.3",
    "postcss": "^8.4.24",
    "tailwindcss": "^3.3.0",
    "typescript": "^5.0.2",
    "vite": "^4.4.5"
  }
}`,
        type: 'config'
      },
      {
        path: 'vite.config.ts',
        content: `import { defineConfig } from 'vite'
import react from '@vitejs/plugin-react'

export default defineConfig({
  plugins: [react()],
  server: {
    port: 3000,
    host: true
  }
})`,
        type: 'config'
      },
      {
        path: 'tailwind.config.js',
        content: `/** @type {import('tailwindcss').Config} */
export default {
  content: [
    "./index.html",
    "./src/**/*.{js,ts,jsx,tsx}",
  ],
  theme: {
    extend: {},
  },
  plugins: [],
}`,
        type: 'config'
      }
    ];
  };

  const generateComponentFiles = (task: PrototypeTask, appName: string, features: any) => {
    const componentName = task.title.replace(/[^a-zA-Z0-9]/g, '');

    return [
      {
        path: `src/components/${componentName}.tsx`,
        content: `import React from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';

interface ${componentName}Props {
  // Add props as needed
}

export default function ${componentName}({}: ${componentName}Props) {
  return (
    <Card className="w-full max-w-md mx-auto">
      <CardHeader>
        <CardTitle>${task.title}</CardTitle>
      </CardHeader>
      <CardContent>
        <p className="text-gray-600">
          ${task.description || 'Component implementation for ' + appName}
        </p>
        {/* Add component logic here */}
      </CardContent>
    </Card>
  );
}`,
        type: 'component'
      }
    ];
  };

  const generatePageFiles = (task: PrototypeTask, appName: string, features: any) => {
    const pageName = task.title.replace(/[^a-zA-Z0-9]/g, '');

    return [
      {
        path: `src/pages/${pageName}.tsx`,
        content: `import React from 'react';

export default function ${pageName}() {
  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center">
          <h1 className="text-3xl font-bold text-gray-900 sm:text-4xl">
            ${task.title}
          </h1>
          <p className="mt-4 text-lg text-gray-600">
            ${task.description || 'Page implementation for ' + appName}
          </p>
        </div>

        <div className="mt-12">
          {/* Add page content here */}
          <div className="bg-white shadow rounded-lg p-6">
            <h2 className="text-xl font-semibold mb-4">Features</h2>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {/* Feature cards will go here */}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}`,
        type: 'page'
      }
    ];
  };

  const generateServiceFiles = (task: PrototypeTask, appName: string, features: any) => {
    const serviceName = task.title.replace(/[^a-zA-Z0-9]/g, '');

    return [
      {
        path: `src/services/${serviceName}.ts`,
        content: `// ${task.title} Service
// ${task.description}

export interface ${serviceName}Config {
  apiKey?: string;
  baseUrl?: string;
}

export class ${serviceName} {
  private config: ${serviceName}Config;

  constructor(config: ${serviceName}Config = {}) {
    this.config = {
      baseUrl: '/api',
      ...config
    };
  }

  async processData(data: any): Promise<any> {
    try {
      // Implement service logic here
      console.log('Processing data:', data);

      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000));

      return {
        success: true,
        data: data,
        timestamp: new Date().toISOString()
      };
    } catch (error) {
      console.error('Service error:', error);
      throw error;
    }
  }

  async getData(): Promise<any[]> {
    // Implement data fetching logic
    return [];
  }
}

export const ${serviceName.toLowerCase()} = new ${serviceName}();`,
        type: 'service'
      }
    ];
  };

  const generateStylingFiles = (task: PrototypeTask, appName: string) => {
    return [
      {
        path: 'src/index.css',
        content: `@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 222.2 84% 4.9%;
    --primary: 222.2 47.4% 11.2%;
    --primary-foreground: 210 40% 98%;
  }
}

@layer components {
  .btn-primary {
    @apply bg-blue-600 hover:bg-blue-700 text-white font-medium py-2 px-4 rounded-lg transition-colors;
  }

  .card {
    @apply bg-white rounded-lg shadow-md border border-gray-200;
  }
}`,
        type: 'style'
      }
    ];
  };

  const generateGenericFiles = (task: PrototypeTask, appName: string) => {
    return [
      {
        path: `src/generated/${task.title.replace(/[^a-zA-Z0-9]/g, '')}.ts`,
        content: `// Generated file for: ${task.title}
// Description: ${task.description}

export const ${task.title.replace(/[^a-zA-Z0-9]/g, '')}Config = {
  name: '${task.title}',
  description: '${task.description}',
  createdAt: '${new Date().toISOString()}',
  appName: '${appName}'
};

// Add implementation here
export function initialize() {
  console.log('Initializing ${task.title}');
}`,
        type: 'type'
      }
    ];
  };

  const handleTaskUpdate = (task: PrototypeTask) => {
    // Handle real-time task updates
    console.log('Task updated:', task);
  };

  if (isLoading) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="animate-pulse space-y-6">
          <div className="h-8 bg-gray-200 rounded w-1/3"></div>
          <div className="h-64 bg-gray-200 rounded"></div>
        </div>
      </div>
    );
  }

  if (!validationData) {
    return (
      <div className="container mx-auto px-4 py-8">
        <Card>
          <CardContent className="p-6 text-center">
            <h2 className="text-xl font-semibold mb-2">Validation Not Found</h2>
            <p className="text-muted-foreground mb-4">
              The validation data could not be found.
            </p>
            <Button onClick={() => navigate('/dashboard')}>
              Back to Dashboard
            </Button>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="container mx-auto px-4 py-8">
      {/* Header */}
      <div className="flex items-center gap-4 mb-8">
        <Button 
          variant="ghost" 
          size="sm" 
          onClick={() => navigate(`/validation-results/${validationId}`)}
        >
          <ArrowLeft className="h-4 w-4 mr-2" />
          Back to Validation
        </Button>
        
        <div className="flex-1">
          <h1 className="text-3xl font-bold">Generate Prototype</h1>
          <p className="text-muted-foreground">
            Convert your validated idea into a working React prototype
          </p>
        </div>

        {prototype && (
          <Badge 
            variant={prototype.status === 'completed' ? 'default' : 'secondary'}
            className="capitalize"
          >
            {prototype.status}
          </Badge>
        )}
      </div>

      {/* Main Content */}
      <Tabs value={activeTab} onValueChange={(value) => {
        console.log('Tab changed to:', value);
        setActiveTab(value);
      }} className="space-y-6">
        <TabsList className="grid w-full grid-cols-3">
          <TabsTrigger value="configure" disabled={prototype?.status === 'generating'}>
            <Settings className="h-4 w-4 mr-2" />
            Configure
          </TabsTrigger>
          <TabsTrigger value="progress" disabled={!prototype}>
            <BarChart3 className="h-4 w-4 mr-2" />
            Progress
          </TabsTrigger>
          <TabsTrigger value="preview" disabled={false}>
            <Eye className="h-4 w-4 mr-2" />
            Preview
          </TabsTrigger>
        </TabsList>

        <TabsContent value="configure" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Project Configuration</CardTitle>
              <CardDescription>
                Customize your prototype's appearance and select features to include
              </CardDescription>
            </CardHeader>
            <CardContent>
              <ProjectConfigurationForm
                validationData={validationData}
                onSubmit={handleGeneratePrototype}
                isLoading={isGenerating}
              />
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="progress" className="space-y-6">
          {prototype && (
            <ProgressTracker
              prototypeId={prototype.id}
              onTaskUpdate={handleTaskUpdate}
            />
          )}
        </TabsContent>

        <TabsContent value="preview" className="space-y-6">
          {prototype ? (
            <PrototypePreview
              prototype={prototype}
              onCustomize={() => setActiveTab('configure')}
            />
          ) : (
            <div className="text-center py-8">
              <p className="text-muted-foreground">
                Please generate a prototype first to see the preview.
              </p>
            </div>
          )}
        </TabsContent>


      </Tabs>
    </div>
  );
}
