import { ProgressTracker } from "@/components/prototype/ProgressTracker";
import { ProjectConfigurationForm } from "@/components/prototype/ProjectConfigurationForm";
import { PrototypePreview } from "@/components/prototype/PrototypePreview";
import { Badge } from "@/components/ui/badge";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { useToast } from "@/hooks/use-toast";
import { supabase } from "@/integrations/supabase/client";
import { ProjectConfig, Prototype, PrototypeTask } from "@/types";
import { generateTaskPlan } from "@/utils/taskPlanner";
import {
    ArrowLeft,
    BarChart3,
    Eye,
    Settings
} from "lucide-react";
import { useEffect, useState } from 'react';
import { useNavigate, useParams } from 'react-router-dom';

export default function GeneratePrototype() {
  const { validationId } = useParams();
  const navigate = useNavigate();
  const { toast } = useToast();
  
  const [validationData, setValidationData] = useState<any>(null);
  const [prototype, setPrototype] = useState<Prototype | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [isGenerating, setIsGenerating] = useState(false);
  const [activeTab, setActiveTab] = useState('configure');

  // Debug logging
  console.log('Current prototype:', prototype);
  console.log('Prototype status:', prototype?.status);
  console.log('Preview tab should be enabled:', prototype?.status === 'completed');

  useEffect(() => {
    if (validationId) {
      fetchValidationData();
      checkExistingPrototype();
    }
  }, [validationId]);

  const fetchValidationData = async () => {
    try {
      const { data, error } = await supabase
        .from('idea_validations')
        .select('*')
        .eq('id', validationId)
        .single();

      if (error) throw error;
      setValidationData(data);
    } catch (error) {
      console.error('Error fetching validation data:', error);
      toast({
        title: "Error",
        description: "Failed to load validation data",
        variant: "destructive"
      });
    } finally {
      setIsLoading(false);
    }
  };

  const checkExistingPrototype = async () => {
    try {
      const { data, error } = await supabase
        .from('prototypes')
        .select('*')
        .eq('validation_id', validationId)
        .maybeSingle();

      if (error && error.code !== 'PGRST116') throw error;
      
      if (data) {
        setPrototype(data);
        setActiveTab('progress');
      }
    } catch (error) {
      console.error('Error checking existing prototype:', error);
    }
  };

  const handleGeneratePrototype = async (config: ProjectConfig) => {
    if (!validationData) return;

    setIsGenerating(true);

    try {
      // Get current user
      const { data: { user }, error: userError } = await supabase.auth.getUser();
      if (userError || !user) {
        console.error('User authentication error:', userError);
        throw new Error('User not authenticated');
      }

      console.log('Creating prototype for user:', user.id);
      console.log('Validation ID:', validationId);
      console.log('Config:', config);

      // Create prototype record
      const { data: prototypeData, error: prototypeError } = await supabase
        .from('prototypes')
        .insert({
          validation_id: validationId,
          user_id: user.id,
          name: config.app_name,
          description: validationData.projectConcept?.overview,
          config: config,
          status: 'planning'
        })
        .select()
        .single();

      if (prototypeError) throw prototypeError;

      // Create project configuration
      const { error: configError } = await supabase
        .from('project_configurations')
        .insert({
          prototype_id: prototypeData.id,
          app_name: config.app_name,
          logo_url: config.logo_url,
          colors: config.colors,
          typography: config.typography,
          selected_features: config.selected_features,
          theme_config: config.theme_config
        });

      if (configError) throw configError;

      // Generate task plan
      const tasks = generateTaskPlan(config);
      console.log('Generated tasks:', tasks.length);
      console.log('First task:', tasks[0]);

      // Insert tasks
      const tasksToInsert = tasks.map(task => ({
        ...task,
        prototype_id: prototypeData.id
      }));

      console.log('Tasks to insert:', tasksToInsert.length);
      console.log('First task to insert:', tasksToInsert[0]);

      const { error: tasksError } = await supabase
        .from('prototype_tasks')
        .insert(tasksToInsert);

      if (tasksError) throw tasksError;

      setPrototype(prototypeData);
      setActiveTab('progress');

      // Start generation process
      await startGeneration(prototypeData.id);

      toast({
        title: "Prototype Generation Started",
        description: "Your prototype is being generated. You can track the progress below."
      });

    } catch (error) {
      console.error('Error generating prototype:', error);
      toast({
        title: "Generation Failed",
        description: "Failed to start prototype generation",
        variant: "destructive"
      });
    } finally {
      setIsGenerating(false);
    }
  };

  const startGeneration = async (prototypeId: string) => {
    try {
      // Call the generation edge function
      const { data, error } = await supabase.functions.invoke('generate-prototype', {
        body: { prototypeId }
      });

      if (error) throw error;

      console.log('Generation started:', data);
    } catch (error) {
      console.error('Error starting generation:', error);
    }
  };

  const handleTaskUpdate = (task: PrototypeTask) => {
    // Handle real-time task updates
    console.log('Task updated:', task);
  };

  if (isLoading) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="animate-pulse space-y-6">
          <div className="h-8 bg-gray-200 rounded w-1/3"></div>
          <div className="h-64 bg-gray-200 rounded"></div>
        </div>
      </div>
    );
  }

  if (!validationData) {
    return (
      <div className="container mx-auto px-4 py-8">
        <Card>
          <CardContent className="p-6 text-center">
            <h2 className="text-xl font-semibold mb-2">Validation Not Found</h2>
            <p className="text-muted-foreground mb-4">
              The validation data could not be found.
            </p>
            <Button onClick={() => navigate('/dashboard')}>
              Back to Dashboard
            </Button>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="container mx-auto px-4 py-8">
      {/* Header */}
      <div className="flex items-center gap-4 mb-8">
        <Button 
          variant="ghost" 
          size="sm" 
          onClick={() => navigate(`/validation-results/${validationId}`)}
        >
          <ArrowLeft className="h-4 w-4 mr-2" />
          Back to Validation
        </Button>
        
        <div className="flex-1">
          <h1 className="text-3xl font-bold">Generate Prototype</h1>
          <p className="text-muted-foreground">
            Convert your validated idea into a working React prototype
          </p>
        </div>

        {prototype && (
          <Badge 
            variant={prototype.status === 'completed' ? 'default' : 'secondary'}
            className="capitalize"
          >
            {prototype.status}
          </Badge>
        )}
      </div>

      {/* Main Content */}
      <Tabs value={activeTab} onValueChange={(value) => {
        console.log('Tab changed to:', value);
        setActiveTab(value);
      }} className="space-y-6">
        <TabsList className="grid w-full grid-cols-3">
          <TabsTrigger value="configure" disabled={prototype?.status === 'generating'}>
            <Settings className="h-4 w-4 mr-2" />
            Configure
          </TabsTrigger>
          <TabsTrigger value="progress" disabled={!prototype}>
            <BarChart3 className="h-4 w-4 mr-2" />
            Progress
          </TabsTrigger>
          <TabsTrigger value="preview" disabled={false}>
            <Eye className="h-4 w-4 mr-2" />
            Preview
          </TabsTrigger>
        </TabsList>

        <TabsContent value="configure" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Project Configuration</CardTitle>
              <CardDescription>
                Customize your prototype's appearance and select features to include
              </CardDescription>
            </CardHeader>
            <CardContent>
              <ProjectConfigurationForm
                validationData={validationData}
                onSubmit={handleGeneratePrototype}
                isLoading={isGenerating}
              />
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="progress" className="space-y-6">
          {prototype && (
            <ProgressTracker
              prototypeId={prototype.id}
              onTaskUpdate={handleTaskUpdate}
            />
          )}
        </TabsContent>

        <TabsContent value="preview" className="space-y-6">
          {prototype ? (
            <PrototypePreview
              prototype={prototype}
              onCustomize={() => setActiveTab('configure')}
            />
          ) : (
            <div className="text-center py-8">
              <p className="text-muted-foreground">
                Please generate a prototype first to see the preview.
              </p>
            </div>
          )}
        </TabsContent>


      </Tabs>
    </div>
  );
}
