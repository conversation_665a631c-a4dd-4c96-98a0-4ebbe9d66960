import { ProgressTracker } from "@/components/prototype/ProgressTracker";
import { ProjectConfigurationForm } from "@/components/prototype/ProjectConfigurationForm";
import { PrototypePreview } from "@/components/prototype/PrototypePreview";
import { Badge } from "@/components/ui/badge";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { useToast } from "@/hooks/use-toast";
import { supabase } from "@/integrations/supabase/client";
import { ProjectConfig, Prototype, PrototypeTask } from "@/types";
import { generateTaskPlan } from "@/utils/taskPlanner";
import {
    ArrowLeft,
    BarChart3,
    Eye,
    Settings
} from "lucide-react";
import { useEffect, useState } from 'react';
import { useNavigate, useParams } from 'react-router-dom';

export default function GeneratePrototype() {
  const { validationId } = useParams();
  const navigate = useNavigate();
  const { toast } = useToast();
  
  const [validationData, setValidationData] = useState<any>(null);
  const [prototype, setPrototype] = useState<Prototype | null>(null);
  const [tasks, setTasks] = useState<PrototypeTask[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [isGenerating, setIsGenerating] = useState(false);
  const [activeTab, setActiveTab] = useState('configure');

  // Debug logging
  console.log('Current prototype:', prototype);
  console.log('Prototype status:', prototype?.status);
  console.log('Preview tab should be enabled:', prototype?.status === 'completed' || prototype?.status === 'generating');

  useEffect(() => {
    if (validationId) {
      fetchValidationData();
      checkExistingPrototype();
    }
  }, [validationId]);

  // Set up real-time subscription for prototype updates
  useEffect(() => {
    if (!prototype?.id) return;

    console.log('Setting up real-time subscription for prototype:', prototype.id);

    const channel = supabase
      .channel('prototype-updates')
      .on(
        'postgres_changes',
        {
          event: 'UPDATE',
          schema: 'public',
          table: 'prototypes',
          filter: `id=eq.${prototype.id}`
        },
        (payload) => {
          console.log('Prototype updated:', payload.new);
          setPrototype(payload.new as Prototype);
        }
      )
      .subscribe();

    return () => {
      console.log('Cleaning up prototype subscription');
      supabase.removeChannel(channel);
    };
  }, [prototype?.id]);

  // Set up real-time subscription for task updates
  useEffect(() => {
    if (!prototype?.id) return;

    console.log('Setting up real-time subscription for tasks');

    const channel = supabase
      .channel('task-updates')
      .on(
        'postgres_changes',
        {
          event: '*',
          schema: 'public',
          table: 'prototype_tasks',
          filter: `prototype_id=eq.${prototype.id}`
        },
        (payload) => {
          console.log('Task updated:', payload);

          if (payload.eventType === 'UPDATE') {
            setTasks(prevTasks =>
              prevTasks.map(task =>
                task.id === payload.new.id ? payload.new as PrototypeTask : task
              )
            );
          }
        }
      )
      .subscribe();

    return () => {
      console.log('Cleaning up task subscription');
      supabase.removeChannel(channel);
    };
  }, [prototype?.id]);

  const fetchValidationData = async () => {
    try {
      const { data, error } = await supabase
        .from('idea_validations')
        .select('*')
        .eq('id', validationId)
        .single();

      if (error) throw error;
      setValidationData(data);
    } catch (error) {
      console.error('Error fetching validation data:', error);
      toast({
        title: "Error",
        description: "Failed to load validation data",
        variant: "destructive"
      });
    } finally {
      setIsLoading(false);
    }
  };

  const checkExistingPrototype = async () => {
    try {
      const { data, error } = await supabase
        .from('prototypes')
        .select('*')
        .eq('validation_id', validationId)
        .maybeSingle();

      if (error && error.code !== 'PGRST116') throw error;
      
      if (data) {
        setPrototype(data);
        setActiveTab('progress');
      }
    } catch (error) {
      console.error('Error checking existing prototype:', error);
    }
  };

  const handleGeneratePrototype = async (config: ProjectConfig) => {
    if (!validationData) return;

    setIsGenerating(true);

    try {
      // Get current user
      const { data: { user }, error: userError } = await supabase.auth.getUser();
      if (userError || !user) {
        console.error('User authentication error:', userError);
        throw new Error('User not authenticated');
      }

      console.log('Creating prototype for user:', user.id);
      console.log('Validation ID:', validationId);
      console.log('Config:', config);

      // Create prototype record
      const { data: prototypeData, error: prototypeError } = await supabase
        .from('prototypes')
        .insert({
          validation_id: validationId,
          user_id: user.id,
          name: config.app_name,
          description: validationData.projectConcept?.overview,
          config: config,
          status: 'planning'
        })
        .select()
        .single();

      if (prototypeError) throw prototypeError;

      // Create project configuration
      const { error: configError } = await supabase
        .from('project_configurations')
        .insert({
          prototype_id: prototypeData.id,
          app_name: config.app_name,
          logo_url: config.logo_url,
          colors: config.colors,
          typography: config.typography,
          selected_features: config.selected_features,
          theme_config: config.theme_config
        });

      if (configError) throw configError;

      // Generate task plan based on actual validation features
      const tasks = generateTaskPlan(config, validationData);
      console.log('Generated tasks:', tasks.length);
      console.log('First task:', tasks[0]);
      console.log('Features being implemented:', validationData?.projectConcept?.features);

      // Insert tasks
      const tasksToInsert = tasks.map(task => ({
        ...task,
        prototype_id: prototypeData.id
      }));

      console.log('Tasks to insert:', tasksToInsert.length);
      console.log('First task to insert:', tasksToInsert[0]);

      const { error: tasksError } = await supabase
        .from('prototype_tasks')
        .insert(tasksToInsert);

      if (tasksError) throw tasksError;

      setPrototype(prototypeData);
      setActiveTab('progress');

      // Start generation process
      await startGeneration(prototypeData.id);

      toast({
        title: "Prototype Generation Started",
        description: "Your prototype is being generated. You can track the progress below."
      });

    } catch (error) {
      console.error('Error generating prototype:', error);
      toast({
        title: "Generation Failed",
        description: "Failed to start prototype generation",
        variant: "destructive"
      });
    } finally {
      setIsGenerating(false);
    }
  };



  const startGeneration = async (prototypeId: string) => {
    try {
      // Skip edge function and update status directly
      const { error } = await supabase
        .from('prototypes')
        .update({ status: 'generating' })
        .eq('id', prototypeId);

      if (error) throw error;

      console.log('Generation started - status updated to generating');

      // Start processing tasks one by one in the frontend
      processTasksSequentially(prototypeId);

    } catch (error) {
      console.error('Error starting generation:', error);
    }
  };

  const processTasksSequentially = async (prototypeId: string) => {
    try {
      // Get all pending tasks for this prototype
      const { data: fetchedTasks, error } = await supabase
        .from('prototype_tasks')
        .select('*')
        .eq('prototype_id', prototypeId)
        .eq('status', 'pending')
        .order('order_index');

      if (error) throw error;

      console.log(`Processing ${fetchedTasks.length} tasks sequentially`);

      // Update local tasks state
      setTasks(fetchedTasks);

      // Process each task one by one
      for (const task of fetchedTasks) {
        try {
          await processTask(task.id);
          // Small delay between tasks
          await new Promise(resolve => setTimeout(resolve, 1000));
        } catch (taskError) {
          console.error(`Error processing task ${task.id}:`, taskError);
          // Continue with next task even if one fails
        }
      }

      // Update prototype status to completed
      console.log('All tasks completed, updating prototype status to completed');
      const { error: statusError } = await supabase
        .from('prototypes')
        .update({
          status: 'completed',
          completed_at: new Date().toISOString()
        })
        .eq('id', prototypeId);

      if (statusError) {
        console.error('Error updating prototype status:', statusError);
      } else {
        console.log('Prototype status updated to completed successfully');
        // Auto-switch to preview tab when generation is complete
        setActiveTab('preview');
      }

    } catch (error) {
      console.error('Error in sequential task processing:', error);
      // Update prototype status to failed
      await supabase
        .from('prototypes')
        .update({
          status: 'failed',
          error_message: error.message
        })
        .eq('id', prototypeId);
    }
  };

  const processTask = async (taskId: string) => {
    try {
      // Get task details
      const { data: task, error: taskError } = await supabase
        .from('prototype_tasks')
        .select('*')
        .eq('id', taskId)
        .single();

      if (taskError || !task) {
        throw new Error('Task not found');
      }

      console.log(`Processing task: ${task.title}`);

      // Update task status to in_progress
      await supabase
        .from('prototype_tasks')
        .update({
          status: 'in_progress',
          started_at: new Date().toISOString()
        })
        .eq('id', taskId);

      // Generate actual code based on task type
      console.log(`Generating code for task type: ${task.task_type}`);
      const generatedFiles = await generateCodeForTask(task);
      console.log(`Generated ${generatedFiles.length} files:`, generatedFiles.map(f => f.path));

      // Store generated files in Supabase Storage
      if (generatedFiles.length > 0) {
        console.log('Storing files in Supabase Storage:', generatedFiles.length, 'files');

        // Get current user ID
        const { data: { user } } = await supabase.auth.getUser();
        if (!user) {
          console.error('No authenticated user found');
          return;
        }

        const storedFiles = [];

        for (const file of generatedFiles) {
          try {
            // Create storage path: user_id/prototype_id/file_path
            const storagePath = `${user.id}/${task.prototype_id}/${file.path}`;

            // Upload file to Supabase Storage
            const { data: uploadData, error: uploadError } = await supabase.storage
              .from('prototype-files')
              .upload(storagePath, file.content, {
                contentType: getContentType(file.type),
                upsert: true
              });

            if (uploadError) {
              console.error(`Error uploading file ${file.path}:`, uploadError);
              continue;
            }

            // Get public URL for the file
            const { data: urlData } = supabase.storage
              .from('prototype-files')
              .getPublicUrl(storagePath);

            // Store file metadata in database
            const fileMetadata = {
              prototype_id: task.prototype_id,
              task_id: task.id,
              file_path: file.path,
              file_type: file.type,
              template_used: 'ai-generated',
              storage_path: storagePath,
              file_size: new Blob([file.content]).size,
              public_url: urlData.publicUrl
            };

            storedFiles.push(fileMetadata);
            console.log(`Successfully uploaded: ${file.path} to ${storagePath}`);

          } catch (error) {
            console.error(`Error processing file ${file.path}:`, error);
          }
        }

        // Insert file metadata into database
        if (storedFiles.length > 0) {
          const { data: insertedFiles, error: filesError } = await supabase
            .from('prototype_files')
            .insert(storedFiles)
            .select();

          if (filesError) {
            console.error('Error storing file metadata:', filesError);
          } else {
            console.log('Successfully stored file metadata:', insertedFiles?.length || 0, 'files');
          }
        }
      }

      // Update task status to completed
      await supabase
        .from('prototype_tasks')
        .update({
          status: 'completed',
          completed_at: new Date().toISOString(),
          actual_duration: Math.floor(2 + Math.random() * 3),
          generated_files: generatedFiles.map(f => f.path)
        })
        .eq('id', taskId);

      console.log(`Task completed: ${task.title} - Generated ${generatedFiles.length} files`);

    } catch (error) {
      console.error(`Error processing task ${taskId}:`, error);

      // Update task status to failed
      await supabase
        .from('prototype_tasks')
        .update({
          status: 'failed',
          error_message: error.message,
          completed_at: new Date().toISOString()
        })
        .eq('id', taskId);
    }
  };

  const generateCodeForTask = async (task: PrototypeTask): Promise<{path: string, content: string, type: string}[]> => {
    console.log(`generateCodeForTask called for: ${task.title} (type: ${task.task_type})`);
    const files: {path: string, content: string, type: string}[] = [];

    // Get validation data for context
    const appName = validationData?.projectConcept?.name || 'AI Assistant';
    const features = validationData?.projectConcept?.features || {};
    console.log(`App name: ${appName}, Features:`, features);

    switch (task.task_type) {
      case 'setup':
        console.log('Generating setup files...');
        files.push(...generateSetupFiles(appName));
        break;
      case 'component':
        console.log('Generating component files...');
        files.push(...generateComponentFiles(task, appName, features));
        break;
      case 'page':
        console.log('Generating page files...');
        files.push(...generatePageFiles(task, appName, features));
        break;
      case 'service':
        console.log('Generating service files...');
        files.push(...generateServiceFiles(task, appName, features));
        break;
      case 'styling':
        console.log('Generating styling files...');
        files.push(...generateStylingFiles(task, appName));
        break;
      case 'integration':
        console.log('Generating integration files...');
        files.push(...generateIntegrationFiles(task, appName, features));
        break;
      default:
        console.log('Generating generic files...');
        files.push(...generateGenericFiles(task, appName));
    }

    // Add realistic delay for code generation
    await new Promise(resolve => setTimeout(resolve, 1000 + Math.random() * 2000));

    console.log(`Returning ${files.length} generated files for task: ${task.title}`);
    return files;
  };

  const generateSetupFiles = (appName: string) => {
    return [
      {
        path: 'package.json',
        content: `{
  "name": "${appName.toLowerCase().replace(/\s+/g, '-')}",
  "private": true,
  "version": "0.0.0",
  "type": "module",
  "scripts": {
    "dev": "vite",
    "build": "tsc && vite build",
    "lint": "eslint . --ext ts,tsx --report-unused-disable-directives --max-warnings 0",
    "preview": "vite preview"
  },
  "dependencies": {
    "react": "^18.2.0",
    "react-dom": "^18.2.0",
    "react-router-dom": "^6.8.1",
    "lucide-react": "^0.263.1",
    "@radix-ui/react-button": "^1.0.3",
    "@radix-ui/react-card": "^1.0.4"
  },
  "devDependencies": {
    "@types/react": "^18.2.15",
    "@types/react-dom": "^18.2.7",
    "@typescript-eslint/eslint-plugin": "^6.0.0",
    "@typescript-eslint/parser": "^6.0.0",
    "@vitejs/plugin-react": "^4.0.3",
    "autoprefixer": "^10.4.14",
    "eslint": "^8.45.0",
    "eslint-plugin-react-hooks": "^4.6.0",
    "eslint-plugin-react-refresh": "^0.4.3",
    "postcss": "^8.4.24",
    "tailwindcss": "^3.3.0",
    "typescript": "^5.0.2",
    "vite": "^4.4.5"
  }
}`,
        type: 'config'
      },
      {
        path: 'vite.config.ts',
        content: `import { defineConfig } from 'vite'
import react from '@vitejs/plugin-react'

export default defineConfig({
  plugins: [react()],
  server: {
    port: 3000,
    host: true
  }
})`,
        type: 'config'
      },
      {
        path: 'tailwind.config.js',
        content: `/** @type {import('tailwindcss').Config} */
export default {
  content: [
    "./index.html",
    "./src/**/*.{js,ts,jsx,tsx}",
  ],
  theme: {
    extend: {},
  },
  plugins: [],
}`,
        type: 'config'
      },
      {
        path: 'src/App.tsx',
        content: `import React from 'react';
import { BrowserRouter as Router, Routes, Route } from 'react-router-dom';
import { Navigation } from './components/Navigation';
import { Dashboard } from './pages/Dashboard';
import { ContractAnalyzer } from './components/ContractAnalyzer';
import { ContractComparison } from './components/ContractComparison';
import { RiskAssessment } from './components/RiskAssessment';
import { DocumentManager } from './components/DocumentManager';
import { LegalExplainer } from './components/LegalExplainer';

function App() {
  return (
    <Router>
      <div className="min-h-screen bg-gray-50">
        <Navigation />
        <main className="container mx-auto px-4 py-8">
          <Routes>
            <Route path="/" element={<Dashboard />} />
            <Route path="/analyze" element={<ContractAnalyzer />} />
            <Route path="/compare" element={<ContractComparison />} />
            <Route path="/risk-assessment" element={<RiskAssessment />} />
            <Route path="/documents" element={<DocumentManager />} />
            <Route path="/legal-help" element={<LegalExplainer />} />
          </Routes>
        </main>
      </div>
    </Router>
  );
}

export default App;`,
        type: 'component'
      },
      {
        path: 'src/main.tsx',
        content: `import React from 'react'
import ReactDOM from 'react-dom/client'
import App from './App.tsx'
import './index.css'

ReactDOM.createRoot(document.getElementById('root')!).render(
  <React.StrictMode>
    <App />
  </React.StrictMode>,
)`,
        type: 'component'
      },
      {
        path: 'index.html',
        content: `<!doctype html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <link rel="icon" type="image/svg+xml" href="/vite.svg" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>${appName}</title>
  </head>
  <body>
    <div id="root"></div>
    <script type="module" src="/src/main.tsx"></script>
  </body>
</html>`,
        type: 'config'
      }
    ];
  };

  const generateComponentFiles = (task: PrototypeTask, appName: string, features: any) => {
    // Generate clean component name from the actual feature description
    let componentName = task.title
      .replace(/^(Implement|Create|Build)\s+/i, '')
      .replace(/[^a-zA-Z0-9\s]/g, '')
      .toLowerCase();

    // Smart component naming based on feature type
    if (componentName.includes('contract analysis') || componentName.includes('ai-powered')) {
      componentName = 'ContractAnalyzer';
    } else if (componentName.includes('contract comparison') || componentName.includes('comparison tool')) {
      componentName = 'ContractComparison';
    } else if (componentName.includes('risk assessment') || componentName.includes('risk framework')) {
      componentName = 'RiskAssessment';
    } else if (componentName.includes('contract storage') || componentName.includes('document management')) {
      componentName = 'DocumentManager';
    } else if (componentName.includes('plain language') || componentName.includes('legal explanation')) {
      componentName = 'LegalExplainer';
    } else if (componentName.includes('dashboard') || componentName.includes('analytics')) {
      componentName = 'Dashboard';
    } else if (componentName.includes('user management') || componentName.includes('user')) {
      componentName = 'UserManager';
    } else {
      // Fallback: create clean name from key words
      const words = componentName
        .split(' ')
        .filter(word => word.length > 2 && !['that', 'with', 'and', 'the', 'for'].includes(word))
        .slice(0, 3); // Take first 3 meaningful words

      componentName = words
        .map(word => word.charAt(0).toUpperCase() + word.slice(1))
        .join('');

      // Ensure it's not empty
      if (!componentName) {
        componentName = 'FeatureComponent';
      }
    }

    // Generate feature-specific component based on the actual task description
    const componentContent = generateFeatureComponent(componentName, task.description, task.title, appName);

    return [
      {
        path: `src/components/${componentName}.tsx`,
        content: componentContent,
        type: 'component'
      }
    ];
  };

  const generatePageFiles = (task: PrototypeTask, appName: string, features: any) => {
    const pageName = task.title.replace(/[^a-zA-Z0-9]/g, '').replace(/^(Create|Build)/, '');
    const cleanPageName = pageName.charAt(0).toUpperCase() + pageName.slice(1);

    let pageContent = '';

    if (task.description?.toLowerCase().includes('dashboard') || task.description?.toLowerCase().includes('analytics')) {
      pageContent = generateDashboardPage(cleanPageName, task.description, appName);
    } else if (task.description?.toLowerCase().includes('template') || task.description?.toLowerCase().includes('library')) {
      pageContent = generateTemplateLibraryPage(cleanPageName, task.description, appName);
    } else if (task.description?.toLowerCase().includes('user management') || task.description?.toLowerCase().includes('role')) {
      pageContent = generateUserManagementPage(cleanPageName, task.description, appName);
    } else if (task.description?.toLowerCase().includes('deadline') || task.description?.toLowerCase().includes('notification')) {
      pageContent = generateDeadlineTrackingPage(cleanPageName, task.description, appName);
    } else {
      pageContent = generateGenericPage(cleanPageName, task.description, appName);
    }

    return [
      {
        path: `src/pages/${cleanPageName}.tsx`,
        content: pageContent,
        type: 'page'
      }
    ];
  };

  const generateServiceFiles = (task: PrototypeTask, appName: string, features: any) => {
    const serviceName = task.title.replace(/[^a-zA-Z0-9]/g, '');

    return [
      {
        path: `src/services/${serviceName}.ts`,
        content: `// ${task.title} Service
// ${task.description}

export interface ${serviceName}Config {
  apiKey?: string;
  baseUrl?: string;
}

export class ${serviceName} {
  private config: ${serviceName}Config;

  constructor(config: ${serviceName}Config = {}) {
    this.config = {
      baseUrl: '/api',
      ...config
    };
  }

  async processData(data: any): Promise<any> {
    try {
      // Implement service logic here
      console.log('Processing data:', data);

      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000));

      return {
        success: true,
        data: data,
        timestamp: new Date().toISOString()
      };
    } catch (error) {
      console.error('Service error:', error);
      throw error;
    }
  }

  async getData(): Promise<any[]> {
    // Implement data fetching logic
    return [];
  }
}

export const ${serviceName.toLowerCase()} = new ${serviceName}();`,
        type: 'service'
      }
    ];
  };

  const generateStylingFiles = (task: PrototypeTask, appName: string) => {
    return [
      {
        path: 'src/index.css',
        content: `@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 222.2 84% 4.9%;
    --primary: 222.2 47.4% 11.2%;
    --primary-foreground: 210 40% 98%;
  }
}

@layer components {
  .btn-primary {
    @apply bg-blue-600 hover:bg-blue-700 text-white font-medium py-2 px-4 rounded-lg transition-colors;
  }

  .card {
    @apply bg-white rounded-lg shadow-md border border-gray-200;
  }
}`,
        type: 'style'
      },
      {
        path: 'src/components/Navigation.tsx',
        content: `import React from 'react';
import { Link, useLocation } from 'react-router-dom';
import {
  BarChart3,
  FileSearch,
  GitCompare,
  Shield,
  FolderOpen,
  BookOpen,
  Menu
} from 'lucide-react';

export function Navigation() {
  const location = useLocation();

  const navItems = [
    { path: '/', label: 'Dashboard', icon: BarChart3 },
    { path: '/analyze', label: 'Analyze Contract', icon: FileSearch },
    { path: '/compare', label: 'Compare Contracts', icon: GitCompare },
    { path: '/risk-assessment', label: 'Risk Assessment', icon: Shield },
    { path: '/documents', label: 'Document Manager', icon: FolderOpen },
    { path: '/legal-help', label: 'Legal Help', icon: BookOpen },
  ];

  return (
    <nav className="bg-white shadow-lg border-b">
      <div className="container mx-auto px-4">
        <div className="flex items-center justify-between h-16">
          <div className="flex items-center space-x-8">
            <Link to="/" className="text-xl font-bold text-blue-600">
              ${appName}
            </Link>

            <div className="hidden md:flex space-x-4">
              {navItems.map(({ path, label, icon: Icon }) => (
                <Link
                  key={path}
                  to={path}
                  className={\`flex items-center space-x-2 px-3 py-2 rounded-md text-sm font-medium transition-colors \${
                    location.pathname === path
                      ? 'bg-blue-100 text-blue-700'
                      : 'text-gray-600 hover:text-gray-900 hover:bg-gray-100'
                  }\`}
                >
                  <Icon className="h-4 w-4" />
                  <span>{label}</span>
                </Link>
              ))}
            </div>
          </div>

          <button className="md:hidden">
            <Menu className="h-6 w-6" />
          </button>
        </div>
      </div>
    </nav>
  );
}`,
        type: 'component'
      },
      {
        path: 'src/pages/Dashboard.tsx',
        content: `import React from 'react';
import { Link } from 'react-router-dom';
import {
  FileSearch,
  GitCompare,
  Shield,
  FolderOpen,
  BookOpen,
  TrendingUp,
  AlertTriangle,
  CheckCircle,
  Clock
} from 'lucide-react';

export function Dashboard() {
  const features = [
    {
      title: 'Contract Analysis',
      description: 'AI-powered analysis to identify risks and missing clauses',
      icon: FileSearch,
      path: '/analyze',
      color: 'bg-blue-500'
    },
    {
      title: 'Contract Comparison',
      description: 'Compare different contract versions and templates',
      icon: GitCompare,
      path: '/compare',
      color: 'bg-green-500'
    },
    {
      title: 'Risk Assessment',
      description: 'Customizable framework for industry-specific risk evaluation',
      icon: Shield,
      path: '/risk-assessment',
      color: 'bg-red-500'
    },
    {
      title: 'Document Manager',
      description: 'Secure storage and organization of legal documents',
      icon: FolderOpen,
      path: '/documents',
      color: 'bg-purple-500'
    },
    {
      title: 'Legal Help',
      description: 'Plain language explanations of complex legal terms',
      icon: BookOpen,
      path: '/legal-help',
      color: 'bg-yellow-500'
    }
  ];

  const stats = [
    { label: 'Contracts Analyzed', value: '156', icon: FileSearch, change: '+12%' },
    { label: 'Risk Alerts', value: '23', icon: AlertTriangle, change: '-8%' },
    { label: 'Completed Reviews', value: '89', icon: CheckCircle, change: '+15%' },
    { label: 'Pending Tasks', value: '12', icon: Clock, change: '+3%' }
  ];

  return (
    <div className="space-y-8">
      <div className="text-center">
        <h1 className="text-4xl font-bold text-gray-900 mb-4">
          Welcome to ${appName}
        </h1>
        <p className="text-xl text-gray-600 max-w-3xl mx-auto">
          Your AI-powered legal assistant for contract review, risk analysis, and document management.
        </p>
      </div>

      {/* Stats */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {stats.map((stat, index) => (
          <div key={index} className="bg-white p-6 rounded-lg shadow-md">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">{stat.label}</p>
                <p className="text-2xl font-bold text-gray-900">{stat.value}</p>
              </div>
              <div className="p-3 bg-blue-100 rounded-full">
                <stat.icon className="h-6 w-6 text-blue-600" />
              </div>
            </div>
            <div className="mt-4">
              <span className={\`text-sm font-medium \${
                stat.change.startsWith('+') ? 'text-green-600' : 'text-red-600'
              }\`}>
                {stat.change}
              </span>
              <span className="text-sm text-gray-500 ml-1">from last month</span>
            </div>
          </div>
        ))}
      </div>

      {/* Features */}
      <div>
        <h2 className="text-2xl font-bold text-gray-900 mb-6">Available Features</h2>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {features.map((feature, index) => (
            <Link
              key={index}
              to={feature.path}
              className="bg-white p-6 rounded-lg shadow-md hover:shadow-lg transition-shadow"
            >
              <div className="flex items-center mb-4">
                <div className={\`p-3 rounded-full \${feature.color}\`}>
                  <feature.icon className="h-6 w-6 text-white" />
                </div>
                <h3 className="text-lg font-semibold text-gray-900 ml-4">
                  {feature.title}
                </h3>
              </div>
              <p className="text-gray-600">{feature.description}</p>
            </Link>
          ))}
        </div>
      </div>
    </div>
  );
}`,
        type: 'page'
      }
    ];
  };

  const generateIntegrationFiles = (task: PrototypeTask, appName: string, features: any) => {
    // This creates the final integrated App.tsx that ties everything together
    return [
      {
        path: 'src/App.tsx',
        content: `import React from 'react';
import { BrowserRouter as Router, Routes, Route } from 'react-router-dom';
import { Navigation } from './components/Navigation';
import { Dashboard } from './pages/Dashboard';

function App() {
  return (
    <Router>
      <div className="min-h-screen bg-gray-50">
        <Navigation />
        <main className="container mx-auto px-4 py-8">
          <Routes>
            <Route path="/" element={<Dashboard />} />
            {/* Routes for generated components will be added here */}
          </Routes>
        </main>
      </div>
    </Router>
  );
}

export default App;`,
        type: 'component'
      },
      {
        path: 'src/data/mockData.ts',
        content: `// Mock data for ${appName}
// Generated on: ${new Date().toISOString()}

export const mockUsers = [
  { id: 1, name: 'John Doe', email: '<EMAIL>', role: 'admin' },
  { id: 2, name: 'Jane Smith', email: '<EMAIL>', role: 'user' },
  { id: 3, name: 'Bob Johnson', email: '<EMAIL>', role: 'user' }
];

export const mockProjects = [
  { id: 1, title: 'Project Alpha', status: 'active', progress: 75 },
  { id: 2, title: 'Project Beta', status: 'pending', progress: 30 },
  { id: 3, title: 'Project Gamma', status: 'completed', progress: 100 }
];

export const mockStats = {
  totalUsers: mockUsers.length,
  activeProjects: mockProjects.filter(p => p.status === 'active').length,
  completedProjects: mockProjects.filter(p => p.status === 'completed').length,
  averageProgress: Math.round(mockProjects.reduce((acc, p) => acc + p.progress, 0) / mockProjects.length)
};

// Feature-specific mock data
export const mockFeatureData = {
  features: Object.keys(features || {}),
  appName: '${appName}',
  generatedAt: '${new Date().toISOString()}'
};`,
        type: 'data'
      }
    ];
  };

  const generateGenericFiles = (task: PrototypeTask, appName: string) => {
    return [
      {
        path: `src/generated/${task.title.replace(/[^a-zA-Z0-9]/g, '')}.ts`,
        content: `// Generated file for: ${task.title}
// Description: ${task.description}

export const ${task.title.replace(/[^a-zA-Z0-9]/g, '')}Config = {
  name: '${task.title}',
  description: '${task.description}',
  createdAt: '${new Date().toISOString()}',
  appName: '${appName}'
};

// Add implementation here
export function initialize() {
  console.log('Initializing ${task.title}');
}`,
        type: 'type'
      }
    ];
  };

  const getContentType = (fileType: string): string => {
    switch (fileType) {
      case 'component':
      case 'page':
      case 'service':
      case 'type':
        return 'application/typescript';
      case 'style':
        return 'text/css';
      case 'config':
        return 'application/json';
      case 'test':
        return 'application/typescript';
      default:
        return 'text/plain';
    }
  };

  const downloadPrototypeFiles = async (prototypeId: string) => {
    try {
      console.log('Downloading prototype files for:', prototypeId);

      // Get current user ID
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) {
        console.error('No authenticated user found');
        return;
      }

      // Get all files for this prototype
      const { data: files, error } = await supabase
        .from('prototype_files')
        .select('*')
        .eq('prototype_id', prototypeId);

      if (error) {
        console.error('Error fetching files:', error);
        return;
      }

      if (!files || files.length === 0) {
        console.log('No files found for prototype');
        return;
      }

      // Create a zip file with all the generated files
      const JSZip = (await import('jszip')).default;
      const zip = new JSZip();

      // Add each file to the zip
      for (const file of files) {
        try {
          // Download file content from storage using the correct path structure
          const storagePath = file.storage_path || `${user.id}/${prototypeId}/${file.file_path}`;

          const { data: fileData, error: downloadError } = await supabase.storage
            .from('prototype-files')
            .download(storagePath);

          if (downloadError) {
            console.error(`Error downloading ${file.file_path}:`, downloadError);
            continue;
          }

          // Add file to zip
          const content = await fileData.text();
          zip.file(file.file_path, content);

        } catch (error) {
          console.error(`Error processing file ${file.file_path}:`, error);
        }
      }

      // Generate and download the zip file
      const zipBlob = await zip.generateAsync({ type: 'blob' });
      const url = URL.createObjectURL(zipBlob);

      const link = document.createElement('a');
      link.href = url;
      link.download = `${prototype?.name?.replace(/[^a-zA-Z0-9]/g, '-') || 'prototype'}-source-code.zip`;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      URL.revokeObjectURL(url);

      console.log('Prototype files downloaded successfully');

    } catch (error) {
      console.error('Error downloading prototype files:', error);
    }
  };

  const generateContractAnalysisComponent = (componentName: string, description: string) => {
    return `import React, { useState } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { AlertTriangle, CheckCircle, FileText, Upload } from 'lucide-react';

interface ${componentName}Props {
  onAnalysisComplete?: (results: any) => void;
}

export default function ${componentName}({ onAnalysisComplete }: ${componentName}Props) {
  const [isAnalyzing, setIsAnalyzing] = useState(false);
  const [analysisResults, setAnalysisResults] = useState<any>(null);
  const [uploadedFile, setUploadedFile] = useState<File | null>(null);

  const handleFileUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      setUploadedFile(file);
    }
  };

  const analyzeContract = async () => {
    if (!uploadedFile) return;

    setIsAnalyzing(true);

    // Simulate AI analysis
    setTimeout(() => {
      const mockResults = {
        riskLevel: 'medium',
        risks: [
          { type: 'Missing Clause', description: 'No termination clause found', severity: 'high' },
          { type: 'Unclear Terms', description: 'Payment terms are ambiguous', severity: 'medium' },
          { type: 'Non-standard Language', description: 'Liability clause uses non-standard wording', severity: 'low' }
        ],
        suggestions: [
          'Add clear termination clause with 30-day notice',
          'Specify exact payment due dates and penalties',
          'Review liability limitations with legal counsel'
        ],
        compliance: {
          gdpr: true,
          ccpa: false,
          industry: 'partial'
        }
      };

      setAnalysisResults(mockResults);
      setIsAnalyzing(false);
      onAnalysisComplete?.(mockResults);
    }, 3000);
  };

  return (
    <div className="w-full max-w-4xl mx-auto space-y-6">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <FileText className="h-5 w-5" />
            AI Contract Analysis
          </CardTitle>
          <p className="text-sm text-gray-600">{description}</p>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div className="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center">
              <Upload className="h-8 w-8 mx-auto text-gray-400 mb-2" />
              <input
                type="file"
                accept=".pdf,.doc,.docx,.txt"
                onChange={handleFileUpload}
                className="hidden"
                id="contract-upload"
              />
              <label htmlFor="contract-upload" className="cursor-pointer">
                <span className="text-sm text-gray-600">
                  {uploadedFile ? uploadedFile.name : 'Click to upload contract document'}
                </span>
              </label>
            </div>

            <Button
              onClick={analyzeContract}
              disabled={!uploadedFile || isAnalyzing}
              className="w-full"
            >
              {isAnalyzing ? 'Analyzing Contract...' : 'Start AI Analysis'}
            </Button>
          </div>
        </CardContent>
      </Card>

      {analysisResults && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <AlertTriangle className="h-5 w-5" />
              Analysis Results
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="flex items-center gap-2">
                <span className="text-sm font-medium">Risk Level:</span>
                <Badge variant={analysisResults.riskLevel === 'high' ? 'destructive' :
                              analysisResults.riskLevel === 'medium' ? 'default' : 'secondary'}>
                  {analysisResults.riskLevel.toUpperCase()}
                </Badge>
              </div>

              <div>
                <h4 className="font-medium mb-2">Identified Risks:</h4>
                <div className="space-y-2">
                  {analysisResults.risks.map((risk: any, index: number) => (
                    <div key={index} className="flex items-start gap-2 p-2 bg-red-50 rounded">
                      <AlertTriangle className="h-4 w-4 text-red-500 mt-0.5" />
                      <div>
                        <span className="font-medium text-sm">{risk.type}:</span>
                        <p className="text-sm text-gray-600">{risk.description}</p>
                      </div>
                    </div>
                  ))}
                </div>
              </div>

              <div>
                <h4 className="font-medium mb-2">Recommendations:</h4>
                <div className="space-y-1">
                  {analysisResults.suggestions.map((suggestion: string, index: number) => (
                    <div key={index} className="flex items-start gap-2">
                      <CheckCircle className="h-4 w-4 text-green-500 mt-0.5" />
                      <span className="text-sm">{suggestion}</span>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
}`;
  };

  const generateComparisonComponent = (componentName: string, description: string) => {
    return `import React, { useState } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { GitCompare, FileText, Upload, ArrowRight } from 'lucide-react';

export default function ${componentName}() {
  const [file1, setFile1] = useState<File | null>(null);
  const [file2, setFile2] = useState<File | null>(null);
  const [comparison, setComparison] = useState<any>(null);
  const [isComparing, setIsComparing] = useState(false);

  const compareContracts = async () => {
    if (!file1 || !file2) return;

    setIsComparing(true);

    // Simulate comparison
    setTimeout(() => {
      const mockComparison = {
        differences: [
          { section: 'Payment Terms', type: 'modified', old: 'Net 30 days', new: 'Net 15 days' },
          { section: 'Liability Cap', type: 'added', old: null, new: '$1,000,000 maximum liability' },
          { section: 'Termination Notice', type: 'removed', old: '60 days notice required', new: null }
        ],
        similarity: 87,
        riskChanges: [
          { change: 'Reduced payment terms', impact: 'positive' },
          { change: 'Added liability cap', impact: 'positive' },
          { change: 'Removed termination notice', impact: 'negative' }
        ]
      };

      setComparison(mockComparison);
      setIsComparing(false);
    }, 2000);
  };

  return (
    <div className="w-full max-w-6xl mx-auto space-y-6">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <GitCompare className="h-5 w-5" />
            Contract Comparison Tool
          </CardTitle>
          <p className="text-sm text-gray-600">{description}</p>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 items-center">
            <div className="space-y-2">
              <label className="text-sm font-medium">Original Contract</label>
              <div className="border-2 border-dashed border-gray-300 rounded-lg p-4 text-center">
                <input
                  type="file"
                  accept=".pdf,.doc,.docx"
                  onChange={(e) => setFile1(e.target.files?.[0] || null)}
                  className="hidden"
                  id="file1-upload"
                />
                <label htmlFor="file1-upload" className="cursor-pointer">
                  <FileText className="h-6 w-6 mx-auto text-gray-400 mb-1" />
                  <span className="text-xs text-gray-600">
                    {file1 ? file1.name : 'Upload original'}
                  </span>
                </label>
              </div>
            </div>

            <div className="flex justify-center">
              <ArrowRight className="h-6 w-6 text-gray-400" />
            </div>

            <div className="space-y-2">
              <label className="text-sm font-medium">Updated Contract</label>
              <div className="border-2 border-dashed border-gray-300 rounded-lg p-4 text-center">
                <input
                  type="file"
                  accept=".pdf,.doc,.docx"
                  onChange={(e) => setFile2(e.target.files?.[0] || null)}
                  className="hidden"
                  id="file2-upload"
                />
                <label htmlFor="file2-upload" className="cursor-pointer">
                  <FileText className="h-6 w-6 mx-auto text-gray-400 mb-1" />
                  <span className="text-xs text-gray-600">
                    {file2 ? file2.name : 'Upload updated'}
                  </span>
                </label>
              </div>
            </div>
          </div>

          <Button
            onClick={compareContracts}
            disabled={!file1 || !file2 || isComparing}
            className="w-full mt-4"
          >
            {isComparing ? 'Comparing Contracts...' : 'Compare Contracts'}
          </Button>
        </CardContent>
      </Card>

      {comparison && (
        <Card>
          <CardHeader>
            <CardTitle>Comparison Results</CardTitle>
            <div className="flex items-center gap-2">
              <span className="text-sm">Similarity:</span>
              <Badge variant="secondary">{comparison.similarity}%</Badge>
            </div>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div>
                <h4 className="font-medium mb-2">Key Differences:</h4>
                <div className="space-y-2">
                  {comparison.differences.map((diff: any, index: number) => (
                    <div key={index} className="p-3 border rounded-lg">
                      <div className="flex items-center gap-2 mb-1">
                        <Badge variant={diff.type === 'added' ? 'default' :
                                      diff.type === 'removed' ? 'destructive' : 'secondary'}>
                          {diff.type}
                        </Badge>
                        <span className="font-medium text-sm">{diff.section}</span>
                      </div>
                      {diff.old && <p className="text-sm text-red-600">- {diff.old}</p>}
                      {diff.new && <p className="text-sm text-green-600">+ {diff.new}</p>}
                    </div>
                  ))}
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
}`;
  };

  const generateRiskAssessmentComponent = (componentName: string, description: string) => {
    return `import React, { useState } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Shield, Settings, AlertTriangle } from 'lucide-react';

export default function ${componentName}() {
  const [riskParams, setRiskParams] = useState({
    industry: 'technology',
    contractValue: 50000,
    duration: 12,
    complexity: 3
  });
  const [assessment, setAssessment] = useState<any>(null);

  const runAssessment = () => {
    const mockAssessment = {
      overallRisk: 'medium',
      score: 65,
      factors: [
        { name: 'Contract Value', risk: 'low', score: 20 },
        { name: 'Duration', risk: 'medium', score: 45 },
        { name: 'Industry Compliance', risk: 'high', score: 80 },
        { name: 'Complexity', risk: 'medium', score: 60 }
      ],
      recommendations: [
        'Consider adding additional compliance clauses for technology sector',
        'Review data protection requirements',
        'Add specific performance metrics'
      ]
    };
    setAssessment(mockAssessment);
  };

  return (
    <div className="w-full max-w-4xl mx-auto space-y-6">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Shield className="h-5 w-5" />
            Risk Assessment Framework
          </CardTitle>
          <p className="text-sm text-gray-600">{description}</p>
        </CardHeader>
        <CardContent>
          <div className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="text-sm font-medium mb-2 block">Industry</label>
                <select
                  value={riskParams.industry}
                  onChange={(e) => setRiskParams({...riskParams, industry: e.target.value})}
                  className="w-full p-2 border rounded"
                >
                  <option value="technology">Technology</option>
                  <option value="healthcare">Healthcare</option>
                  <option value="finance">Finance</option>
                  <option value="manufacturing">Manufacturing</option>
                </select>
              </div>

              <div>
                <label className="text-sm font-medium mb-2 block">
                  Contract Value: $\{riskParams.contractValue.toLocaleString()}
                </label>
                <input
                  type="range"
                  value={riskParams.contractValue}
                  onChange={(e) => setRiskParams({...riskParams, contractValue: parseInt(e.target.value)})}
                  max={1000000}
                  min={1000}
                  step={1000}
                  className="w-full"
                />
              </div>
            </div>

            <Button onClick={runAssessment} className="w-full">
              <Settings className="h-4 w-4 mr-2" />
              Run Risk Assessment
            </Button>
          </div>
        </CardContent>
      </Card>

      {assessment && (
        <Card>
          <CardHeader>
            <CardTitle>Assessment Results</CardTitle>
            <div className="flex items-center gap-2">
              <span className="text-sm">Overall Risk:</span>
              <Badge variant={assessment.overallRisk === 'high' ? 'destructive' :
                            assessment.overallRisk === 'medium' ? 'default' : 'secondary'}>
                {assessment.overallRisk.toUpperCase()}
              </Badge>
              <span className="text-sm">Score: {assessment.score}/100</span>
            </div>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {assessment.factors.map((factor: any, index: number) => (
                <div key={index} className="flex items-center justify-between p-2 border rounded">
                  <span className="text-sm font-medium">{factor.name}</span>
                  <div className="flex items-center gap-2">
                    <Badge variant={factor.risk === 'high' ? 'destructive' :
                                  factor.risk === 'medium' ? 'default' : 'secondary'}>
                      {factor.risk}
                    </Badge>
                    <span className="text-sm">{factor.score}/100</span>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
}`;
  };

  const generateDocumentStorageComponent = (componentName: string, description: string) => {
    return `import React, { useState } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { FolderOpen, Upload, Search, Download, Lock } from 'lucide-react';

export default function ${componentName}() {
  const [documents, setDocuments] = useState([
    { id: 1, name: 'Service Agreement.pdf', type: 'contract', size: '2.4 MB', date: '2024-01-15', status: 'active' },
    { id: 2, name: 'NDA Template.docx', type: 'template', size: '1.1 MB', date: '2024-01-10', status: 'draft' },
    { id: 3, name: 'Partnership Agreement.pdf', type: 'contract', size: '3.2 MB', date: '2024-01-05', status: 'signed' }
  ]);
  const [searchTerm, setSearchTerm] = useState('');

  const filteredDocs = documents.filter(doc =>
    doc.name.toLowerCase().includes(searchTerm.toLowerCase())
  );

  return (
    <div className="w-full max-w-6xl mx-auto space-y-6">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <FolderOpen className="h-5 w-5" />
            Document Management System
          </CardTitle>
          <p className="text-sm text-gray-600">{description}</p>
        </CardHeader>
        <CardContent>
          <div className="flex gap-4 mb-4">
            <div className="flex-1 relative">
              <Search className="h-4 w-4 absolute left-3 top-3 text-gray-400" />
              <input
                type="text"
                placeholder="Search documents..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="w-full pl-10 pr-4 py-2 border rounded-lg"
              />
            </div>
            <Button>
              <Upload className="h-4 w-4 mr-2" />
              Upload Document
            </Button>
          </div>

          <div className="space-y-2">
            {filteredDocs.map(doc => (
              <div key={doc.id} className="flex items-center justify-between p-3 border rounded-lg hover:bg-gray-50">
                <div className="flex items-center gap-3">
                  <div className="p-2 bg-blue-100 rounded">
                    <FolderOpen className="h-4 w-4 text-blue-600" />
                  </div>
                  <div>
                    <h4 className="font-medium text-sm">{doc.name}</h4>
                    <p className="text-xs text-gray-500">{doc.size} • {doc.date}</p>
                  </div>
                </div>
                <div className="flex items-center gap-2">
                  <Badge variant={doc.status === 'signed' ? 'default' :
                                doc.status === 'active' ? 'secondary' : 'outline'}>
                    {doc.status}
                  </Badge>
                  <Button size="sm" variant="ghost">
                    <Download className="h-4 w-4" />
                  </Button>
                  <Button size="sm" variant="ghost">
                    <Lock className="h-4 w-4" />
                  </Button>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    </div>
  );
}`;
  };

  const generateExplanationComponent = (componentName: string, description: string) => {
    return `import React, { useState } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { BookOpen, MessageCircle, Lightbulb } from 'lucide-react';

export default function ${componentName}() {
  const [selectedTerm, setSelectedTerm] = useState<string | null>(null);
  const [explanation, setExplanation] = useState<any>(null);

  const legalTerms = [
    { term: 'Force Majeure', complexity: 'high', category: 'contract' },
    { term: 'Indemnification', complexity: 'high', category: 'liability' },
    { term: 'Liquidated Damages', complexity: 'medium', category: 'damages' },
    { term: 'Severability', complexity: 'medium', category: 'contract' },
    { term: 'Governing Law', complexity: 'low', category: 'jurisdiction' }
  ];

  const explainTerm = (term: string) => {
    const explanations: any = {
      'Force Majeure': {
        simple: 'A "force majeure" clause frees parties from liability when extraordinary circumstances beyond their control prevent them from fulfilling their contract.',
        detailed: 'This French term literally means "superior force." It refers to unforeseeable circumstances that prevent a party from fulfilling a contract. Common examples include natural disasters, wars, or government actions.',
        examples: ['Natural disasters like earthquakes or floods', 'Government shutdowns or new regulations', 'Wars or terrorist attacks'],
        implications: ['May excuse non-performance of contract duties', 'Usually requires immediate notification to other party', 'May allow contract termination in extreme cases']
      },
      'Indemnification': {
        simple: 'Indemnification means one party agrees to compensate the other for certain damages or losses.',
        detailed: 'This is a contractual obligation where one party agrees to compensate another party for incurred losses, damages, or liabilities arising from specific circumstances.',
        examples: ['Company A agrees to cover legal costs if Company B is sued over Company A\'s product', 'Contractor indemnifies client against worker injury claims'],
        implications: ['Creates financial protection for the indemnified party', 'Can be mutual or one-sided', 'Often includes duty to defend in lawsuits']
      }
    };

    setSelectedTerm(term);
    setExplanation(explanations[term] || {
      simple: 'This is a legal term that may have specific implications in your contract.',
      detailed: 'For a detailed explanation of this term, please consult with a legal professional.',
      examples: [],
      implications: []
    });
  };

  return (
    <div className="w-full max-w-4xl mx-auto space-y-6">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <BookOpen className="h-5 w-5" />
            Legal Terms Explained
          </CardTitle>
          <p className="text-sm text-gray-600">{description}</p>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-3">
            {legalTerms.map(item => (
              <button
                key={item.term}
                onClick={() => explainTerm(item.term)}
                className="p-3 border rounded-lg text-left hover:bg-gray-50 transition-colors"
              >
                <div className="flex items-center justify-between mb-1">
                  <span className="font-medium text-sm">{item.term}</span>
                  <Badge variant={item.complexity === 'high' ? 'destructive' :
                                item.complexity === 'medium' ? 'default' : 'secondary'}>
                    {item.complexity}
                  </Badge>
                </div>
                <span className="text-xs text-gray-500">{item.category}</span>
              </button>
            ))}
          </div>
        </CardContent>
      </Card>

      {explanation && selectedTerm && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Lightbulb className="h-5 w-5" />
              {selectedTerm} - Plain English Explanation
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="p-4 bg-blue-50 rounded-lg">
                <h4 className="font-medium text-blue-900 mb-2">Simple Explanation:</h4>
                <p className="text-blue-800">{explanation.simple}</p>
              </div>

              <div>
                <h4 className="font-medium mb-2">Detailed Explanation:</h4>
                <p className="text-gray-700">{explanation.detailed}</p>
              </div>

              {explanation.examples.length > 0 && (
                <div>
                  <h4 className="font-medium mb-2">Examples:</h4>
                  <ul className="list-disc list-inside space-y-1">
                    {explanation.examples.map((example: string, index: number) => (
                      <li key={index} className="text-sm text-gray-600">{example}</li>
                    ))}
                  </ul>
                </div>
              )}

              {explanation.implications.length > 0 && (
                <div>
                  <h4 className="font-medium mb-2">Key Implications:</h4>
                  <ul className="list-disc list-inside space-y-1">
                    {explanation.implications.map((implication: string, index: number) => (
                      <li key={index} className="text-sm text-gray-600">{implication}</li>
                    ))}
                  </ul>
                </div>
              )}
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
}`;
  };

  const generateFeatureComponent = (componentName: string, description: string, title: string, appName: string) => {
    // Analyze the feature description to determine what type of component to generate
    const lowerDesc = description.toLowerCase();
    const lowerTitle = title.toLowerCase();

    // Determine feature category and generate appropriate component
    if (lowerDesc.includes('dashboard') || lowerDesc.includes('analytics') || lowerDesc.includes('metrics')) {
      return generateDashboardComponent(componentName, description, title);
    } else if (lowerDesc.includes('form') || lowerDesc.includes('input') || lowerDesc.includes('create') || lowerDesc.includes('add')) {
      return generateFormComponent(componentName, description, title);
    } else if (lowerDesc.includes('list') || lowerDesc.includes('table') || lowerDesc.includes('manage') || lowerDesc.includes('view')) {
      return generateListComponent(componentName, description, title);
    } else if (lowerDesc.includes('search') || lowerDesc.includes('filter') || lowerDesc.includes('find')) {
      return generateSearchComponent(componentName, description, title);
    } else if (lowerDesc.includes('upload') || lowerDesc.includes('file') || lowerDesc.includes('document')) {
      return generateFileComponent(componentName, description, title);
    } else if (lowerDesc.includes('chat') || lowerDesc.includes('message') || lowerDesc.includes('communication')) {
      return generateChatComponent(componentName, description, title);
    } else if (lowerDesc.includes('calendar') || lowerDesc.includes('schedule') || lowerDesc.includes('appointment')) {
      return generateCalendarComponent(componentName, description, title);
    } else if (lowerDesc.includes('payment') || lowerDesc.includes('billing') || lowerDesc.includes('subscription')) {
      return generatePaymentComponent(componentName, description, title);
    } else {
      return generateInteractiveComponent(componentName, description, title, appName);
    }
  };

  const generateInteractiveComponent = (componentName: string, description: string, title: string, appName: string) => {
    return `import React, { useState } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Zap, Settings, CheckCircle, Play, Pause } from 'lucide-react';

export default function ${componentName}() {
  const [isActive, setIsActive] = useState(false);
  const [results, setResults] = useState<any[]>([]);
  const [progress, setProgress] = useState(0);

  const handleAction = () => {
    setIsActive(true);
    setProgress(0);

    // Simulate progressive feature execution
    const interval = setInterval(() => {
      setProgress(prev => {
        if (prev >= 100) {
          clearInterval(interval);
          setResults([
            { id: 1, title: '${title} initialized successfully', status: 'success', time: new Date().toLocaleTimeString() },
            { id: 2, title: 'Processing completed for ${appName}', status: 'success', time: new Date().toLocaleTimeString() },
            { id: 3, title: 'Feature ready for production use', status: 'success', time: new Date().toLocaleTimeString() }
          ]);
          setIsActive(false);
          return 100;
        }
        return prev + 10;
      });
    }, 200);
  };

  const resetFeature = () => {
    setResults([]);
    setProgress(0);
  };

  return (
    <div className="w-full max-w-4xl mx-auto space-y-6">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Zap className="h-5 w-5 text-blue-600" />
            ${componentName}
          </CardTitle>
          <p className="text-sm text-gray-600">{description}</p>
        </CardHeader>
        <CardContent>
          <div className="space-y-6">
            {/* Feature Controls */}
            <div className="p-4 border rounded-lg bg-gray-50">
              <h4 className="font-medium mb-3">Feature Controls</h4>
              <div className="flex gap-3">
                <Button
                  onClick={handleAction}
                  disabled={isActive}
                  className="flex-1"
                >
                  {isActive ? (
                    <>
                      <Settings className="h-4 w-4 mr-2 animate-spin" />
                      Processing... {progress}%
                    </>
                  ) : (
                    <>
                      <Play className="h-4 w-4 mr-2" />
                      Start ${componentName}
                    </>
                  )}
                </Button>

                <Button
                  onClick={resetFeature}
                  variant="outline"
                  disabled={isActive}
                >
                  Reset
                </Button>
              </div>

              {isActive && (
                <div className="mt-3">
                  <div className="w-full bg-gray-200 rounded-full h-2">
                    <div
                      className="bg-blue-600 h-2 rounded-full transition-all duration-300"
                      style={{width: \`\${progress}%\`}}
                    ></div>
                  </div>
                </div>
              )}
            </div>

            {/* Feature Information */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="p-4 border rounded-lg">
                <h5 className="font-medium mb-2">Feature Details</h5>
                <ul className="text-sm text-gray-600 space-y-1">
                  <li>• Implements: ${title}</li>
                  <li>• Application: ${appName}</li>
                  <li>• Status: {isActive ? 'Running' : results.length > 0 ? 'Completed' : 'Ready'}</li>
                  <li>• Type: Interactive Component</li>
                </ul>
              </div>

              <div className="p-4 border rounded-lg">
                <h5 className="font-medium mb-2">Quick Stats</h5>
                <div className="space-y-2">
                  <div className="flex justify-between text-sm">
                    <span>Executions:</span>
                    <Badge variant="secondary">{results.length > 0 ? '1' : '0'}</Badge>
                  </div>
                  <div className="flex justify-between text-sm">
                    <span>Success Rate:</span>
                    <Badge variant="default">100%</Badge>
                  </div>
                  <div className="flex justify-between text-sm">
                    <span>Last Run:</span>
                    <span className="text-gray-500">
                      {results.length > 0 ? results[0].time : 'Never'}
                    </span>
                  </div>
                </div>
              </div>
            </div>

            {/* Results */}
            {results.length > 0 && (
              <div className="space-y-3">
                <h4 className="font-medium flex items-center gap-2">
                  <CheckCircle className="h-4 w-4 text-green-500" />
                  Execution Results
                </h4>
                <div className="space-y-2">
                  {results.map(result => (
                    <div key={result.id} className="flex items-center justify-between p-3 bg-green-50 rounded-lg border border-green-200">
                      <div className="flex items-center gap-2">
                        <CheckCircle className="h-4 w-4 text-green-500" />
                        <span className="text-sm font-medium">{result.title}</span>
                      </div>
                      <div className="flex items-center gap-2">
                        <Badge variant="default">{result.status}</Badge>
                        <span className="text-xs text-gray-500">{result.time}</span>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            )}
          </div>
        </CardContent>
      </Card>
    </div>
  );
}`;
  };

  const generateDashboardPage = (pageName: string, description: string, appName: string) => {
    return `import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { TrendingUp, FileText, AlertTriangle, CheckCircle, Clock, BarChart3 } from 'lucide-react';

export default function ${pageName}() {
  const [metrics, setMetrics] = useState({
    totalContracts: 156,
    activeContracts: 89,
    pendingReview: 23,
    riskAlerts: 12
  });

  const chartData = [
    { month: 'Jan', contracts: 12, risks: 3 },
    { month: 'Feb', contracts: 19, risks: 5 },
    { month: 'Mar', contracts: 15, risks: 2 },
    { month: 'Apr', contracts: 22, risks: 7 },
    { month: 'May', contracts: 18, risks: 4 },
    { month: 'Jun', contracts: 25, risks: 6 }
  ];

  const riskData = [
    { name: 'Low Risk', value: 65, color: '#10B981' },
    { name: 'Medium Risk', value: 25, color: '#F59E0B' },
    { name: 'High Risk', value: 10, color: '#EF4444' }
  ];

  return (
    <div className="min-h-screen bg-gray-50 p-6">
      <div className="max-w-7xl mx-auto">
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900">{appName} Dashboard</h1>
          <p className="text-gray-600 mt-2">{description}</p>
        </div>

        {/* Metrics Cards */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">Total Contracts</p>
                  <p className="text-2xl font-bold text-gray-900">{metrics.totalContracts}</p>
                </div>
                <FileText className="h-8 w-8 text-blue-600" />
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">Active Contracts</p>
                  <p className="text-2xl font-bold text-green-600">{metrics.activeContracts}</p>
                </div>
                <CheckCircle className="h-8 w-8 text-green-600" />
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">Pending Review</p>
                  <p className="text-2xl font-bold text-yellow-600">{metrics.pendingReview}</p>
                </div>
                <Clock className="h-8 w-8 text-yellow-600" />
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">Risk Alerts</p>
                  <p className="text-2xl font-bold text-red-600">{metrics.riskAlerts}</p>
                </div>
                <AlertTriangle className="h-8 w-8 text-red-600" />
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Charts */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <BarChart3 className="h-5 w-5" />
                Contract Activity
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="h-64 bg-gradient-to-r from-blue-50 to-blue-100 rounded-lg flex items-center justify-center">
                <div className="text-center">
                  <BarChart3 className="h-12 w-12 text-blue-500 mx-auto mb-2" />
                  <p className="text-blue-700 font-medium">Contract Activity Chart</p>
                  <p className="text-blue-600 text-sm">Interactive chart showing monthly trends</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <TrendingUp className="h-5 w-5" />
                Risk Distribution
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="h-64 bg-gradient-to-r from-green-50 to-red-50 rounded-lg flex items-center justify-center">
                <div className="text-center">
                  <div className="flex justify-center space-x-4 mb-4">
                    {riskData.map((item, index) => (
                      <div key={index} className="text-center">
                        <div className={\`w-8 h-8 rounded-full mx-auto mb-1\`} style={{backgroundColor: item.color}}></div>
                        <p className="text-xs font-medium">{item.name}</p>
                        <p className="text-xs text-gray-600">{item.value}%</p>
                      </div>
                    ))}
                  </div>
                  <p className="text-gray-700 font-medium">Risk Distribution Overview</p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Recent Activity */}
        <Card>
          <CardHeader>
            <CardTitle>Recent Activity</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {[
                { action: 'Contract analyzed', contract: 'Service Agreement v2.1', time: '2 hours ago', status: 'completed' },
                { action: 'Risk assessment', contract: 'Partnership Agreement', time: '4 hours ago', status: 'warning' },
                { action: 'Document uploaded', contract: 'NDA Template', time: '6 hours ago', status: 'completed' },
                { action: 'Compliance check', contract: 'Vendor Contract', time: '1 day ago', status: 'pending' }
              ].map((activity, index) => (
                <div key={index} className="flex items-center justify-between p-3 border rounded-lg">
                  <div className="flex items-center gap-3">
                    <div className={\`p-2 rounded-full \${
                      activity.status === 'completed' ? 'bg-green-100' :
                      activity.status === 'warning' ? 'bg-yellow-100' : 'bg-blue-100'
                    }\`}>
                      {activity.status === 'completed' ? <CheckCircle className="h-4 w-4 text-green-600" /> :
                       activity.status === 'warning' ? <AlertTriangle className="h-4 w-4 text-yellow-600" /> :
                       <Clock className="h-4 w-4 text-blue-600" />}
                    </div>
                    <div>
                      <p className="font-medium text-sm">{activity.action}</p>
                      <p className="text-xs text-gray-500">{activity.contract}</p>
                    </div>
                  </div>
                  <div className="text-right">
                    <Badge variant={activity.status === 'completed' ? 'default' :
                                  activity.status === 'warning' ? 'destructive' : 'secondary'}>
                      {activity.status}
                    </Badge>
                    <p className="text-xs text-gray-500 mt-1">{activity.time}</p>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}`;
  };

  const generateGenericPage = (pageName: string, description: string, appName: string) => {
    return `import React, { useState } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Zap, Settings, Plus } from 'lucide-react';

export default function ${pageName}() {
  const [items, setItems] = useState([
    { id: 1, title: 'Sample Item 1', status: 'active', date: '2024-01-15' },
    { id: 2, title: 'Sample Item 2', status: 'pending', date: '2024-01-14' },
    { id: 3, title: 'Sample Item 3', status: 'completed', date: '2024-01-13' }
  ]);

  return (
    <div className="min-h-screen bg-gray-50 p-6">
      <div className="max-w-6xl mx-auto">
        <div className="flex items-center justify-between mb-8">
          <div>
            <h1 className="text-3xl font-bold text-gray-900">{pageName}</h1>
            <p className="text-gray-600 mt-2">{description}</p>
          </div>
          <Button>
            <Plus className="h-4 w-4 mr-2" />
            Add New
          </Button>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          <div className="lg:col-span-2">
            <Card>
              <CardHeader>
                <CardTitle>Items</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  {items.map(item => (
                    <div key={item.id} className="flex items-center justify-between p-3 border rounded-lg">
                      <div>
                        <h4 className="font-medium">{item.title}</h4>
                        <p className="text-sm text-gray-500">{item.date}</p>
                      </div>
                      <Badge variant={item.status === 'completed' ? 'default' :
                                    item.status === 'active' ? 'secondary' : 'outline'}>
                        {item.status}
                      </Badge>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </div>

          <div>
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Settings className="h-5 w-5" />
                  Quick Actions
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-2">
                  <Button variant="outline" className="w-full justify-start">
                    <Zap className="h-4 w-4 mr-2" />
                    Quick Action 1
                  </Button>
                  <Button variant="outline" className="w-full justify-start">
                    <Zap className="h-4 w-4 mr-2" />
                    Quick Action 2
                  </Button>
                  <Button variant="outline" className="w-full justify-start">
                    <Zap className="h-4 w-4 mr-2" />
                    Quick Action 3
                  </Button>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </div>
  );
}`;
  };

  const generateTemplateLibraryPage = (pageName: string, description: string, appName: string) => {
    return generateGenericPage(pageName, description, appName);
  };

  const generateUserManagementPage = (pageName: string, description: string, appName: string) => {
    return generateGenericPage(pageName, description, appName);
  };

  const generateDeadlineTrackingPage = (pageName: string, description: string, appName: string) => {
    return generateGenericPage(pageName, description, appName);
  };

  const generateFormComponent = (componentName: string, description: string, title: string) => {
    return `import React, { useState } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Plus, Save, X } from 'lucide-react';

export default function ${componentName}() {
  const [formData, setFormData] = useState({});
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [submitted, setSubmitted] = useState(false);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsSubmitting(true);

    // Simulate form submission
    setTimeout(() => {
      setSubmitted(true);
      setIsSubmitting(false);
    }, 1500);
  };

  return (
    <div className="max-w-2xl mx-auto">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Plus className="h-5 w-5" />
            ${title}
          </CardTitle>
          <p className="text-sm text-gray-600">{description}</p>
        </CardHeader>
        <CardContent>
          {submitted ? (
            <div className="text-center py-8">
              <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <Save className="h-8 w-8 text-green-600" />
              </div>
              <h3 className="text-lg font-medium text-gray-900 mb-2">Successfully Submitted!</h3>
              <p className="text-gray-600">Your ${title.toLowerCase()} has been processed.</p>
              <Button
                onClick={() => {setSubmitted(false); setFormData({});}}
                className="mt-4"
              >
                Create Another
              </Button>
            </div>
          ) : (
            <form onSubmit={handleSubmit} className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Name
                </label>
                <input
                  type="text"
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  placeholder="Enter name..."
                  required
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Description
                </label>
                <textarea
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  rows={3}
                  placeholder="Enter description..."
                  required
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Category
                </label>
                <select className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                  <option value="">Select category...</option>
                  <option value="important">Important</option>
                  <option value="urgent">Urgent</option>
                  <option value="normal">Normal</option>
                </select>
              </div>

              <div className="flex gap-3 pt-4">
                <Button type="submit" disabled={isSubmitting} className="flex-1">
                  {isSubmitting ? 'Submitting...' : 'Submit'}
                </Button>
                <Button type="button" variant="outline">
                  <X className="h-4 w-4 mr-2" />
                  Cancel
                </Button>
              </div>
            </form>
          )}
        </CardContent>
      </Card>
    </div>
  );
}`;
  };

  const generateListComponent = (componentName: string, description: string, title: string) => {
    return `import React, { useState } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { List, Edit, Trash2, Plus } from 'lucide-react';

export default function ${componentName}() {
  const [items, setItems] = useState([
    { id: 1, name: 'Sample Item 1', status: 'active', date: '2024-01-15', category: 'important' },
    { id: 2, name: 'Sample Item 2', status: 'pending', date: '2024-01-14', category: 'normal' },
    { id: 3, name: 'Sample Item 3', status: 'completed', date: '2024-01-13', category: 'urgent' },
    { id: 4, name: 'Sample Item 4', status: 'active', date: '2024-01-12', category: 'normal' }
  ]);

  const deleteItem = (id: number) => {
    setItems(items.filter(item => item.id !== id));
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active': return 'bg-green-100 text-green-800';
      case 'pending': return 'bg-yellow-100 text-yellow-800';
      case 'completed': return 'bg-blue-100 text-blue-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  return (
    <div className="max-w-4xl mx-auto">
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle className="flex items-center gap-2">
                <List className="h-5 w-5" />
                ${title}
              </CardTitle>
              <p className="text-sm text-gray-600 mt-1">{description}</p>
            </div>
            <Button>
              <Plus className="h-4 w-4 mr-2" />
              Add New
            </Button>
          </div>
        </CardHeader>
        <CardContent>
          <div className="space-y-3">
            {items.map(item => (
              <div key={item.id} className="flex items-center justify-between p-4 border rounded-lg hover:bg-gray-50">
                <div className="flex-1">
                  <div className="flex items-center gap-3">
                    <h4 className="font-medium">{item.name}</h4>
                    <Badge className={\`\${getStatusColor(item.status)} border-0\`}>
                      {item.status}
                    </Badge>
                    <Badge variant="outline">{item.category}</Badge>
                  </div>
                  <p className="text-sm text-gray-500 mt-1">Created: {item.date}</p>
                </div>
                <div className="flex items-center gap-2">
                  <Button size="sm" variant="outline">
                    <Edit className="h-4 w-4" />
                  </Button>
                  <Button
                    size="sm"
                    variant="outline"
                    onClick={() => deleteItem(item.id)}
                    className="text-red-600 hover:text-red-700"
                  >
                    <Trash2 className="h-4 w-4" />
                  </Button>
                </div>
              </div>
            ))}
          </div>

          {items.length === 0 && (
            <div className="text-center py-8">
              <List className="h-12 w-12 text-gray-400 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">No items found</h3>
              <p className="text-gray-600">Get started by adding your first item.</p>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}`;
  };

  const generateDashboardComponent = (componentName: string, description: string, title: string) => {
    return `import React from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { TrendingUp, Users, Activity, DollarSign } from 'lucide-react';

export default function ${componentName}() {
  const stats = [
    { title: 'Total Users', value: '2,543', change: '+12%', icon: Users },
    { title: 'Revenue', value: '$45,231', change: '+8%', icon: DollarSign },
    { title: 'Activity', value: '1,234', change: '+23%', icon: Activity },
    { title: 'Growth', value: '89%', change: '+5%', icon: TrendingUp }
  ];

  return (
    <div className="space-y-6">
      <div>
        <h1 className="text-2xl font-bold text-gray-900">{title}</h1>
        <p className="text-gray-600">{description}</p>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {stats.map((stat, index) => (
          <Card key={index}>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">{stat.title}</p>
                  <p className="text-2xl font-bold text-gray-900">{stat.value}</p>
                </div>
                <div className="p-3 bg-blue-100 rounded-full">
                  <stat.icon className="h-6 w-6 text-blue-600" />
                </div>
              </div>
              <div className="mt-4">
                <span className="text-sm font-medium text-green-600">{stat.change}</span>
                <span className="text-sm text-gray-500 ml-1">from last month</span>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <Card>
          <CardHeader>
            <CardTitle>Recent Activity</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {[1,2,3,4].map(i => (
                <div key={i} className="flex items-center gap-3 p-2">
                  <div className="w-2 h-2 bg-blue-600 rounded-full"></div>
                  <span className="text-sm">Activity item {i} completed</span>
                  <span className="text-xs text-gray-500 ml-auto">{i}h ago</span>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Performance</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="h-32 bg-gradient-to-r from-blue-50 to-blue-100 rounded flex items-center justify-center">
              <TrendingUp className="h-8 w-8 text-blue-600" />
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}`;
  };

  const generateSearchComponent = (componentName: string, description: string, title: string) => {
    return generateListComponent(componentName, description, title); // Reuse list component for search
  };

  const generateFileComponent = (componentName: string, description: string, title: string) => {
    return generateInteractiveComponent(componentName, description, title, 'File Manager');
  };

  const generateChatComponent = (componentName: string, description: string, title: string) => {
    return generateInteractiveComponent(componentName, description, title, 'Chat System');
  };

  const generateCalendarComponent = (componentName: string, description: string, title: string) => {
    return generateInteractiveComponent(componentName, description, title, 'Calendar');
  };

  const generatePaymentComponent = (componentName: string, description: string, title: string) => {
    return generateFormComponent(componentName, description, title);
  };

  const handleTaskUpdate = (task: PrototypeTask) => {
    // Handle real-time task updates
    console.log('Task updated:', task);
  };

  if (isLoading) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="animate-pulse space-y-6">
          <div className="h-8 bg-gray-200 rounded w-1/3"></div>
          <div className="h-64 bg-gray-200 rounded"></div>
        </div>
      </div>
    );
  }

  if (!validationData) {
    return (
      <div className="container mx-auto px-4 py-8">
        <Card>
          <CardContent className="p-6 text-center">
            <h2 className="text-xl font-semibold mb-2">Validation Not Found</h2>
            <p className="text-muted-foreground mb-4">
              The validation data could not be found.
            </p>
            <Button onClick={() => navigate('/dashboard')}>
              Back to Dashboard
            </Button>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="container mx-auto px-4 py-8">
      {/* Header */}
      <div className="flex items-center gap-4 mb-8">
        <Button 
          variant="ghost" 
          size="sm" 
          onClick={() => navigate(`/validation-results/${validationId}`)}
        >
          <ArrowLeft className="h-4 w-4 mr-2" />
          Back to Validation
        </Button>
        
        <div className="flex-1">
          <h1 className="text-3xl font-bold">Generate Prototype</h1>
          <p className="text-muted-foreground">
            Convert your validated idea into a working React prototype
          </p>
        </div>

        {prototype && (
          <Badge 
            variant={prototype.status === 'completed' ? 'default' : 'secondary'}
            className="capitalize"
          >
            {prototype.status}
          </Badge>
        )}
      </div>

      {/* Main Content */}
      <Tabs value={activeTab} onValueChange={(value) => {
        console.log('Tab changed to:', value);
        setActiveTab(value);
      }} className="space-y-6">
        <TabsList className="grid w-full grid-cols-3">
          <TabsTrigger value="configure" disabled={prototype?.status === 'generating'}>
            <Settings className="h-4 w-4 mr-2" />
            Configure
          </TabsTrigger>
          <TabsTrigger value="progress" disabled={!prototype}>
            <BarChart3 className="h-4 w-4 mr-2" />
            Progress
          </TabsTrigger>
          <TabsTrigger value="preview" disabled={false}>
            <Eye className="h-4 w-4 mr-2" />
            Preview
          </TabsTrigger>
        </TabsList>

        <TabsContent value="configure" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Project Configuration</CardTitle>
              <CardDescription>
                Customize your prototype's appearance and select features to include
              </CardDescription>
            </CardHeader>
            <CardContent>
              <ProjectConfigurationForm
                validationData={validationData}
                onSubmit={handleGeneratePrototype}
                isLoading={isGenerating}
              />
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="progress" className="space-y-6">
          {prototype && (
            <ProgressTracker
              prototypeId={prototype.id}
              onTaskUpdate={handleTaskUpdate}
            />
          )}
        </TabsContent>

        <TabsContent value="preview" className="space-y-6">
          {prototype ? (
            <PrototypePreview
              prototype={prototype}
              onCustomize={() => setActiveTab('configure')}
              onDownload={() => downloadPrototypeFiles(prototype.id)}
            />
          ) : (
            <div className="text-center py-8">
              <p className="text-muted-foreground">
                Please generate a prototype first to see the preview.
              </p>
            </div>
          )}
        </TabsContent>


      </Tabs>
    </div>
  );
}
