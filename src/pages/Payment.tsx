import { useEffect, useState } from "react";
import { useNavigate, useParams, useLocation } from "react-router-dom";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { useAuthStore } from "@/store/authStore";
import { supabase } from "@/integrations/supabase/client";
import { CreditCard, DollarSign, Package, Info, CheckCircle, AlertTriangle, Loader2, ChevronLeft } from "lucide-react";
import { toast } from "@/hooks/use-toast";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { useQuery } from "@tanstack/react-query";
import { useSubscriptionStore } from "@/store/subscriptionStore";

const ROADMAP_PACKAGES = [
  { id: 'poc', name: 'POC Roadmap', description: 'Proof of Concept development roadmap', price: 10 },
  { id: 'poc-mvp', name: 'POC + MVP Roadmaps', description: 'Both POC and MVP development roadmaps', price: 17 },
  { id: 'all', name: 'All Roadmaps', description: 'POC, MVP and Production-ready roadmaps', price: 20 },
];

export default function Payment() {
  const navigate = useNavigate();
  const location = useLocation();
  const { isAuthenticated, userId } = useAuthStore();
  
  const [cardNumber, setCardNumber] = useState('4242 4242 4242 4242');
  const [cardExpiry, setCardExpiry] = useState('12/25');
  const [cardCVC, setCardCVC] = useState('123');
  const [cardName, setCardName] = useState('Test User');
  
  const { planId } = useParams<{ planId: string }>();
  const searchParams = new URLSearchParams(location.search);
  const priceParam = searchParams.get('price');
  const price = priceParam ? parseFloat(priceParam) : 0;
  
  const [ideaCount, setIdeaCount] = useState(3);
  const [roadmapPack, setRoadmapPack] = useState<string>(searchParams.get('pack') || 'poc');
  
  const [projectName, setProjectName] = useState<string>('');
  
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState(false);
  
  const { data: projectData } = useQuery({
    queryKey: ['project', searchParams.get('project')],
    queryFn: async () => {
      if (!searchParams.get('project')) return null;
      
      const { data, error } = await supabase
        .from('idea_validations')
        .select('originalIdea')
        .eq('id', searchParams.get('project'))
        .single();
        
      if (error) throw error;
      return data;
    },
    enabled: !!searchParams.get('project')
  });
  
  useEffect(() => {
    if (projectData?.originalIdea) {
      setProjectName(projectData.originalIdea);
    }
  }, [projectData]);
  
  useEffect(() => {
    if (!isAuthenticated) {
      navigate('/login?redirect=' + encodeURIComponent(location.pathname + location.search));
    }
  }, [isAuthenticated, navigate, location]);
  
  useEffect(() => {
    const checkSession = async () => {
      const { data } = await supabase.auth.getSession();
      if (!data.session) {
        toast({
          title: "Session Expired",
          description: "Your session has expired. Please log in again.",
          variant: "destructive"
        });
        navigate('/login?redirect=' + encodeURIComponent(location.pathname + location.search));
      }
    };
    
    checkSession();
  }, [navigate, location.pathname, location.search]);
  
  const getPlanName = () => {
    switch (planId) {
      case 'pro':
        return 'Pro Plan';
      case 'multi-idea':
        return 'Multi-Idea Pack';
      case 'roadmap-pack':
        const selectedPack = ROADMAP_PACKAGES.find(pack => pack.id === roadmapPack);
        return selectedPack ? selectedPack.name : 'Roadmap Pack';
      case 'poc-access':
        return 'POC Access';
      case 'mvp-access':
        return 'MVP Access';
      case 'full-access':
        return 'Full Access';
      default:
        return 'Subscription';
    }
  };
  
  const getPriceDisplay = () => {
    if (planId === 'roadmap-pack') {
      const selectedPack = ROADMAP_PACKAGES.find(pack => pack.id === roadmapPack);
      return selectedPack ? `$${selectedPack.price}` : `$${price}`;
    }
    
    if (planId === 'multi-idea') {
      return `$${ideaCount * 5}`;
    }
    
    return `$${price}`;
  };
  
  const fetchCurrentSubscription = useSubscriptionStore(state => state.fetchCurrentSubscription);
  
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!isAuthenticated || !userId) {
      setError('You must be logged in to make a purchase');
      toast({
        title: "Authentication Error",
        description: "You must be logged in to make a purchase",
        variant: "destructive",
      });
      return;
    }
    
    setIsSubmitting(true);
    setError(null);
    
    try {
      const { data: { session } } = await supabase.auth.getSession();
      
      if (!session) {
        throw new Error('No active session found. Please log in again.');
      }
      
      const requestBody: any = {
        plan_id: planId,
        payment_processor: 'dummy',
        payment_id: `dummy-${Date.now()}`
      };
      
      if (planId === 'roadmap-pack') {
        requestBody.pack_type = roadmapPack;
        
        if (searchParams.get('project')) {
          requestBody.project_id = searchParams.get('project');
        }
      }
      
      console.log('Creating subscription with:', requestBody);
      
      const response = await supabase.functions.invoke('create_user_subscription', {
        body: requestBody,
        headers: {
          Authorization: `Bearer ${session.access_token}`
        }
      });
      
      if (response.error) {
        console.error('Edge function error:', response.error);
        throw new Error(response.error.message || 'An unknown error occurred');
      }
      
      const data = response.data;
      
      if (!data || !data.success) {
        throw new Error((data && data.error) || 'Failed to create subscription');
      }
      
      console.log('Subscription created:', data);
      
      setSuccess(true);
      toast({
        title: "Success!",
        description: "Payment processed successfully!",
      });
      
      fetchCurrentSubscription();
      
      setTimeout(() => {
        navigate('/dashboard');
      }, 3000);
    } catch (err: any) {
      console.error('Payment error:', err);
      setError('Payment processing failed: ' + (err.message || 'Please try again.'));
      toast({
        title: "Payment Failed",
        description: err.message || "There was a problem processing your payment.",
        variant: "destructive",
      });
    } finally {
      setIsSubmitting(false);
    }
  };
  
  if (success) {
    return (
      <div className="container max-w-lg mx-auto py-12 px-4">
        <Card className="w-full">
          <CardHeader>
            <div className="flex justify-center mb-4">
              <div className="h-12 w-12 rounded-full bg-green-100 flex items-center justify-center">
                <CheckCircle className="h-6 w-6 text-green-600" />
              </div>
            </div>
            <CardTitle className="text-center">Payment Successful!</CardTitle>
            <CardDescription className="text-center">
              Your subscription to {getPlanName()} has been activated.
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="text-center">
              <p className="mb-4">
                Thank you for your purchase. You can now access all the features included in your plan.
              </p>
              <Button 
                onClick={() => navigate('/dashboard')}
                className="mt-2"
              >
                Go to Dashboard
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }
  
  return (
    <div className="container max-w-lg mx-auto py-12 px-4">
      <div className="mb-6">
        <Button 
          variant="ghost" 
          className="px-0 text-muted-foreground hover:text-foreground"
          onClick={() => navigate(-1)}
        >
          <ChevronLeft className="mr-1 h-4 w-4" />
          Back
        </Button>
      </div>
      
      <Card className="w-full">
        <form onSubmit={handleSubmit}>
          <CardHeader>
            <CardTitle>Complete Your Purchase</CardTitle>
            <CardDescription>
              You're subscribing to {getPlanName()}
            </CardDescription>
          </CardHeader>
          
          <CardContent className="space-y-6">
            {searchParams.get('project') && projectName && (
              <Alert className="bg-blue-50 border-blue-200">
                <Info className="h-4 w-4 text-blue-600" />
                <AlertTitle className="text-blue-700">Project-Specific Purchase</AlertTitle>
                <AlertDescription className="text-blue-600">
                  This purchase will apply to the project: <span className="font-semibold">{projectName}</span>
                </AlertDescription>
              </Alert>
            )}
          
            {planId === 'multi-idea' && (
              <div className="space-y-3">
                <Label htmlFor="ideaCount">Number of Ideas</Label>
                <Select 
                  value={ideaCount.toString()} 
                  onValueChange={(value) => setIdeaCount(parseInt(value))}
                >
                  <SelectTrigger id="ideaCount">
                    <SelectValue placeholder="Select idea count" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="3">3 Ideas ($15)</SelectItem>
                    <SelectItem value="5">5 Ideas ($25)</SelectItem>
                    <SelectItem value="10">10 Ideas ($50)</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            )}
            
            {planId === 'roadmap-pack' && (
              <div className="space-y-3">
                <Label htmlFor="packageType">Select Package</Label>
                <Select 
                  value={roadmapPack} 
                  onValueChange={setRoadmapPack}
                >
                  <SelectTrigger id="packageType">
                    <SelectValue placeholder="Select package" />
                  </SelectTrigger>
                  <SelectContent>
                    {ROADMAP_PACKAGES.map(pack => (
                      <SelectItem key={pack.id} value={pack.id}>
                        {pack.name} (${pack.price})
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                
                <Alert className="bg-blue-50 border-blue-200">
                  <Info className="h-4 w-4 text-blue-600" />
                  <AlertTitle className="text-blue-700">Package Information</AlertTitle>
                  <AlertDescription className="text-blue-600">
                    {roadmapPack === 'poc' && 'Includes detailed Proof of Concept roadmap only.'}
                    {roadmapPack === 'poc-mvp' && 'Includes both POC and MVP development roadmaps.'}
                    {roadmapPack === 'all' && 'Comprehensive package with all roadmap phases.'}
                  </AlertDescription>
                </Alert>
                
                {searchParams.get('project') && (
                  <Alert className="bg-green-50 border-green-200">
                    <Info className="h-4 w-4 text-green-600" />
                    <AlertTitle className="text-green-700">Project-Specific Package</AlertTitle>
                    <AlertDescription className="text-green-600">
                      This roadmap package will be applied to your selected project.
                    </AlertDescription>
                  </Alert>
                )}
              </div>
            )}
            
            <div className="flex items-center justify-between py-4 border-t border-b">
              <div className="font-medium">Total Price:</div>
              <div className="text-xl font-bold">{getPriceDisplay()}</div>
            </div>
            
            <div className="space-y-4">
              <h3 className="font-semibold text-lg">Payment Information</h3>
              
              <div className="space-y-3">
                <div>
                  <Label htmlFor="cardName">Cardholder Name</Label>
                  <Input 
                    type="text"
                    id="cardName"
                    placeholder="John Doe"
                    value={cardName}
                    onChange={(e) => setCardName(e.target.value)}
                    required
                  />
                </div>
                
                <div>
                  <Label htmlFor="cardNumber">Card Number</Label>
                  <Input 
                    type="text"
                    id="cardNumber"
                    placeholder="4242 4242 4242 4242"
                    value={cardNumber}
                    onChange={(e) => setCardNumber(e.target.value)}
                    required
                  />
                </div>
                
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <Label htmlFor="cardExpiry">Expiration Date</Label>
                    <Input 
                      type="text"
                      id="cardExpiry"
                      placeholder="MM/YY"
                      value={cardExpiry}
                      onChange={(e) => setCardExpiry(e.target.value)}
                      required
                    />
                  </div>
                  <div>
                    <Label htmlFor="cardCvc">CVC/CVV</Label>
                    <Input 
                      type="text"
                      id="cardCvc"
                      placeholder="123"
                      value={cardCVC}
                      onChange={(e) => setCardCVC(e.target.value)}
                      required
                    />
                  </div>
                </div>
              </div>
              
              {error && (
                <Alert variant="destructive">
                  <AlertTriangle className="h-4 w-4" />
                  <AlertTitle>Payment Failed</AlertTitle>
                  <AlertDescription>{error}</AlertDescription>
                </Alert>
              )}
            </div>
          </CardContent>
          
          <CardFooter>
            <Button 
              type="submit" 
              className="w-full"
              disabled={isSubmitting}
            >
              {isSubmitting ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Processing...
                </>
              ) : (
                <>
                  <CreditCard className="mr-2 h-4 w-4" />
                  Pay {getPriceDisplay()}
                </>
              )}
            </Button>
          </CardFooter>
        </form>
      </Card>
    </div>
  );
}
