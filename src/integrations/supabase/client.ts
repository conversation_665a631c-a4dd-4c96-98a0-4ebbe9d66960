
// This file is automatically generated. Do not edit it directly.
import { createClient } from '@supabase/supabase-js';
import type { Database } from './types';

const SUPABASE_URL = "https://hvljvkfpbavbtlxzjkqs.supabase.co";
const SUPABASE_PUBLISHABLE_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Imh2bGp2a2ZwYmF2YnRseHpqa3FzIiwicm9sZSI6ImFub24iLCJpYXQiOjE3MzkzNjYwNTAsImV4cCI6MjA1NDk0MjA1MH0.PEwjNBi-tyWnbQJ-ObG9Fqw_xrSo9oGpaaLo2omYu9c";

// Import the supabase client like this:
// import { supabase } from "@/integrations/supabase/client";

export const supabase = createClient<Database>(SUPABASE_URL, SUPABASE_PUBLISHABLE_KEY);

// Add TypeScript type declaration for RPC functions
declare module '@supabase/supabase-js' {
  interface SupabaseClient {
    rpc<T = any>(
      fn: 'is_admin' | 'is_pro_user' | 'get_free_plan_limits' | 'get_pricing_plans' | 
          'update_free_plan_limits' | 'upsert_pricing_plan' | 'delete_pricing_plan' |
          'create_user_subscription' | 'get_roadmap' | 'generate_roadmap' |
          'check_roadmap_access' | 'store_subscription_metadata' |
          'store_market_research' | 'store_competitor_analysis' | 'store_verified_citation',
      params?: {
        input_validation_id?: string;
        input_phase?: string;
        roadmap_content?: any;
        phase?: string;
        user_id?: string;
        subscription_id?: string;
        metadata?: any;
        pack_type?: string;
        plan_id?: string;
        payment_processor?: string;
        payment_id?: string;
        // Enhanced validation parameters
        p_validation_id?: string;
        p_query_type?: string;
        p_search_query?: string;
        p_search_results?: any;
        p_api_provider?: string;
        p_competitor_url?: string;
        p_competitor_name?: string;
        p_scraped_data?: any;
        p_status?: string;
        p_citation_type?: string;
        p_url?: string;
        p_title?: string;
        p_source_name?: string;
        p_relevance_score?: number;
        p_snippet?: string;
        p_published_date?: string;
        // Match the exact parameter names and types that the function expects
        [key: string]: any;
      },
      options?: object
    ): Promise<{ data: T; error: Error | null }>
  }
}
