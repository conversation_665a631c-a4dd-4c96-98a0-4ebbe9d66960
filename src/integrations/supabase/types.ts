export type Json =
  | string
  | number
  | boolean
  | null
  | { [key: string]: J<PERSON> | undefined }
  | Json[]

export type Database = {
  public: {
    Tables: {
      ai_provider_settings: {
        Row: {
          anthropic_model:
            | Database["public"]["Enums"]["anthropic_model_type"]
            | null
          created_at: string | null
          deepseek_model:
            | Database["public"]["Enums"]["deepseek_model_type"]
            | null
          id: string
          is_active: boolean | null
          openai_model: Database["public"]["Enums"]["openai_model_type"] | null
          provider: Database["public"]["Enums"]["ai_provider_type"]
          updated_at: string | null
        }
        Insert: {
          anthropic_model?:
            | Database["public"]["Enums"]["anthropic_model_type"]
            | null
          created_at?: string | null
          deepseek_model?:
            | Database["public"]["Enums"]["deepseek_model_type"]
            | null
          id?: string
          is_active?: boolean | null
          openai_model?: Database["public"]["Enums"]["openai_model_type"] | null
          provider?: Database["public"]["Enums"]["ai_provider_type"]
          updated_at?: string | null
        }
        Update: {
          anthropic_model?:
            | Database["public"]["Enums"]["anthropic_model_type"]
            | null
          created_at?: string | null
          deepseek_model?:
            | Database["public"]["Enums"]["deepseek_model_type"]
            | null
          id?: string
          is_active?: boolean | null
          openai_model?: Database["public"]["Enums"]["openai_model_type"] | null
          provider?: Database["public"]["Enums"]["ai_provider_type"]
          updated_at?: string | null
        }
        Relationships: []
      }
      idea_validations: {
        Row: {
          created_at: string
          id: string
          is_shareable: boolean | null
          marketValidation: Json | null
          originalIdea: string
          projectConcept: Json | null
          status: string
          statusDetail: string | null
          targetUsers: Json | null
          title: string | null
          updated_at: string
          userId: string | null
          userRoles: Json | null
        }
        Insert: {
          created_at?: string
          id?: string
          is_shareable?: boolean | null
          marketValidation?: Json | null
          originalIdea: string
          projectConcept?: Json | null
          status?: string
          statusDetail?: string | null
          targetUsers?: Json | null
          title?: string | null
          updated_at?: string
          userId?: string | null
          userRoles?: Json | null
        }
        Update: {
          created_at?: string
          id?: string
          is_shareable?: boolean | null
          marketValidation?: Json | null
          originalIdea?: string
          projectConcept?: Json | null
          status?: string
          statusDetail?: string | null
          targetUsers?: Json | null
          title?: string | null
          updated_at?: string
          userId?: string | null
          userRoles?: Json | null
        }
        Relationships: []
      }
      pricing_plans: {
        Row: {
          created_at: string
          features: Json | null
          id: string
          interval: string
          is_active: boolean | null
          name: string
          price: number
          updated_at: string
        }
        Insert: {
          created_at?: string
          features?: Json | null
          id: string
          interval: string
          is_active?: boolean | null
          name: string
          price: number
          updated_at?: string
        }
        Update: {
          created_at?: string
          features?: Json | null
          id?: string
          interval?: string
          is_active?: boolean | null
          name?: string
          price?: number
          updated_at?: string
        }
        Relationships: []
      }
      profiles: {
        Row: {
          avatarUrl: string | null
          created_at: string
          firstName: string | null
          id: string
          lastName: string | null
          updated_at: string
        }
        Insert: {
          avatarUrl?: string | null
          created_at?: string
          firstName?: string | null
          id: string
          lastName?: string | null
          updated_at?: string
        }
        Update: {
          avatarUrl?: string | null
          created_at?: string
          firstName?: string | null
          id?: string
          lastName?: string | null
          updated_at?: string
        }
        Relationships: []
      }
      project_planning: {
        Row: {
          branding_preferences: Json | null
          budget_range: Json | null
          created_at: string
          development_phases: Json | null
          id: string
          monthly_users: string | null
          platforms: Json | null
          technology_stack: Json | null
          timeline: Json | null
          updated_at: string
          validation_id: string
        }
        Insert: {
          branding_preferences?: Json | null
          budget_range?: Json | null
          created_at?: string
          development_phases?: Json | null
          id?: string
          monthly_users?: string | null
          platforms?: Json | null
          technology_stack?: Json | null
          timeline?: Json | null
          updated_at?: string
          validation_id: string
        }
        Update: {
          branding_preferences?: Json | null
          budget_range?: Json | null
          created_at?: string
          development_phases?: Json | null
          id?: string
          monthly_users?: string | null
          platforms?: Json | null
          technology_stack?: Json | null
          timeline?: Json | null
          updated_at?: string
          validation_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "project_planning_validation_id_fkey"
            columns: ["validation_id"]
            isOneToOne: false
            referencedRelation: "idea_validations"
            referencedColumns: ["id"]
          },
        ]
      }
      roadmaps: {
        Row: {
          content: Json | null
          created_at: string
          id: string
          is_locked: boolean | null
          phase: string
          status: string | null
          updated_at: string
          user_id: string
          validation_id: string
        }
        Insert: {
          content?: Json | null
          created_at?: string
          id?: string
          is_locked?: boolean | null
          phase: string
          status?: string | null
          updated_at?: string
          user_id: string
          validation_id: string
        }
        Update: {
          content?: Json | null
          created_at?: string
          id?: string
          is_locked?: boolean | null
          phase?: string
          status?: string | null
          updated_at?: string
          user_id?: string
          validation_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "roadmaps_validation_id_fkey"
            columns: ["validation_id"]
            isOneToOne: false
            referencedRelation: "idea_validations"
            referencedColumns: ["id"]
          },
        ]
      }
      system_settings: {
        Row: {
          created_at: string
          id: string
          key: string
          updated_at: string
          value: Json | null
        }
        Insert: {
          created_at?: string
          id?: string
          key: string
          updated_at?: string
          value?: Json | null
        }
        Update: {
          created_at?: string
          id?: string
          key?: string
          updated_at?: string
          value?: Json | null
        }
        Relationships: []
      }
      user_preferences: {
        Row: {
          ai_summary_enabled: boolean
          created_at: string
          id: string
          updated_at: string
          user_id: string
        }
        Insert: {
          ai_summary_enabled?: boolean
          created_at?: string
          id?: string
          updated_at?: string
          user_id: string
        }
        Update: {
          ai_summary_enabled?: boolean
          created_at?: string
          id?: string
          updated_at?: string
          user_id?: string
        }
        Relationships: []
      }
      user_roles: {
        Row: {
          created_at: string
          id: string
          role: Database["public"]["Enums"]["app_role"]
          user_id: string
        }
        Insert: {
          created_at?: string
          id?: string
          role?: Database["public"]["Enums"]["app_role"]
          user_id: string
        }
        Update: {
          created_at?: string
          id?: string
          role?: Database["public"]["Enums"]["app_role"]
          user_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "user_roles_user_id_fkey"
            columns: ["user_id"]
            isOneToOne: false
            referencedRelation: "profiles"
            referencedColumns: ["id"]
          },
        ]
      }
      user_subscriptions: {
        Row: {
          created_at: string
          id: string
          pack_type: string | null
          payment_id: string | null
          payment_processor: string | null
          plan_id: string
          project_id: string | null
          status: string
          updated_at: string
          user_id: string
          valid_until: string
        }
        Insert: {
          created_at?: string
          id?: string
          pack_type?: string | null
          payment_id?: string | null
          payment_processor?: string | null
          plan_id: string
          project_id?: string | null
          status: string
          updated_at?: string
          user_id: string
          valid_until: string
        }
        Update: {
          created_at?: string
          id?: string
          pack_type?: string | null
          payment_id?: string | null
          payment_processor?: string | null
          plan_id?: string
          project_id?: string | null
          status?: string
          updated_at?: string
          user_id?: string
          valid_until?: string
        }
        Relationships: [
          {
            foreignKeyName: "user_subscriptions_plan_id_fkey"
            columns: ["plan_id"]
            isOneToOne: false
            referencedRelation: "pricing_plans"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "user_subscriptions_project_id_fkey"
            columns: ["project_id"]
            isOneToOne: false
            referencedRelation: "idea_validations"
            referencedColumns: ["id"]
          },
        ]
      }
    }
    Views: {
      [_ in never]: never
    }
    Functions: {
      check_roadmap_access: {
        Args: { phase: string; project_id?: string } | { phase: string }
        Returns: boolean
      }
      create_user_subscription: {
        Args:
          | {
              plan_id: string
              payment_processor?: string
              payment_id?: string
              pack_type?: string
            }
          | {
              plan_id: string
              payment_processor?: string
              payment_id?: string
              pack_type?: string
              project_id?: string
            }
          | {
              user_id: string
              plan_id: string
              payment_processor?: string
              payment_id?: string
              pack_type?: string
              project_id?: string
            }
          | { plan_id: string; payment_processor?: string; payment_id?: string }
        Returns: Json
      }
      delete_pricing_plan: {
        Args: { plan_id: string }
        Returns: undefined
      }
      generate_roadmap: {
        Args: {
          input_validation_id: string
          input_phase: string
          roadmap_content: Json
        }
        Returns: string
      }
      get_free_plan_limits: {
        Args: Record<PropertyKey, never>
        Returns: Json
      }
      get_pricing_plans: {
        Args: Record<PropertyKey, never>
        Returns: {
          created_at: string
          features: Json | null
          id: string
          interval: string
          is_active: boolean | null
          name: string
          price: number
          updated_at: string
        }[]
      }
      get_roadmap: {
        Args: { input_validation_id: string }
        Returns: {
          id: string
          validation_id: string
          phase: string
          content: Json
          is_locked: boolean
          created_at: string
          updated_at: string
        }[]
      }
      is_admin: {
        Args: Record<PropertyKey, never>
        Returns: boolean
      }
      is_pro_user: {
        Args: Record<PropertyKey, never>
        Returns: boolean
      }
      store_subscription_metadata: {
        Args: { subscription_id: string; metadata: Json }
        Returns: undefined
      }
      update_free_plan_limits: {
        Args: { new_limits: Json }
        Returns: undefined
      }
      upsert_pricing_plan: {
        Args: {
          plan_id: string
          plan_name: string
          plan_price: number
          plan_interval: string
          plan_features: Json
          is_plan_active: boolean
        }
        Returns: undefined
      }
    }
    Enums: {
      ai_provider_type: "openai" | "anthropic" | "deepseek"
      anthropic_model_type:
        | "claude-3-5-sonnet-latest"
        | "claude-3-5-haiku-latest"
        | "claude-3-opus-latest"
        | "claude-3-7-sonnet-latest"
      app_role: "admin" | "user"
      deepseek_model_type:
        | "deepseek-chat"
        | "deepseek-reasoner"
        | "deepseek-llm-70b"
      idea_validation_status: "pending" | "analyzing" | "completed" | "error"
      openai_model_type:
        | "gpt-4o"
        | "gpt-4o-mini"
        | "o1"
        | "o1-mini"
        | "o3-mini"
        | "gpt-4o-2024-05"
        | "o2-preview"
      project_status:
        | "draft"
        | "submitted"
        | "in_review"
        | "approved"
        | "rejected"
      project_validation_status:
        | "pending"
        | "in_progress"
        | "completed"
        | "analyzing"
    }
    CompositeTypes: {
      [_ in never]: never
    }
  }
}

type DefaultSchema = Database[Extract<keyof Database, "public">]

export type Tables<
  DefaultSchemaTableNameOrOptions extends
    | keyof (DefaultSchema["Tables"] & DefaultSchema["Views"])
    | { schema: keyof Database },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof Database
  }
    ? keyof (Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"] &
        Database[DefaultSchemaTableNameOrOptions["schema"]]["Views"])
    : never = never,
> = DefaultSchemaTableNameOrOptions extends { schema: keyof Database }
  ? (Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"] &
      Database[DefaultSchemaTableNameOrOptions["schema"]]["Views"])[TableName] extends {
      Row: infer R
    }
    ? R
    : never
  : DefaultSchemaTableNameOrOptions extends keyof (DefaultSchema["Tables"] &
        DefaultSchema["Views"])
    ? (DefaultSchema["Tables"] &
        DefaultSchema["Views"])[DefaultSchemaTableNameOrOptions] extends {
        Row: infer R
      }
      ? R
      : never
    : never

export type TablesInsert<
  DefaultSchemaTableNameOrOptions extends
    | keyof DefaultSchema["Tables"]
    | { schema: keyof Database },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof Database
  }
    ? keyof Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"]
    : never = never,
> = DefaultSchemaTableNameOrOptions extends { schema: keyof Database }
  ? Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"][TableName] extends {
      Insert: infer I
    }
    ? I
    : never
  : DefaultSchemaTableNameOrOptions extends keyof DefaultSchema["Tables"]
    ? DefaultSchema["Tables"][DefaultSchemaTableNameOrOptions] extends {
        Insert: infer I
      }
      ? I
      : never
    : never

export type TablesUpdate<
  DefaultSchemaTableNameOrOptions extends
    | keyof DefaultSchema["Tables"]
    | { schema: keyof Database },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof Database
  }
    ? keyof Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"]
    : never = never,
> = DefaultSchemaTableNameOrOptions extends { schema: keyof Database }
  ? Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"][TableName] extends {
      Update: infer U
    }
    ? U
    : never
  : DefaultSchemaTableNameOrOptions extends keyof DefaultSchema["Tables"]
    ? DefaultSchema["Tables"][DefaultSchemaTableNameOrOptions] extends {
        Update: infer U
      }
      ? U
      : never
    : never

export type Enums<
  DefaultSchemaEnumNameOrOptions extends
    | keyof DefaultSchema["Enums"]
    | { schema: keyof Database },
  EnumName extends DefaultSchemaEnumNameOrOptions extends {
    schema: keyof Database
  }
    ? keyof Database[DefaultSchemaEnumNameOrOptions["schema"]]["Enums"]
    : never = never,
> = DefaultSchemaEnumNameOrOptions extends { schema: keyof Database }
  ? Database[DefaultSchemaEnumNameOrOptions["schema"]]["Enums"][EnumName]
  : DefaultSchemaEnumNameOrOptions extends keyof DefaultSchema["Enums"]
    ? DefaultSchema["Enums"][DefaultSchemaEnumNameOrOptions]
    : never

export type CompositeTypes<
  PublicCompositeTypeNameOrOptions extends
    | keyof DefaultSchema["CompositeTypes"]
    | { schema: keyof Database },
  CompositeTypeName extends PublicCompositeTypeNameOrOptions extends {
    schema: keyof Database
  }
    ? keyof Database[PublicCompositeTypeNameOrOptions["schema"]]["CompositeTypes"]
    : never = never,
> = PublicCompositeTypeNameOrOptions extends { schema: keyof Database }
  ? Database[PublicCompositeTypeNameOrOptions["schema"]]["CompositeTypes"][CompositeTypeName]
  : PublicCompositeTypeNameOrOptions extends keyof DefaultSchema["CompositeTypes"]
    ? DefaultSchema["CompositeTypes"][PublicCompositeTypeNameOrOptions]
    : never

export const Constants = {
  public: {
    Enums: {
      ai_provider_type: ["openai", "anthropic", "deepseek"],
      anthropic_model_type: [
        "claude-3-5-sonnet-latest",
        "claude-3-5-haiku-latest",
        "claude-3-opus-latest",
        "claude-3-7-sonnet-latest",
      ],
      app_role: ["admin", "user"],
      deepseek_model_type: [
        "deepseek-chat",
        "deepseek-reasoner",
        "deepseek-llm-70b",
      ],
      idea_validation_status: ["pending", "analyzing", "completed", "error"],
      openai_model_type: [
        "gpt-4o",
        "gpt-4o-mini",
        "o1",
        "o1-mini",
        "o3-mini",
        "gpt-4o-2024-05",
        "o2-preview",
      ],
      project_status: [
        "draft",
        "submitted",
        "in_review",
        "approved",
        "rejected",
      ],
      project_validation_status: [
        "pending",
        "in_progress",
        "completed",
        "analyzing",
      ],
    },
  },
} as const
