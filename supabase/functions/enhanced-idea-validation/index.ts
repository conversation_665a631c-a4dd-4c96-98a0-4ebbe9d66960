// deno-lint-ignore-file no-explicit-any
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2.39.3'
import { OpenAI } from 'https://esm.sh/openai@4.24.1'
import { Anthropic } from 'https://esm.sh/@anthropic-ai/sdk@0.14.1'

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
}

interface EnhancedValidationRequest {
  idea: string;
  validationId: string;
}

// Tavily client class for Deno
class TavilyClient {
  private apiKey: string;
  private baseUrl = 'https://api.tavily.com';

  constructor(apiKey: string) {
    this.apiKey = apiKey;
  }

  async search(query: string, options: any = {}) {
    const response = await fetch(`${this.baseUrl}/search`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${this.apiKey}`
      },
      body: JSON.stringify({
        query,
        search_depth: options.search_depth || 'advanced',
        include_answer: options.include_answer !== false,
        include_raw_content: options.include_raw_content || false,
        max_results: options.max_results || 5,
        include_domains: options.include_domains || [],
        exclude_domains: options.exclude_domains || []
      })
    });

    if (!response.ok) {
      throw new Error(`Tavily API error: ${response.statusText}`);
    }

    return await response.json();
  }
}

// Firecrawl client class for Deno
class FirecrawlClient {
  private apiKey: string;
  private baseUrl = 'https://api.firecrawl.dev';

  constructor(apiKey: string) {
    this.apiKey = apiKey;
  }

  async scrapeUrl(url: string, options: any = {}) {
    try {
      const response = await fetch(`${this.baseUrl}/v1/scrape`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${this.apiKey}`
        },
        body: JSON.stringify({
          url,
          formats: options.formats || ['markdown'],
          extract: options.extract || {},
          actions: options.actions || [],
          timeout: options.timeout || 30000
        })
      });

      if (!response.ok) {
        throw new Error(`Firecrawl API error: ${response.status} ${response.statusText}`);
      }

      const contentType = response.headers.get('content-type');
      if (contentType && contentType.includes('application/json')) {
        return await response.json();
      } else {
        // If response is not JSON, wrap the text content
        const textContent = await response.text();
        return {
          success: true,
          data: {
            markdown: textContent,
            metadata: {
              title: 'Scraped Content',
              description: 'Content scraped from ' + url
            }
          }
        };
      }
    } catch (error) {
      console.error(`Firecrawl scraping failed for ${url}:`, error);
      return {
        success: false,
        error: error.message,
        data: null
      };
    }
  }
}

// Enhanced market research function
async function enhancedMarketResearch(idea: string, tavilyClient: TavilyClient, firecrawlClient: FirecrawlClient) {
  try {
    console.log('Starting enhanced market research for idea:', idea);

    // 1. Search for market data, competitors, and trends
    const searchPromises = [
      tavilyClient.search(`${idea} market size 2024 TAM SAM industry analysis`, {
        search_depth: 'advanced',
        max_results: 3
      }),
      tavilyClient.search(`${idea} competitors analysis comparison 2024`, {
        search_depth: 'advanced', 
        max_results: 3
      }),
      tavilyClient.search(`${idea} industry trends funding rounds investment 2024`, {
        search_depth: 'advanced',
        max_results: 3
      })
    ];

    const [marketData, competitorData, trendsData] = await Promise.all(searchPromises);

    // 2. Extract competitor URLs for detailed analysis
    const competitorUrls = extractCompetitorUrls(competitorData.results || []);
    console.log('Found competitor URLs:', competitorUrls);

    // 3. Scrape top competitor websites (limit to 2 to avoid timeouts)
    const competitorScrapingPromises = competitorUrls.slice(0, 2).map(async url => {
      try {
        const result = await firecrawlClient.scrapeUrl(url, {
          formats: ['markdown'],
          timeout: 15000
        });
        return { url, success: result.success, data: result.data, error: result.error || null };
      } catch (error) {
        console.log(`Failed to scrape ${url}:`, error.message);
        return { url, success: false, error: error.message, data: null };
      }
    });

    const scrapedCompetitors = await Promise.all(competitorScrapingPromises);

    return {
      marketResearch: {
        marketData: marketData.results || [],
        competitorData: competitorData.results || [],
        trendsData: trendsData.results || [],
        scrapedCompetitors: scrapedCompetitors.filter(result => result.success && result.data),
        searchTimestamp: new Date().toISOString()
      },
      realCitations: [
        ...extractCitations(marketData.results || []),
        ...extractCitations(competitorData.results || []),
        ...extractCitations(trendsData.results || [])
      ]
    };

  } catch (error) {
    console.error('Enhanced market research failed:', error);
    return {
      marketResearch: null,
      realCitations: [],
      error: error.message
    };
  }
}

// Helper function to extract competitor URLs from search results
function extractCompetitorUrls(searchResults: any[]): string[] {
  const urls: string[] = [];
  
  for (const result of searchResults) {
    if (result.url && 
        !result.url.includes('wikipedia.org') && 
        !result.url.includes('linkedin.com') &&
        !result.url.includes('crunchbase.com')) {
      urls.push(result.url);
    }
  }
  
  return urls.slice(0, 5); // Limit to top 5 URLs
}

// Helper function to extract citations from search results
function extractCitations(searchResults: any[]): any[] {
  return searchResults.map(result => ({
    url: result.url,
    title: result.title,
    source: new URL(result.url).hostname.replace('www.', ''),
    relevance: result.score || 'high',
    publishedDate: result.published_date || new Date().toISOString().split('T')[0],
    snippet: result.content?.substring(0, 200) + '...' || 'No snippet available'
  }));
}

// Enhanced prompts with real data integration
const enhancedPrompts = {
  projectConcept: `You are a senior technology expert analyzing a project idea with REAL market data and competitor information.

Using the provided REAL market research data and competitor analysis, create a detailed project concept with:

1. Project Overview (150-200 words):
- Reference actual market size and trends from the research data
- Highlight competitive landscape based on real competitor analysis
- Include specific market opportunities identified in the research

2. User Roles & Responsibilities (exactly 4 roles, 4 responsibilities each)

3. Feature Categories (exactly 5 features per category):
- Core Features, Must-Have Features, Should-Have Features, Nice-to-Have Features, Future Features
- Each feature needs: description, 3 acceptance criteria, user roles, platform availability

4. REAL Citations:
- Use ONLY the citations provided from the actual research data
- Include URL, title, source, relevance, and snippet from real searches

IMPORTANT: Base your analysis on the REAL data provided, not generic knowledge.`,

  marketValidation: `You are a market research analyst with access to REAL, current market data.

Using the provided REAL market research, competitor analysis, and industry trends data, create a comprehensive market validation with:

1. Market Analysis:
- TAM/SAM/SOM based on actual market size data found
- Reference specific market reports and data from the research
- Include actual growth rates and trends from the data

2. Competitive Analysis:
- Use REAL competitor data from scraped websites
- Compare actual features, pricing, and positioning
- Identify gaps based on real competitive intelligence

3. Market Trends:
- Reference actual funding rounds and investment data
- Include real industry developments and trends
- Use specific examples from the research data

4. VERIFIED Citations:
- Use ONLY the real citations from search results
- Each citation includes actual URL, title, source, and verification date

CRITICAL: Base ALL analysis on the provided real data, not assumptions.`
};

Deno.serve(async (req) => {
  if (req.method === 'OPTIONS') {
    return new Response(null, { headers: corsHeaders });
  }

  try {
    const requestData = await req.json() as EnhancedValidationRequest;
    const { idea, validationId } = requestData;

    if (!idea || !validationId) {
      return new Response(
        JSON.stringify({ error: 'Missing idea or validationId' }),
        { status: 400, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
      );
    }

    // Initialize clients
    const supabase = createClient(
      Deno.env.get('SUPABASE_URL') ?? '',
      Deno.env.get('SUPABASE_SERVICE_ROLE_KEY') ?? ''
    );

    const tavilyClient = new TavilyClient(Deno.env.get('TAVILY_API_KEY') ?? '');
    const firecrawlClient = new FirecrawlClient(Deno.env.get('FIRECRAWL_API_KEY') ?? '');

    // Update status to analyzing
    await supabase
      .from('idea_validations')
      .update({ 
        status: 'analyzing',
        statusDetail: 'Conducting enhanced market research...'
      })
      .eq('id', validationId);

    // 1. Enhanced market research with real data
    const marketResearch = await enhancedMarketResearch(idea, tavilyClient, firecrawlClient);

    // Update status
    await supabase
      .from('idea_validations')
      .update({ 
        statusDetail: 'Analyzing project concept with real data...'
      })
      .eq('id', validationId);

    // 2. Get AI provider settings
    const { data: aiSettings } = await supabase
      .from('ai_provider_settings')
      .select('*')
      .eq('is_active', true)
      .single();

    if (!aiSettings) {
      throw new Error('No active AI provider found');
    }

    // 3. Generate enhanced project concept with real data
    const projectConceptInput = `
Project Idea: ${idea}

REAL MARKET RESEARCH DATA:
${JSON.stringify(marketResearch.marketResearch, null, 2)}

REAL CITATIONS:
${JSON.stringify(marketResearch.realCitations, null, 2)}
`;

    let projectConcept;
    
    // Generate project concept using selected AI provider
    switch (aiSettings.provider) {
      case 'openai': {
        const openai = new OpenAI({ apiKey: Deno.env.get('OPENAI_API_KEY') });
        const completion = await openai.chat.completions.create({
          model: aiSettings.openai_model ?? 'gpt-4o-mini',
          messages: [
            { role: "system", content: enhancedPrompts.projectConcept },
            { role: "user", content: projectConceptInput }
          ],
          response_format: { type: "json_object" },
          temperature: 0.7,
          max_tokens: 4000
        });
        
        projectConcept = JSON.parse(completion.choices[0].message.content || '{}');
        break;
      }
      
      case 'anthropic': {
        const anthropic = new Anthropic({ apiKey: Deno.env.get('ANTHROPIC_API_KEY') ?? '' });
        const message = await anthropic.messages.create({
          model: aiSettings.anthropic_model ?? 'claude-3-5-sonnet-20240620',
          system: enhancedPrompts.projectConcept,
          messages: [{ role: 'user', content: projectConceptInput }],
          max_tokens: 4000
        });
        
        const content = message.content[0].text.replace(/```json\n|\n```/g, '');
        projectConcept = JSON.parse(content);
        break;
      }
      
      default:
        throw new Error('Unsupported AI provider');
    }

    // Update status
    await supabase
      .from('idea_validations')
      .update({ 
        statusDetail: 'Generating enhanced market validation...'
      })
      .eq('id', validationId);

    // 4. Generate enhanced market validation
    let marketValidation;
    
    switch (aiSettings.provider) {
      case 'openai': {
        const openai = new OpenAI({ apiKey: Deno.env.get('OPENAI_API_KEY') });
        const completion = await openai.chat.completions.create({
          model: aiSettings.openai_model ?? 'gpt-4o-mini',
          messages: [
            { role: "system", content: enhancedPrompts.marketValidation },
            { role: "user", content: projectConceptInput }
          ],
          response_format: { type: "json_object" },
          temperature: 0.7,
          max_tokens: 4000
        });
        
        marketValidation = JSON.parse(completion.choices[0].message.content || '{}');
        break;
      }
      
      case 'anthropic': {
        const anthropic = new Anthropic({ apiKey: Deno.env.get('ANTHROPIC_API_KEY') ?? '' });
        const message = await anthropic.messages.create({
          model: aiSettings.anthropic_model ?? 'claude-3-5-sonnet-20240620',
          system: enhancedPrompts.marketValidation,
          messages: [{ role: 'user', content: projectConceptInput }],
          max_tokens: 4000
        });
        
        const content = message.content[0].text.replace(/```json\n|\n```/g, '');
        marketValidation = JSON.parse(content);
        break;
      }
    }

    // 5. Store enhanced results using new database functions
    const enhancedResult = {
      projectConcept,
      marketValidation,
      targetUsers: [], // Can be enhanced similarly
      realCitations: marketResearch.realCitations,
      marketResearchData: marketResearch.marketResearch,
      enhancedAt: new Date().toISOString()
    };

    // Store market research queries
    if (marketResearch.marketResearch) {
      const marketData = marketResearch.marketResearch;
      
      // Store market size research
      if (marketData.marketData && marketData.marketData.length > 0) {
        await supabase.rpc('store_market_research', {
          p_validation_id: validationId,
          p_query_type: 'market_size',
          p_search_query: `${idea} market size 2024 TAM SAM industry analysis`,
          p_search_results: marketData.marketData,
          p_api_provider: 'tavily'
        });
      }

      // Store competitor research
      if (marketData.competitorData && marketData.competitorData.length > 0) {
        await supabase.rpc('store_market_research', {
          p_validation_id: validationId,
          p_query_type: 'competitors',
          p_search_query: `${idea} competitors analysis comparison 2024`,
          p_search_results: marketData.competitorData,
          p_api_provider: 'tavily'
        });
      }

      // Store trends research
      if (marketData.trendsData && marketData.trendsData.length > 0) {
        await supabase.rpc('store_market_research', {
          p_validation_id: validationId,
          p_query_type: 'trends',
          p_search_query: `${idea} industry trends funding rounds investment 2024`,
          p_search_results: marketData.trendsData,
          p_api_provider: 'tavily'
        });
      }

      // Store scraped competitor data
      if (marketData.scrapedCompetitors && marketData.scrapedCompetitors.length > 0) {
        for (const competitor of marketData.scrapedCompetitors) {
          if (competitor.success && competitor.data) {
            try {
              await supabase.rpc('store_competitor_analysis', {
                p_validation_id: validationId,
                p_competitor_url: competitor.url || 'unknown',
                p_competitor_name: competitor.data.metadata?.title || 'Unknown Competitor',
                p_scraped_data: competitor.data,
                p_status: 'success'
              });
            } catch (dbError) {
              console.error('Failed to store competitor analysis:', dbError);
            }
          }
        }
      }
    }

    // Store verified citations
    if (marketResearch.realCitations && marketResearch.realCitations.length > 0) {
      for (const citation of marketResearch.realCitations) {
        await supabase.rpc('store_verified_citation', {
          p_validation_id: validationId,
          p_citation_type: 'market_research',
          p_url: citation.url,
          p_title: citation.title,
          p_source_name: citation.source,
          p_relevance_score: citation.relevance === 'high' ? 0.8 : 0.6,
          p_snippet: citation.snippet,
          p_published_date: citation.publishedDate
        });
      }
    }

    // Update main validation record
    await supabase
      .from('idea_validations')
      .update({
        projectConcept: enhancedResult.projectConcept,
        marketValidation: enhancedResult.marketValidation,
        real_citations: enhancedResult.realCitations,
        market_research_data: enhancedResult.marketResearchData,
        enhanced_at: enhancedResult.enhancedAt,
        research_sources: ['tavily', 'firecrawl'],
        status: 'completed',
        statusDetail: 'Enhanced validation completed with real market data'
      })
      .eq('id', validationId);

    return new Response(
      JSON.stringify({ 
        success: true, 
        message: 'Enhanced validation completed',
        enhancedResult 
      }),
      { headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
    );

  } catch (error) {
    console.error('Enhanced validation error:', error);
    
    return new Response(
      JSON.stringify({ 
        success: false, 
        error: error.message 
      }),
      { 
        status: 500,
        headers: { ...corsHeaders, 'Content-Type': 'application/json' }
      }
    );
  }
});