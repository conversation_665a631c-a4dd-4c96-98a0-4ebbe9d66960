// deno-lint-ignore-file no-explicit-any
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2.39.3'
import { OpenAI } from 'https://esm.sh/openai@4.24.1'
import { Anthropic } from 'https://esm.sh/@anthropic-ai/sdk@0.14.1'

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
}

interface EnhancedValidationRequest {
  idea: string;
  validationId: string;
}

// Safe JSON parsing function
function safeJsonParse(content: string) {
  try {
    return JSON.parse(content);
  } catch (error) {
    console.error('Initial JSON parse failed, attempting cleanup:', error);
    let cleanedContent = content
      .replace(/[\u0000-\u001F\u007F-\u009F]/g, '')
      .replace(/\\n/g, ' ')
      .replace(/\\"/g, '\\"')
      .replace(/"\s*:\s*"/g, '": "')
      .trim();
    
    if (cleanedContent.startsWith('```json')) {
      cleanedContent = cleanedContent.replace(/```json\n?/, '').replace(/\n?```$/, '');
    }
    
    try {
      return JSON.parse(cleanedContent);
    } catch (secondError) {
      console.error('Second JSON parse failed:', secondError);
      throw new Error('Unable to parse AI response as valid JSON');
    }
  }
}

// Tavily client for real-time web search
class TavilyClient {
  private apiKey: string;

  constructor(apiKey: string) {
    this.apiKey = apiKey;
  }

  async search(query: string, options: any = {}) {
    try {
      const response = await fetch('https://api.tavily.com/search', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${this.apiKey}`
        },
        body: JSON.stringify({
          query,
          search_depth: options.search_depth || 'advanced',
          include_answer: options.include_answer !== false,
          include_raw_content: options.include_raw_content || false,
          max_results: options.max_results || 5
        })
      });

      if (!response.ok) {
        console.error(`Tavily API error: ${response.status}`);
        return { results: [] };
      }

      return await response.json();
    } catch (error) {
      console.error('Tavily search failed:', error);
      return { results: [] };
    }
  }
}

// Firecrawl client for website scraping
class FirecrawlClient {
  private apiKey: string;
  private baseUrl = 'https://api.firecrawl.dev';

  constructor(apiKey: string) {
    this.apiKey = apiKey;
  }

  async scrapeUrl(url: string, options: any = {}) {
    try {
      const response = await fetch(`${this.baseUrl}/v1/scrape`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${this.apiKey}`
        },
        body: JSON.stringify({
          url,
          formats: options.formats || ['markdown'],
          extract: options.extract || {},
          actions: options.actions || [],
          timeout: options.timeout || 30000
        })
      });

      if (!response.ok) {
        throw new Error(`Firecrawl API error: ${response.status} ${response.statusText}`);
      }

      const contentType = response.headers.get('content-type');
      if (contentType && contentType.includes('application/json')) {
        return await response.json();
      } else {
        const textContent = await response.text();
        return {
          success: true,
          data: {
            markdown: textContent,
            metadata: {
              title: 'Scraped Content',
              description: 'Content scraped from ' + url
            }
          }
        };
      }
    } catch (error) {
      console.error(`Firecrawl scraping failed for ${url}:`, error);
      return {
        success: false,
        error: error.message,
        data: null
      };
    }
  }
}

// Fast market research (Tavily only, no Firecrawl)
async function fastMarketResearch(idea: string, tavilyClient: TavilyClient) {
  try {
    console.log('Starting fast market research for:', idea);

    // Parallel market research searches (no Firecrawl scraping)
    const searchPromises = [
      tavilyClient.search(`${idea} market size 2024 TAM SAM industry analysis`, { max_results: 3 }),
      tavilyClient.search(`${idea} competitors analysis comparison 2024`, { max_results: 3 }),
      tavilyClient.search(`${idea} industry trends funding rounds investment 2024`, { max_results: 3 })
    ];

    const [marketData, competitorData, trendsData] = await Promise.all(searchPromises);

    return {
      marketResearch: {
        marketData: marketData.results || [],
        competitorData: competitorData.results || [],
        trendsData: trendsData.results || [],
        scrapedCompetitors: [], // Skip Firecrawl for speed
        searchTimestamp: new Date().toISOString()
      },
      realCitations: [
        ...extractCitations(marketData.results || []),
        ...extractCitations(competitorData.results || []),
        ...extractCitations(trendsData.results || [])
      ]
    };

  } catch (error) {
    console.error('Fast market research failed:', error);
    return {
      marketResearch: null,
      realCitations: [],
      error: error.message
    };
  }
}

// AI section generation helper
async function generateAISection(aiSettings: any, sectionType: string, idea: string, prompt: string) {
  try {
    const fullPrompt = `${prompt}\n\nAnalyze the following project idea: ${idea}`;
    
    switch (aiSettings.provider) {
      case 'openai': {
        const openai = new OpenAI({ apiKey: Deno.env.get('OPENAI_API_KEY') });
        const completion = await openai.chat.completions.create({
          model: aiSettings.openai_model ?? 'gpt-4o-mini',
          messages: [
            { role: "system", content: "You are an expert analyst. Always respond with valid JSON. Escape all quotes in text content." },
            { role: "user", content: fullPrompt }
          ],
          response_format: { type: "json_object" },
          temperature: 0.7,
          max_tokens: 4000
        });
        
        return safeJsonParse(completion.choices[0].message.content || '{}');
      }
      
      case 'anthropic': {
        const anthropic = new Anthropic({ apiKey: Deno.env.get('ANTHROPIC_API_KEY') ?? '' });
        const message = await anthropic.messages.create({
          model: aiSettings.anthropic_model ?? 'claude-3-5-sonnet-20240620',
          system: "You are an expert analyst. Always respond with valid JSON. Escape all quotes in text content.",
          messages: [{ role: 'user', content: fullPrompt }],
          max_tokens: 4000
        });
        
        return safeJsonParse(message.content[0].text.replace(/```json\n|\n```/g, ''));
      }
      
      default:
        throw new Error('Unsupported AI provider');
    }
  } catch (error) {
    console.error(`${sectionType} generation failed:`, error);
    return null;
  }
}

// Enhanced market research function (with Firecrawl - optional)
async function enhancedMarketResearch(idea: string, tavilyClient: TavilyClient, firecrawlClient: FirecrawlClient) {
  try {
    console.log('Starting enhanced market research for:', idea);

    // Parallel market research searches
    const searchPromises = [
      tavilyClient.search(`${idea} market size 2024 TAM SAM industry analysis`, { max_results: 3 }),
      tavilyClient.search(`${idea} competitors analysis comparison 2024`, { max_results: 3 }),
      tavilyClient.search(`${idea} industry trends funding rounds investment 2024`, { max_results: 3 })
    ];

    const [marketData, competitorData, trendsData] = await Promise.all(searchPromises);

    // Extract competitor URLs
    const competitorUrls = extractCompetitorUrls(competitorData.results || []);
    console.log('Found competitor URLs:', competitorUrls);

    // Scrape top competitor websites (limit to 2 to avoid timeouts)
    const competitorScrapingPromises = competitorUrls.slice(0, 2).map(async url => {
      try {
        const result = await firecrawlClient.scrapeUrl(url, {
          formats: ['markdown'],
          timeout: 15000
        });
        return { url, success: result.success, data: result.data, error: result.error || null };
      } catch (error) {
        console.log(`Failed to scrape ${url}:`, error.message);
        return { url, success: false, error: error.message, data: null };
      }
    });

    const scrapedCompetitors = await Promise.all(competitorScrapingPromises);

    return {
      marketResearch: {
        marketData: marketData.results || [],
        competitorData: competitorData.results || [],
        trendsData: trendsData.results || [],
        scrapedCompetitors: scrapedCompetitors.filter(result => result.success && result.data),
        searchTimestamp: new Date().toISOString()
      },
      realCitations: [
        ...extractCitations(marketData.results || []),
        ...extractCitations(competitorData.results || []),
        ...extractCitations(trendsData.results || [])
      ]
    };

  } catch (error) {
    console.error('Enhanced market research failed:', error);
    return {
      marketResearch: null,
      realCitations: [],
      error: error.message
    };
  }
}

// Helper function to extract competitor URLs
function extractCompetitorUrls(searchResults: any[]): string[] {
  const urls: string[] = [];
  
  for (const result of searchResults) {
    if (result.url && 
        !result.url.includes('wikipedia.org') && 
        !result.url.includes('linkedin.com') &&
        !result.url.includes('crunchbase.com')) {
      urls.push(result.url);
    }
  }
  
  return urls.slice(0, 5);
}

// Helper function to extract citations
function extractCitations(searchResults: any[]): any[] {
  return searchResults.map(result => ({
    url: result.url,
    title: result.title,
    source: new URL(result.url).hostname.replace('www.', ''),
    relevance: 'high',
    publishedDate: result.published_date || new Date().toISOString().split('T')[0],
    snippet: result.content?.substring(0, 200) + '...' || 'No snippet available'
  }));
}

// Standard validation prompts (copied from validate-idea function)
const standardPrompts = {
  projectConcept: `You are a senior web and mobile technology expert with 30+ years of experience, specializing in creating innovative, scalable, and user-centric solutions. Your task is to analyze the provided project idea and generate a detailed project concept with the following sections:

1. Project Overview:
- Provide a concise, high-level description of the project concept (150-200 words).
- Highlight the problem being solved, the target audience, and the key value proposition.
- Mention any innovative technologies or unique approaches being leveraged.
- Consider geographical context and market assumptions if provided in the idea.

2. User Roles & Responsibilities:
- Define exactly 4 user roles for the project (e.g., Admin, Customer, Vendor, Manager).
- For each role, list 4 specific responsibilities relevant to their function in the system.
- Ensure responsibilities align with project goals and target audience needs.

3. Feature Categories and Details:
Organize features into the following categories with a reasonable number of features per category (3-8 features each):
- Core Features: Fundamental to the project's operation.
- Must-Have Features: Necessary for a minimum viable product (MVP).
- Should-Have Features: Enhances user experience but not critical for MVP.
- Nice-to-Have Features: Optional features that provide additional value.
- Future Features: Planned for later development phases.

For each feature, provide:
1. A brief description of the feature.
2. Three acceptance criteria defining when the feature is considered complete.
3. The specific user roles that can access/use this feature.
4. Platform availability using this format: platforms: {"web": true/false, "mobile": true/false}.

Important Requirements:
- Each feature MUST have exactly 3 acceptance criteria
- Each feature MUST specify which user roles can access it
- Use the exact format provided for specifying platform availability

Return ONLY a valid JSON object with this exact structure:
{
  "overview": "A detailed 150-200 word project description",
  "userRoles": [
    {
      "title": "Role title",
      "focus": "Role focus/purpose",
      "responsibilities": ["resp1", "resp2", "resp3", "resp4"]
    }
  ],
  "features": {
    "core": [
      {
        "description": "Feature description",
        "acceptance_criteria": ["criterion1", "criterion2", "criterion3"],
        "platforms": {"web": true, "mobile": true},
        "user_roles": ["Role1", "Role2"]
      }
    ],
    "mustHave": [],
    "shouldHave": [],
    "niceToHave": [],
    "future": []
  },
  "citations": [
    {
      "url": "https://example.com/article",
      "title": "Article title",
      "source": "Source name",
      "relevance": "Brief explanation of how this source supports the analysis",
      "date": "YYYY-MM-DD"
    }
  ]
}`,

  targetUsers: `You are a user experience researcher tasked with defining the core target users for a software project. 

1. Focus on Relevance: Ensure all personas represent users who are directly connected to the project's purpose and are likely to use the solution.
2. Include 3-4 Personas: Reflect diverse user types and behaviors that align with the idea.
3. Specific and Realistic Details: Make demographics and motivations relatable and actionable.
4. Identify Pain Points: Highlight challenges or unmet needs that the project aims to solve (2-3 per persona).
5. Define Goals: Clarify what each persona hopes to achieve by using the project (2-3 per persona).
6. Align with the Idea: Ensure personas reflect the key target audience for the idea and include actionable insights for design and development.

Additionally, provide citations:
- Include 3-5 relevant market research or demographic studies that support your user persona analysis
- Each citation must include URL, title, source, relevance to the user analysis, and publication date if available

Return the response as JSON in this format:
{
  "targetUsers": [
    {
      "description": "Brief description of the user type and their relationship to the project idea",
      "demographics": {
        "age": "Age range (e.g., '25-34')",
        "occupation": "Typical occupation or role relevant to the project",
        "technicalLevel": "beginner" | "intermediate" | "advanced"
      },
      "painPoints": [
        "specific pain point 1",
        "specific pain point 2",
        "specific pain point 3 (optional)"
      ],
      "goals": [
        "specific goal 1",
        "specific goal 2",
        "specific goal 3 (optional)"
      ]
    }
  ],
  "citations": [
    {
      "url": "https://example.com/research",
      "title": "Research title",
      "source": "Source name",
      "relevance": "Brief explanation of how this research supports the user analysis",
      "date": "YYYY-MM-DD"
    }
  ]
}`,

  marketValidation: `You are an experienced market analyst with expertise in validating new product and service ideas.

1. Clearly defines the TAM, SAM, and SOM with realistic market size estimates and numerical data when possible
2. Is data-driven and specific, avoiding generic statements
3. Considers both immediate market potential and long-term sustainability
4. Provides actionable insights for business strategy and product development
5. Evaluates market saturation and competitive pressure
6. Identifies unique opportunities and potential roadblocks

Additionally, provide citations:
- Include 3-5 relevant market reports or industry analyses that support your market validation
- Each citation must include URL, title, source, relevance to the market analysis, and publication date if available

Return the response in this JSON format:
{
  "marketSize": {
    "totalAddressableMarket": "Detailed assessment of the Total Addressable Market (TAM) size with specific numbers when possible",
    "serviceableAddressableMarket": "Analysis of the Serviceable Addressable Market (SAM) that the product could realistically target",
    "serviceableObtainableMarket": "Realistic projection of the Serviceable Obtainable Market (SOM) that the product could capture in 3-5 years"
  },
  "targetMarket": "Description of the specific market segments most suitable for this product",
  "competitors": [
    {
      "name": "Competitor name",
      "strengths": ["key strength 1", "key strength 2"],
      "weaknesses": ["key weakness 1", "key weakness 2"]
    }
  ],
  "uniqueValue": "Clear articulation of the unique value proposition and differentiators",
  "challenges": [
    "Market challenge 1",
    "Market challenge 2",
    "Market challenge 3"
  ],
  "opportunities": [
    "Market opportunity 1",
    "Market opportunity 2",
    "Market opportunity 3"
  ],
  "monetizationStrategies": [
    "Potential revenue stream 1",
    "Potential revenue stream 2",
    "Potential revenue stream 3"
  ],
  "marketTrends": [
    "Relevant trend 1",
    "Relevant trend 2",
    "Relevant trend 3"
  ],
  "citations": [
    {
      "url": "https://example.com/market-report",
      "title": "Report title",
      "source": "Source name",
      "relevance": "Brief explanation of how this report supports the market analysis",
      "date": "YYYY-MM-DD"
    }
  ]
}`
};

// Enhanced prompts that incorporate real web data
function createEnhancedPrompts(realData: any) {
  const enhancedProjectConcept = `${standardPrompts.projectConcept}

ENHANCED WITH REAL MARKET DATA:
Use the following REAL market research and competitor data to enhance your analysis:

Market Research: ${JSON.stringify(realData.marketResearch, null, 2)}
Real Citations: ${JSON.stringify(realData.realCitations, null, 2)}

Incorporate this real data into your analysis, especially in the overview and citations sections. Replace generic citations with the provided real citations where relevant.`;

  const enhancedTargetUsers = `${standardPrompts.targetUsers}

ENHANCED WITH REAL MARKET DATA:
Use the following REAL market research to validate and enhance your user personas:

Market Research: ${JSON.stringify(realData.marketResearch, null, 2)}
Real Citations: ${JSON.stringify(realData.realCitations, null, 2)}

Use this real data to support your persona development and replace generic citations with real ones.`;

  const enhancedMarketValidation = `${standardPrompts.marketValidation}

ENHANCED WITH REAL MARKET DATA:
Use the following REAL market research, competitor analysis, and current data:

Market Research: ${JSON.stringify(realData.marketResearch, null, 2)}
Competitor Data: ${JSON.stringify(realData.marketResearch?.scrapedCompetitors, null, 2)}
Real Citations: ${JSON.stringify(realData.realCitations, null, 2)}

Base your market validation on this REAL data instead of assumptions. Use actual market sizes, competitor insights, and trends from the provided data.`;

  return {
    projectConcept: enhancedProjectConcept,
    targetUsers: enhancedTargetUsers,
    marketValidation: enhancedMarketValidation
  };
}

Deno.serve(async (req) => {
  if (req.method === 'OPTIONS') {
    return new Response(null, { headers: corsHeaders });
  }

  try {
    const requestData = await req.json() as EnhancedValidationRequest;
    const { idea, validationId } = requestData;

    console.log('Enhanced validation request:', { idea: idea.substring(0, 50), validationId });

    if (!idea || !validationId) {
      return new Response(
        JSON.stringify({ error: 'Missing idea or validationId' }),
        { status: 400, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
      );
    }

    // Initialize clients
    const supabase = createClient(
      Deno.env.get('SUPABASE_URL') ?? '',
      Deno.env.get('SUPABASE_SERVICE_ROLE_KEY') ?? ''
    );

    const tavilyApiKey = Deno.env.get('TAVILY_API_KEY');
    const firecrawlApiKey = Deno.env.get('FIRECRAWL_API_KEY');
    
    if (!tavilyApiKey || !firecrawlApiKey) {
      throw new Error('TAVILY_API_KEY or FIRECRAWL_API_KEY not found');
    }

    const tavilyClient = new TavilyClient(tavilyApiKey);
    const firecrawlClient = new FirecrawlClient(firecrawlApiKey);

    // Update status to analyzing
    await supabase
      .from('idea_validations')
      .update({ 
        status: 'analyzing',
        statusDetail: 'Step 1/4: Conducting enhanced market research...'
      })
      .eq('id', validationId);

    // Get AI provider settings first
    const { data: aiSettings } = await supabase
      .from('ai_provider_settings')
      .select('*')
      .eq('is_active', true)
      .single();

    if (!aiSettings) {
      throw new Error('No active AI provider found');
    }

    // Step 1: Start market research and AI generation in parallel
    await supabase
      .from('idea_validations')
      .update({ statusDetail: 'Step 1/3: Starting parallel analysis - market research & AI generation...' })
      .eq('id', validationId);

    // Start market research (fast Tavily only, skip slow Firecrawl for now)
    const marketResearchPromise = fastMarketResearch(idea, tavilyClient);
    
    // Start AI generation with standard prompts in parallel (they'll be enhanced later)
    const aiGenerationPromises = Promise.all([
      generateAISection(aiSettings, 'projectConcept', idea, standardPrompts.projectConcept),
      generateAISection(aiSettings, 'targetUsers', idea, standardPrompts.targetUsers),
      generateAISection(aiSettings, 'marketValidation', idea, standardPrompts.marketValidation)
    ]);

    // Wait for both market research and initial AI generation
    const [marketResearch, [projectConcept, targetUsers, marketValidation]] = await Promise.all([
      marketResearchPromise,
      aiGenerationPromises
    ]);

    // Step 2: Enhance with real data if market research succeeded
    await supabase
      .from('idea_validations')
      .update({ statusDetail: 'Step 2/3: Enhancing analysis with real market data...' })
      .eq('id', validationId);

    // Create enhanced versions with real data
    let enhancedProjectConcept = projectConcept;
    let enhancedMarketValidation = marketValidation;

    if (marketResearch.realCitations.length > 0) {
      const enhancedPrompts = createEnhancedPrompts(marketResearch);
      
      // Enhance key sections with real data in parallel
      const enhancementPromises = Promise.all([
        generateAISection(aiSettings, 'projectConcept', idea, enhancedPrompts.projectConcept),
        generateAISection(aiSettings, 'marketValidation', idea, enhancedPrompts.marketValidation)
      ]);

      const [enhancedConcept, enhancedMarket] = await enhancementPromises;
      enhancedProjectConcept = enhancedConcept || projectConcept;
      enhancedMarketValidation = enhancedMarket || marketValidation;
    }

    // Step 3: Final consolidation and storage
    await supabase
      .from('idea_validations')
      .update({ statusDetail: 'Step 3/3: Finalizing enhanced validation...' })
      .eq('id', validationId);

    // Store enhanced results with all data
    await supabase
      .from('idea_validations')
      .update({
        projectConcept: enhancedProjectConcept,
        targetUsers: targetUsers?.targetUsers || [],
        userRoles: enhancedProjectConcept?.userRoles || [],
        marketValidation: enhancedMarketValidation,
        real_citations: marketResearch.realCitations,
        market_research_data: marketResearch.marketResearch,
        enhanced_at: new Date().toISOString(),
        research_sources: ['tavily'],
        status: 'completed',
        statusDetail: 'Enhanced validation completed with parallel processing and real market data'
      })
      .eq('id', validationId);

    return new Response(
      JSON.stringify({ 
        success: true, 
        message: 'Enhanced validation completed with parallel processing',
        projectConcept: enhancedProjectConcept,
        targetUsers: targetUsers?.targetUsers || [],
        userRoles: enhancedProjectConcept?.userRoles || [],
        marketValidation: enhancedMarketValidation,
        realCitations: marketResearch.realCitations,
        marketResearch: marketResearch.marketResearch
      }),
      { headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
    );

  } catch (error) {
    console.error('Enhanced validation error:', error);
    
    return new Response(
      JSON.stringify({ 
        success: false, 
        error: error.message || 'Unknown error occurred'
      }),
      { 
        status: 500,
        headers: { ...corsHeaders, 'Content-Type': 'application/json' }
      }
    );
  }
});