// deno-lint-ignore-file no-explicit-any
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2.39.3'
import { OpenAI } from 'https://esm.sh/openai@4.24.1'
import { Anthropic } from 'https://esm.sh/@anthropic-ai/sdk@0.14.1'

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
}

interface IdeaValidationRequest {
  idea: string;
  validationId: string;
}

// Structured prompts for different sections of the validation
const prompts = {
  projectConcept: `You are a senior web and mobile technology expert with 30+ years of experience, specializing in creating innovative, scalable, and user-centric solutions. Your task is to analyze the provided project idea and generate a detailed project concept with the following sections:

1. Project Overview:
- Provide a concise, high-level description of the project concept (150-200 words).
- Highlight the problem being solved, the target audience, and the key value proposition.
- Mention any innovative technologies or unique approaches being leveraged.
- Consider geographical context and market assumptions if provided in the idea.

2. User Roles & Responsibilities:
- Define exactly 4 user roles for the project (e.g., Ad<PERSON>, Customer, Vendor, Manager).
- For each role, list 4 specific responsibilities relevant to their function in the system.
- Ensure responsibilities align with project goals and target audience needs.

3. Feature Categories and Details:
Organize all features into the following categories, ensuring each category includes exactly 5 features:
- Core Features: Fundamental to the project's operation.
- Must-Have Features: Necessary for a minimum viable product (MVP).
- Should-Have Features: Enhances user experience but not critical for MVP.
- Nice-to-Have Features: Optional features that provide additional value.
- Future Features: Planned for later development phases.

For each feature, provide:
1. A brief description of the feature.
2. Three acceptance criteria defining when the feature is considered complete.
3. The specific user roles that can access/use this feature.
4. Platform availability using this format: platforms: {"web": true/false, "mobile": true/false}.

4. Citations:
- Provide 3-5 relevant citations that support your analysis
- Each citation must include:
  * URL (use real, verifiable sources)
  * Title of the source
  * Source name (e.g., "Gartner", "Forbes", "TechCrunch")
  * Brief explanation of relevance to the project
  * Publication date (if available)

Important Requirements:
- Each feature category MUST have EXACTLY 5 features
- Each feature MUST have exactly 3 acceptance criteria
- Each feature MUST specify which user roles can access it
- Use the exact format provided for specifying platform availability

Return ONLY a valid JSON object with this exact structure:
{
  "overview": "A detailed 150-200 word project description",
  "userRoles": [
    {
      "title": "Role title",
      "focus": "Role focus/purpose",
      "responsibilities": ["resp1", "resp2", "resp3", "resp4"]
    }
  ],
  "features": {
    "core": [
      {
        "description": "Feature description",
        "acceptance_criteria": ["criterion1", "criterion2", "criterion3"],
        "platforms": {"web": true, "mobile": true},
        "user_roles": ["Role1", "Role2"]
      }
    ],
    "mustHave": [],
    "shouldHave": [],
    "niceToHave": [],
    "future": []
  },
  "citations": [
    {
      "url": "https://example.com/article",
      "title": "Article title",
      "source": "Source name",
      "relevance": "Brief explanation of how this source supports the analysis",
      "date": "YYYY-MM-DD" // Optional
    }
  ]
}`,

  targetUsers: `You are a user experience researcher tasked with defining the core target users for a software project. 

1. Focus on Relevance: Ensure all personas represent users who are directly connected to the project's purpose and are likely to use the solution.
2. Include 3-4 Personas: Reflect diverse user types and behaviors that align with the idea.
3. Specific and Realistic Details: Make demographics and motivations relatable and actionable.
4. Identify Pain Points: Highlight challenges or unmet needs that the project aims to solve (2-3 per persona).
5. Define Goals: Clarify what each persona hopes to achieve by using the project (2-3 per persona).
6. Align with the Idea: Ensure personas reflect the key target audience for the idea and include actionable insights for design and development.

Additionally, provide citations:
- Include 3-5 relevant market research or demographic studies that support your user persona analysis
- Each citation must include URL, title, source, relevance to the user analysis, and publication date if available

Return the response as JSON in this format:
{
  "targetUsers": [
    {
      "description": "Brief description of the user type and their relationship to the project idea",
      "demographics": {
        "age": "Age range (e.g., '25-34')",
        "occupation": "Typical occupation or role relevant to the project",
        "technicalLevel": "beginner" | "intermediate" | "advanced"
      },
      "painPoints": [
        "specific pain point 1",
        "specific pain point 2",
        "specific pain point 3 (optional)"
      ],
      "goals": [
        "specific goal 1",
        "specific goal 2",
        "specific goal 3 (optional)"
      ]
    }
  ],
  "citations": [
    {
      "url": "https://example.com/research",
      "title": "Research title",
      "source": "Source name",
      "relevance": "Brief explanation of how this research supports the user analysis",
      "date": "YYYY-MM-DD" // Optional
    }
  ]
}`,

  marketValidation: `You are an experienced market analyst with expertise in validating new product and service ideas.

1. Clearly defines the TAM, SAM, and SOM with realistic market size estimates and numerical data when possible
2. Is data-driven and specific, avoiding generic statements
3. Considers both immediate market potential and long-term sustainability
4. Provides actionable insights for business strategy and product development
5. Evaluates market saturation and competitive pressure
6. Identifies unique opportunities and potential roadblocks

Additionally, provide citations:
- Include 3-5 relevant market reports or industry analyses that support your market validation
- Each citation must include URL, title, source, relevance to the market analysis, and publication date if available

Return the response in this JSON format:
{
  "marketSize": {
    "totalAddressableMarket": "Detailed assessment of the Total Addressable Market (TAM) size with specific numbers when possible",
    "serviceableAddressableMarket": "Analysis of the Serviceable Addressable Market (SAM) that the product could realistically target",
    "serviceableObtainableMarket": "Realistic projection of the Serviceable Obtainable Market (SOM) that the product could capture in 3-5 years"
  },
  "targetMarket": "Description of the specific market segments most suitable for this product",
  "competitors": [
    {
      "name": "Competitor name",
      "strengths": ["key strength 1", "key strength 2"],
      "weaknesses": ["key weakness 1", "key weakness 2"]
    }
  ],
  "uniqueValue": "Clear articulation of the unique value proposition and differentiators",
  "challenges": [
    "Market challenge 1",
    "Market challenge 2",
    "Market challenge 3"
  ],
  "opportunities": [
    "Market opportunity 1",
    "Market opportunity 2",
    "Market opportunity 3"
  ],
  "monetizationStrategies": [
    "Potential revenue stream 1",
    "Potential revenue stream 2",
    "Potential revenue stream 3"
  ],
  "marketTrends": [
    "Relevant trend 1",
    "Relevant trend 2",
    "Relevant trend 3"
  ],
  "citations": [
    {
      "url": "https://example.com/market-report",
      "title": "Report title",
      "source": "Source name",
      "relevance": "Brief explanation of how this report supports the market analysis",
      "date": "YYYY-MM-DD" // Optional
    }
  ]
}`
};

Deno.serve(async (req) => {
  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    return new Response(null, { headers: corsHeaders });
  }

  try {
    // Parse request data with error handling
    let requestData: IdeaValidationRequest;
    try {
      requestData = await req.json() as IdeaValidationRequest;
    } catch (parseError) {
      console.error('JSON parse error on request:', parseError);
      return new Response(
        JSON.stringify({ 
          success: false, 
          error: 'Invalid request format: ' + parseError.message 
        }),
        { 
          status: 400,
          headers: { ...corsHeaders, 'Content-Type': 'application/json' }
        }
      );
    }
    
    const { idea, validationId } = requestData;

    if (!idea || !validationId) {
      throw new Error('Missing required data: idea or validationId');
    }

    const supabase = createClient(
      Deno.env.get('SUPABASE_URL') ?? '',
      Deno.env.get('SUPABASE_SERVICE_ROLE_KEY') ?? ''
    );

    // Get active AI provider settings
    const { data: aiSettings, error: aiError } = await supabase
      .from('ai_provider_settings')
      .select('*')
      .eq('is_active', true)
      .maybeSingle(); // Use maybeSingle instead of single to handle no results

    if (aiError) {
      console.error('Failed to retrieve AI provider settings:', aiError);
      throw new Error('Failed to retrieve AI provider settings');
    }

    // If no settings are found, use default OpenAI settings
    const activeSettings = aiSettings || {
      provider: 'openai',
      openai_model: 'gpt-4o-mini',
      is_active: true
    };

    // Update status to analyzing
    const { error: statusUpdateError } = await supabase
      .from('idea_validations')
      .update({ 
        status: 'analyzing',
        statusDetail: 'Starting analysis process'
      })
      .eq('id', validationId);

    if (statusUpdateError) {
      console.error('Failed to update validation status:', statusUpdateError);
      throw new Error('Failed to update validation status');
    }

    // Initialize empty objects for data
    let projectConcept = {
      overview: "The system encountered an issue generating the project concept. Please try again.",
      userRoles: [],
      features: {
        core: [],
        mustHave: [],
        shouldHave: [],
        niceToHave: [],
        future: []
      }
    };
    
    let targetUsers = {
      targetUsers: []
    };
    
    let marketValidation = {
      marketSize: {
        totalAddressableMarket: "The system encountered an issue generating the TAM. Please try again.",
        serviceableAddressableMarket: "The system encountered an issue generating the SAM. Please try again.",
        serviceableObtainableMarket: "The system encountered an issue generating the SOM. Please try again."
      },
      targetMarket: "Unable to generate target market information.",
      competitors: [],
      uniqueValue: "Unable to generate unique value proposition.",
      challenges: [],
      opportunities: [],
      monetizationStrategies: [],
      marketTrends: []
    };

    // Process each section sequentially to improve reliability
    try {
      // Update database to show we're working on project concept
      await supabase
        .from('idea_validations')
        .update({
          statusDetail: 'Generating project concept...'
        })
        .eq('id', validationId);
      
      const conceptResult = await generateSection(activeSettings, 'projectConcept', idea);
      if (conceptResult) {
        projectConcept = conceptResult;
      }
    
      // Update database immediately with project concept data
      const { error: conceptUpdateError } = await supabase
        .from('idea_validations')
        .update({
          projectConcept: projectConcept,
          userRoles: projectConcept?.userRoles || [],
          statusDetail: 'Project concept generated'
        })
        .eq('id', validationId);
      
      if (conceptUpdateError) {
        console.error('Failed to update project concept:', conceptUpdateError);
        throw new Error(`Failed to update project concept: ${conceptUpdateError.message}`);
      }
    } catch (error) {
      console.error('Error generating project concept:', error);
      // Continue with other sections with default data
      await supabase
        .from('idea_validations')
        .update({
          projectConcept: projectConcept,
          userRoles: [],
          statusDetail: 'Project concept generation encountered an error'
        })
        .eq('id', validationId);
    }

    // Forced delay between API calls
    await new Promise(resolve => setTimeout(resolve, 1000));

    try {
      // Update database to show we're working on target users
      await supabase
        .from('idea_validations')
        .update({
          statusDetail: 'Generating target users...'
        })
        .eq('id', validationId);
        
      const usersResult = await generateSection(activeSettings, 'targetUsers', idea, projectConcept);
      if (usersResult && usersResult.targetUsers) {
        targetUsers = usersResult;
      }
      
      // Update database with target users
      const { error: usersUpdateError } = await supabase
        .from('idea_validations')
        .update({
          targetUsers: targetUsers?.targetUsers || [],
          statusDetail: 'Target users generated'
        })
        .eq('id', validationId);
      
      if (usersUpdateError) {
        console.error('Failed to update target users:', usersUpdateError);
        throw new Error(`Failed to update target users: ${usersUpdateError.message}`);
      }
    } catch (error) {
      console.error('Error generating target users:', error);
      // Continue with market validation with default data
      await supabase
        .from('idea_validations')
        .update({
          targetUsers: [],
          statusDetail: 'Target users generation encountered an error'
        })
        .eq('id', validationId);
    }

    // Forced delay between API calls
    await new Promise(resolve => setTimeout(resolve, 1000));

    try {
      // Update database to show we're working on market validation
      await supabase
        .from('idea_validations')
        .update({
          statusDetail: 'Generating market validation...'
        })
        .eq('id', validationId);
        
      const validationResult = await generateSection(activeSettings, 'marketValidation', idea, projectConcept);
      if (validationResult) {
        marketValidation = validationResult;
      }
      
      // Update database with market validation
      const { error: marketUpdateError } = await supabase
        .from('idea_validations')
        .update({
          marketValidation: marketValidation,
          statusDetail: 'Market validation generated'
        })
        .eq('id', validationId);
      
      if (marketUpdateError) {
        console.error('Failed to update market validation:', marketUpdateError);
        throw new Error(`Failed to update market validation: ${marketUpdateError.message}`);
      }
    } catch (error) {
      console.error('Error generating market validation:', error);
      // Continue with default data
      await supabase
        .from('idea_validations')
        .update({
          marketValidation: marketValidation,
          statusDetail: 'Market validation generation encountered an error'
        })
        .eq('id', validationId);
    }

    // Final update with all results
    const { error: updateError } = await supabase
      .from('idea_validations')
      .update({
        status: 'completed',
        statusDetail: 'Analysis completed successfully',
        projectConcept: projectConcept,
        targetUsers: targetUsers?.targetUsers || [],
        userRoles: projectConcept?.userRoles || [],
        marketValidation: marketValidation
      })
      .eq('id', validationId);

    if (updateError) {
      console.error('Failed to save final validation results:', updateError);
      throw new Error(`Failed to save final validation results: ${updateError.message}`);
    }

    // Return a valid, properly formatted JSON response
    return new Response(
      JSON.stringify({ 
        success: true, 
        analysis: {
          projectConcept,
          targetUsers: targetUsers?.targetUsers || [],
          userRoles: projectConcept?.userRoles || [],
          marketValidation
        }
      }),
      { headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
    );
  } catch (error) {
    console.error('Error processing idea validation:', error);
    
    // Try to update the validation status to error if possible
    try {
      // We need to re-parse the request since we're in the catch block
      let validationId: string | undefined;
      try {
        const requestData = await req.clone().json() as IdeaValidationRequest;
        validationId = requestData?.validationId;
      } catch (parseError) {
        console.error('Failed to re-parse request in error handler:', parseError);
        // Continue with error response
      }
      
      if (validationId) {
        const supabase = createClient(
          Deno.env.get('SUPABASE_URL') ?? '',
          Deno.env.get('SUPABASE_SERVICE_ROLE_KEY') ?? ''
        );
        
        await supabase
          .from('idea_validations')
          .update({ 
            status: 'error',
            statusDetail: `Error: ${error.message || 'Unknown error'}` 
          })
          .eq('id', validationId);
      } else {
        console.error('No validationId available for error status update');
      }
    } catch (updateError) {
      console.error('Unable to update validation status:', updateError);
      // Unable to update validation status
    }
    
    // Always return a well-formed JSON response
    return new Response(
      JSON.stringify({ 
        success: false, 
        error: error.message || 'Unknown error occurred' 
      }),
      { 
        status: 500,
        headers: { ...corsHeaders, 'Content-Type': 'application/json' }
      }
    );
  }
});

// Function to generate each section of the validation
async function generateSection(aiSettings: any, section: string, idea: string, projectConcept: any = null): Promise<any> {
  let prompt = prompts[section];
  
  // For target users, include project concept if available
  if (section === 'targetUsers' && projectConcept) {
    prompt += `\n\nAnalyze the following inputs to create personas:
- Project Overview: ${projectConcept.overview}
- User Roles: ${JSON.stringify(projectConcept.userRoles, null, 2)}`;
  }
  
  // For market validation, include project overview if available
  if (section === 'marketValidation' && projectConcept) {
    prompt += `\n\nAnalyze the following project idea with this additional context:
- Project Overview: ${projectConcept.overview}`;
  }
  
  // Add the idea at the end of the prompt
  prompt += `\n\nAnalyze the following project idea: ${idea}`;
  
  try {
    let result = null;
    let attempts = 0;
    const maxAttempts = 3;
    
    while (attempts < maxAttempts && result === null) {
      attempts++;
      console.log(`Attempt ${attempts} for ${section} generation`);
      
      try {
        switch (aiSettings.provider) {
          case 'openai': {
            const openaiApiKey = Deno.env.get('OPENAI_API_KEY');
            if (!openaiApiKey) {
              throw new Error('OpenAI API key is missing or invalid');
            }
            
            const openai = new OpenAI({ apiKey: openaiApiKey });
            
            // Use the model from settings, with fallback to a default
            const model = aiSettings.openai_model ?? 'gpt-4o-mini';
            console.log(`Using OpenAI model: ${model}`);
            
            const completion = await openai.chat.completions.create({
              model,
              messages: [
                { role: "system", content: prompt },
                { role: "user", content: idea }
              ],
              response_format: { type: "json_object" },
              temperature: 0.7,
              max_tokens: 4000
            });
            
            const content = completion.choices[0].message.content;
            if (!content) {
              throw new Error('Empty response from OpenAI');
            }
            
            try {
              result = JSON.parse(content);
              console.log(`Successfully parsed ${section} JSON response`);
            } catch (parseError) {
              console.error('JSON parse error:', parseError);
              throw new Error(`Invalid JSON response for ${section}`);
            }
          }
          break;
          
          case 'anthropic': {
            const anthropicApiKey = Deno.env.get('ANTHROPIC_API_KEY');
            if (!anthropicApiKey) {
              throw new Error('Anthropic API key is missing or invalid');
            }
            
            const anthropic = new Anthropic({ apiKey: anthropicApiKey });
            // Use model from settings, with fallback
            const model = aiSettings.anthropic_model ?? 'claude-3-5-haiku-latest';
            console.log(`Using Anthropic model: ${model}`);
            
            const message = await anthropic.messages.create({
              model,
              max_tokens: 4096,
              system: prompt,
              messages: [{ role: 'user', content: idea }]
            });
            
            const content = message.content[0].text;
            if (!content) {
              throw new Error('Empty response from Anthropic');
            }
            
            try {
              // Strip any markdown code block syntax if present
              const cleanedContent = content.replace(/```json\n|\n```/g, '');
              result = JSON.parse(cleanedContent);
              console.log(`Successfully parsed ${section} JSON response`);
            } catch (parseError) {
              console.error('JSON parse error:', parseError);
              throw new Error(`Invalid JSON response for ${section}`);
            }
          }
          break;

          case 'deepseek': {
            const deepseekApiKey = Deno.env.get('DEEPSEEK_API_KEY');
            if (!deepseekApiKey) {
              throw new Error('DeepSeek API key is missing or invalid');
            }
            
            // Use model from settings, with fallback
            const model = aiSettings.deepseek_model ?? 'deepseek-chat';
            console.log(`Using DeepSeek model: ${model}`);
            
            // DeepSeek uses a REST API directly since they don't have an official SDK
            try {
              const response = await fetch('https://api.deepseek.com/v1/chat/completions', {
                method: 'POST',
                headers: {
                  'Authorization': `Bearer ${deepseekApiKey}`,
                  'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                  model,
                  messages: [
                    { role: "system", content: prompt },
                    { role: "user", content: idea }
                  ],
                  response_format: { type: "json_object" },
                  temperature: 0.7,
                  max_tokens: 4000
                })
              });
              
              if (!response.ok) {
                let errorMessage = `DeepSeek API error: ${response.status}`;
                try {
                  const errorData = await response.json();
                  errorMessage += ` - ${JSON.stringify(errorData)}`;
                } catch (parseError) {
                  const errorText = await response.text();
                  errorMessage += ` - ${errorText}`;
                }
                throw new Error(errorMessage);
              }
              
              const data = await response.json();
              
              if (!data || !data.choices || !data.choices[0] || !data.choices[0].message) {
                throw new Error(`Unexpected response format from DeepSeek: ${JSON.stringify(data)}`);
              }
              
              const content = data.choices[0].message.content;
              if (!content) {
                throw new Error('Empty response from DeepSeek');
              }
              
              try {
                // Strip any markdown code block syntax if present
                const cleanedContent = content.replace(/```json\n|\n```/g, '');
                result = JSON.parse(cleanedContent);
                console.log(`Successfully parsed ${section} JSON response`);
              } catch (parseError) {
                console.error('JSON parse error:', parseError);
                throw new Error(`Invalid JSON response for ${section}`);
              }
            } catch (fetchError) {
              console.error('Fetch error with DeepSeek API:', fetchError);
              throw fetchError;
            }
          }
          break;

          default:
            throw new Error('No active AI provider configured');
        }
      } catch (error) {
        console.error(`Error in attempt ${attempts} for ${section}:`, error);
        // Add delay between retries
        if (attempts < maxAttempts) {
          console.log(`Retrying ${section} generation after delay...`);
          await new Promise(resolve => setTimeout(resolve, 2000));
        } else {
          throw error; // Re-throw the error after all attempts failed
        }
      }
    }
    
    return result;
  } catch (error) {
    console.error(`Final error in ${section} generation:`, error);
    return null;
  }
}
