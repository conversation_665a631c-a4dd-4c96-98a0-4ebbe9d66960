
// deno-lint-ignore-file no-explicit-any
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2.39.3'
import { OpenAI } from 'https://esm.sh/openai@4.24.1'
import { Anthropic } from 'https://esm.sh/@anthropic-ai/sdk@0.14.1'

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
}

interface RoadmapGenerationRequest {
  validationId: string;
  phase: string;
}

// Improved prompt for roadmap generation with consistent data structure
const ROADMAP_GENERATION_PROMPT = `You are a seasoned software project manager and strategist. Your task is to create a detailed project development roadmap based on the provided project phases, features, and available resources. The roadmap must prioritize relevance, accuracy, and alignment with the project idea, ensuring clear deliverables, milestones, and risks for each phase.

Output Format:

Return ONLY a JSON object in this exact format, with no additional text:

{
  "phases": [
    {
      "name": "Phase Name (e.g., Interactive Prototype, POC, MVP, Production)",
      "duration": "e.g., '4 weeks'",
      "hoursPerResource": 160,
      "cost": "$10,000",
      "teamComposition": [
        {
          "role": "Role Name (e.g., QA, Developer, Tech Lead)",
          "count": 1,
          "skills": ["Skill 1", "Skill 2"],
          "hourlyRate": 25,
          "hours": 40
        }
      ],
      "deliverables": [
        "Deliverable 1",
        "Deliverable 2"
      ],
      "milestones": [
        "Milestone 1",
        "Milestone 2"
      ],
      "risks": [
        {
          "description": "Risk description",
          "mitigation": "Mitigation strategy"
        }
      ]
    }
  ]
}

Note that we've standardized the field names to use camelCase for consistency: 'teamComposition' instead of 'team', 'hourlyRate' instead of 'hourly_rate'. This ensures consistent field naming across the application.

Updated Guidelines:
1. Phase Names and Features:
• Use the phases defined in the development_phases column of the project_planning table:
• "interactive" → Interactive Prototype
• "poc" → Proof of Concept (POC)
• "mvp" → Minimum Viable Product
• "production" → Production Ready
• Interactive Prototype Phase: Include all feature categories (Core, Must-Have, Should-Have). Assign only one role, Prototype Specialist, with the timeline and resource count derived from the idea and features.
• For other phases, prioritize features based on the phase (e.g., Core for POC, Must-Have for MVP, advanced features for Production).

2. Roles and Skills:
• Roles must be relevant to the platforms attached to each feature in the idea_validations table (e.g., web, mobile, backend).
• Ensure skills match the roles and project requirements.

3. Accurate Durations:
• Derive phase durations based on project scope, complexity, and prioritized features.

4. Deliverables:
• Derive deliverables directly from the prioritized features for each phase. For Interactive Prototype, include deliverables from all feature categories.

5. Milestones:
• Generate milestones based on the deliverables for each phase, ensuring they are measurable and relevant.

6. Risks and Mitigation:
• Identify risks specific to the project idea, prioritized features, deliverables, and milestones.
• Provide actionable mitigation strategies for each risk.

7. Resource Allocation and Costs:
• Use hourly rates based on roles and align team composition with the platforms selected for the project:
• Junior Roles (e.g., QA, Junior Developers): $15–20/hour
• Mid-Level Roles (e.g., Developers, Designers): $20–30/hour
• Senior Roles (e.g., Tech Lead, Project Manager): $25–35/hour
• For Interactive Prototype, assign a single role (Prototype Specialist) and calculate costs based on resources and duration.
• For all team members, include both hourlyRate and hours fields.

8. Considerations:
• Account for platform-specific roles (e.g., Android/iOS developers for mobile, web developers for web).
• Ensure feasibility and scalability, especially for later phases like Production.
• Balance resources, team size, and costs to meet deadlines efficiently.`;

Deno.serve(async (req) => {
  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    return new Response(null, { headers: corsHeaders });
  }

  try {
    // Get request data
    const requestData = await req.json() as RoadmapGenerationRequest;
    const { validationId, phase } = requestData;

    if (!validationId || !phase) {
      return new Response(
        JSON.stringify({ error: 'Missing validationId or phase' }),
        { status: 400, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
      );
    }

    // Initialize Supabase client
    const supabase = createClient(
      Deno.env.get('SUPABASE_URL') ?? '',
      Deno.env.get('SUPABASE_SERVICE_ROLE_KEY') ?? ''
    );

    // Get active AI provider settings
    const { data: aiSettings, error: aiError } = await supabase
      .from('ai_provider_settings')
      .select('*')
      .eq('is_active', true)
      .single();

    if (aiError) {
      throw new Error('Failed to retrieve AI provider settings');
    }

    // Get validation data to generate a relevant roadmap
    const { data: validation, error: validationError } = await supabase
      .from('idea_validations')
      .select('originalIdea, projectConcept, targetUsers, marketValidation, userRoles')
      .eq('id', validationId)
      .single();

    if (validationError) {
      throw new Error('Failed to retrieve validation data');
    }

    // Get planning data if available
    const { data: planning, error: planningError } = await supabase
      .from('project_planning')
      .select('platforms, development_phases, technology_stack, branding_preferences, timeline, budget_range, monthly_users')
      .eq('validation_id', validationId)
      .maybeSingle();

    // Generate input context for AI based on validation data and phase
    let inputContext = `Project Idea: ${validation.originalIdea}\n\n`;
    
    if (validation.projectConcept) {
      inputContext += `Project Overview: ${validation.projectConcept.overview}\n\n`;
      
      if (validation.projectConcept.features) {
        inputContext += "Features:\n";
        
        // Core features
        if (validation.projectConcept.features.core && validation.projectConcept.features.core.length > 0) {
          inputContext += "Core Features:\n";
          validation.projectConcept.features.core.forEach((feature: any, index: number) => {
            inputContext += `${index + 1}. ${feature.description}\n`;
            inputContext += `   Platforms: ${JSON.stringify(feature.platforms)}\n`;
            inputContext += `   User Roles: ${feature.user_roles.join(', ')}\n`;
            inputContext += `   Acceptance Criteria: ${feature.acceptance_criteria.join(', ')}\n`;
          });
          inputContext += "\n";
        }
        
        // Must-have features
        if (validation.projectConcept.features.mustHave && validation.projectConcept.features.mustHave.length > 0) {
          inputContext += "Must-Have Features:\n";
          validation.projectConcept.features.mustHave.forEach((feature: any, index: number) => {
            inputContext += `${index + 1}. ${feature.description}\n`;
            inputContext += `   Platforms: ${JSON.stringify(feature.platforms)}\n`;
            inputContext += `   User Roles: ${feature.user_roles.join(', ')}\n`;
            inputContext += `   Acceptance Criteria: ${feature.acceptance_criteria.join(', ')}\n`;
          });
          inputContext += "\n";
        }
        
        // Should-have features
        if (validation.projectConcept.features.shouldHave && validation.projectConcept.features.shouldHave.length > 0) {
          inputContext += "Should-Have Features:\n";
          validation.projectConcept.features.shouldHave.forEach((feature: any, index: number) => {
            inputContext += `${index + 1}. ${feature.description}\n`;
            inputContext += `   Platforms: ${JSON.stringify(feature.platforms)}\n`;
            inputContext += `   User Roles: ${feature.user_roles.join(', ')}\n`;
            inputContext += `   Acceptance Criteria: ${feature.acceptance_criteria.join(', ')}\n`;
          });
          inputContext += "\n";
        }
      }
      
      if (validation.userRoles && validation.userRoles.length > 0) {
        inputContext += "User Roles:\n";
        validation.userRoles.forEach((role: any, index: number) => {
          inputContext += `${index + 1}. ${role.title} - ${role.focus}\n`;
          inputContext += `   Responsibilities: ${role.responsibilities.join(', ')}\n`;
        });
        inputContext += "\n";
      }
    }
    
    // Add planning data if available
    if (planning) {
      if (planning.platforms && planning.platforms.length > 0) {
        inputContext += `Platforms: ${planning.platforms.join(', ')}\n`;
      }
      
      if (planning.technology_stack && planning.technology_stack.length > 0) {
        inputContext += `Technology Stack: ${planning.technology_stack.join(', ')}\n`;
      }
      
      if (planning.timeline) {
        inputContext += `Timeline: ${planning.timeline}\n`;
      }
      
      if (planning.budget_range) {
        inputContext += `Budget Range: ${planning.budget_range}\n`;
      }
      
      if (planning.monthly_users) {
        inputContext += `Expected Monthly Users: ${planning.monthly_users}\n`;
      }
    }
    
    // Add phase-specific instructions
    inputContext += `\nGenerate a roadmap for the "${phase}" phase, considering the project requirements above.`;
    
    // Generate roadmap using the selected AI provider
    let roadmapData;
    
    try {
      switch (aiSettings.provider) {
        case 'openai': {
          const openai = new OpenAI({ apiKey: Deno.env.get('OPENAI_API_KEY') });
          
          const completion = await openai.chat.completions.create({
            model: aiSettings.openai_model ?? 'gpt-4o-mini',
            messages: [
              { role: "system", content: ROADMAP_GENERATION_PROMPT },
              { role: "user", content: inputContext }
            ],
            response_format: { type: "json_object" },
            temperature: 0.7,
            max_tokens: 4000
          });
          
          const content = completion.choices[0].message.content;
          if (!content) {
            throw new Error('Empty response from OpenAI');
          }
          
          roadmapData = JSON.parse(content);
          console.log("OpenAI response:", JSON.stringify(roadmapData, null, 2));
        }
        break;
        
        case 'anthropic': {
          const anthropic = new Anthropic({ 
            apiKey: Deno.env.get('ANTHROPIC_API_KEY') ?? '' 
          });
          
          const message = await anthropic.messages.create({
            model: aiSettings.anthropic_model ?? 'claude-3-5-sonnet-20240620',
            system: ROADMAP_GENERATION_PROMPT,
            messages: [{ role: 'user', content: inputContext }],
            max_tokens: 4000
          });
          
          const content = message.content[0].text;
          if (!content) {
            throw new Error('Empty response from Anthropic');
          }
          
          // Strip any markdown code block syntax if present
          const cleanedContent = content.replace(/```json\n|\n```/g, '');
          roadmapData = JSON.parse(cleanedContent);
          console.log("Anthropic response:", JSON.stringify(roadmapData, null, 2));
        }
        break;
        
        default:
          throw new Error('Unsupported AI provider');
      }
      
      // Create full roadmap content structure
      const roadmapContent = {
        name: `${phase.charAt(0).toUpperCase() + phase.slice(1)} Phase Roadmap`,
        phase: phase,
        phases: roadmapData.phases || []
      };
      
      // Calculate summary information
      let totalCost = 0;
      let totalDurationInWeeks = 0;
      let keyRisks: string[] = [];
      let recommendations: string[] = [];
      
      if (roadmapData.phases && roadmapData.phases.length > 0) {
        // Sum up costs
        totalCost = roadmapData.phases.reduce((sum: number, phase: any) => {
          // Check if cost is a string with $ and convert to number
          let phaseCost = phase.cost;
          if (typeof phaseCost === 'string') {
            phaseCost = parseFloat(phaseCost.replace(/[^0-9.-]+/g, ""));
          }
          return sum + (isNaN(phaseCost) ? 0 : phaseCost);
        }, 0);
        
        // Get duration from all phases and convert to weeks
        roadmapData.phases.forEach((p: any) => {
          if (p.duration) {
            const durationStr = p.duration.toLowerCase();
            if (durationStr.includes('week')) {
              const weeks = parseInt(durationStr.match(/\d+/)[0] || '0');
              totalDurationInWeeks += weeks;
            } else if (durationStr.includes('month')) {
              const months = parseInt(durationStr.match(/\d+/)[0] || '0');
              totalDurationInWeeks += months * 4; // Rough estimate: 1 month = 4 weeks
            } else if (durationStr.includes('day')) {
              const days = parseInt(durationStr.match(/\d+/)[0] || '0');
              totalDurationInWeeks += Math.ceil(days / 7); // Convert days to weeks
            }
          }
        });
        
        // Format the total duration
        const totalDurationString = `${totalDurationInWeeks} weeks`;
        
        // Extract risks and convert to simple strings
        keyRisks = roadmapData.phases.flatMap((phase: any) => 
          phase.risks ? phase.risks.map((risk: any) => risk.description) : []
        );
        
        // Generate recommendations based on the phases and risks
        const uniqueRisks = [...new Set(keyRisks)];
        recommendations = [
          "Start with a smaller MVP scope to reduce initial complexity",
          "Implement continuous integration early to catch integration issues",
          "Allocate adequate time for testing and bug fixes",
          "Conduct regular progress reviews to ensure timeline adherence"
        ];
        
        // Add risk-specific recommendations
        if (uniqueRisks.some(risk => risk.toLowerCase().includes('resource') || risk.toLowerCase().includes('talent'))) {
          recommendations.push("Consider hiring contractors for specialized skills to reduce recruitment delays");
        }
        
        if (uniqueRisks.some(risk => risk.toLowerCase().includes('scope') || risk.toLowerCase().includes('requirement'))) {
          recommendations.push("Implement agile methodologies with regular stakeholder reviews to manage scope creep");
        }
        
        if (uniqueRisks.some(risk => risk.toLowerCase().includes('technical') || risk.toLowerCase().includes('integration'))) {
          recommendations.push("Create proof of concepts for high-risk technical components early in the process");
        }
      
        // Add summary information
        roadmapContent.summary = {
          totalCost: parseFloat(totalCost.toFixed(2)),
          totalDuration: totalDurationString || "Not specified",
          keyRisks: keyRisks.slice(0, 5), // Limit to top 5 risks
          recommendations: recommendations.slice(0, 6) // Limit to 6 recommendations
        };
      }
      
      // Get user ID from the validation for database insertion
      const { data: userData, error: userError } = await supabase
        .from('idea_validations')
        .select('userId')
        .eq('id', validationId)
        .single();
        
      if (userError) {
        throw new Error('Failed to retrieve user ID');
      }
      
      // Check if a roadmap already exists for this validation and phase
      const { data: existingRoadmap, error: checkError } = await supabase
        .from('roadmaps')
        .select('id, status')
        .eq('validation_id', validationId)
        .eq('phase', phase)
        .maybeSingle();
        
      if (checkError) {
        console.error("Error checking for existing roadmap:", checkError);
      }
      
      // Insert or update the roadmap in the database
      let roadmapId;
      
      if (existingRoadmap) {
        // Update existing roadmap
        const { data, error } = await supabase
          .from('roadmaps')
          .update({
            content: roadmapContent,
            status: 'draft',
            updated_at: new Date()
          })
          .eq('id', existingRoadmap.id)
          .select('id')
          .single();
          
        if (error) {
          throw new Error(`Failed to update roadmap: ${error.message}`);
        }
        
        roadmapId = data.id;
      } else {
        // Create new roadmap
        const { data, error } = await supabase
          .from('roadmaps')
          .insert({
            validation_id: validationId,
            user_id: userData.userId,
            phase: phase,
            content: roadmapContent,
            status: 'draft',
            is_locked: phase !== 'interactive' // Interactive is unlocked by default
          })
          .select('id')
          .single();
          
        if (error) {
          throw new Error(`Failed to create roadmap: ${error.message}`);
        }
        
        roadmapId = data.id;
      }
      
      return new Response(
        JSON.stringify({ 
          success: true, 
          roadmapId: roadmapId,
          roadmapContent 
        }),
        { headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
      );
      
    } catch (aiError) {
      console.error("AI generation error:", aiError);
      // Return fallback content on AI error
      return new Response(
        JSON.stringify({ 
          success: false, 
          error: 'AI generation failed',
          message: aiError.message
        }),
        { 
          status: 500,
          headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
        }
      );
    }
    
  } catch (error) {
    console.error("General error:", error);
    return new Response(
      JSON.stringify({ error: error.message }),
      { 
        status: 500,
        headers: { ...corsHeaders, 'Content-Type': 'application/json' }
      }
    );
  }
});
