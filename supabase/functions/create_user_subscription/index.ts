
// deno-lint-ignore-file no-explicit-any
import { serve } from "https://deno.land/std@0.177.0/http/server.ts";
import { createClient } from "https://esm.sh/@supabase/supabase-js@2.32.0";

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
}

serve(async (req) => {
  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    return new Response(null, { headers: corsHeaders });
  }

  try {
    console.log("Function started", new Date().toISOString());
    
    // Create Supabase client with service role key for admin operations
    const supabaseUrl = Deno.env.get('SUPABASE_URL');
    const supabaseServiceKey = Deno.env.get('SUPABASE_SERVICE_ROLE_KEY');
    
    if (!supabaseUrl || !supabaseServiceKey) {
      console.error("Missing Supabase environment variables");
      throw new Error('Server configuration error');
    }

    // Get request data
    let requestData;
    try {
      requestData = await req.json();
      console.log("Request data:", JSON.stringify(requestData));
    } catch (e) {
      console.error("Error parsing request body:", e);
      throw new Error('Invalid request body');
    }
    
    // Extract JWT token from Authorization header
    const authHeader = req.headers.get('Authorization');
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      console.error("Invalid Authorization header format");
      throw new Error('Invalid Authorization header format');
    }
    
    const token = authHeader.replace('Bearer ', '');
    console.log("Auth token received, length:", token.length);

    // Create admin client
    const adminClient = createClient(supabaseUrl, supabaseServiceKey);
    
    // Verify the JWT token and get the user
    const { data: { user }, error: verifyError } = await adminClient.auth.getUser(token);
    
    if (verifyError || !user) {
      console.error("JWT verification failed:", verifyError);
      throw new Error('Invalid authentication token');
    }
    
    console.log(`User authenticated: ${user.id}`);

    // Get params from the request data
    const { plan_id, payment_processor, payment_id, pack_type, project_id } = requestData;

    if (!plan_id) {
      console.error("Missing required parameter: plan_id");
      throw new Error('Missing required parameter: plan_id');
    }

    // Create params object for the RPC call - matching the function signature
    const params: any = {
      user_id: user.id,
      plan_id,
      payment_processor: payment_processor || 'dummy',
      payment_id: payment_id || `dummy-${Date.now()}`
    };

    // If it's a roadmap-pack, include the pack_type
    if (pack_type) {
      params.pack_type = pack_type;
      console.log(`Using pack_type: ${pack_type}`);
    }
    
    // If project_id is provided, add it to the parameters
    if (project_id) {
      params.project_id = project_id;
      console.log(`Creating subscription for specific project: ${project_id}`);
    }

    console.log("Calling RPC with params:", JSON.stringify(params));

    // Call the RPC function using admin client
    const { data, error } = await adminClient.rpc(
      'create_user_subscription',
      params
    );

    if (error) {
      console.error("RPC error:", error);
      throw new Error(`Database error: ${error.message}`);
    }

    console.log("Subscription created successfully:", data);

    return new Response(
      JSON.stringify({ 
        success: true, 
        data 
      }),
      { 
        headers: { 
          ...corsHeaders,
          'Content-Type': 'application/json' 
        } 
      }
    );
  } catch (error) {
    console.error("Function error:", error.message);
    
    return new Response(
      JSON.stringify({ 
        success: false, 
        error: error.message 
      }),
      { 
        status: 400,
        headers: { 
          ...corsHeaders,
          'Content-Type': 'application/json' 
        } 
      }
    );
  }
});
