import { serve } from "https://deno.land/std@0.168.0/http/server.ts";
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2';

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
  'Access-Control-Allow-Methods': 'POST, OPTIONS',
};

interface PrototypeTask {
  id: string;
  prototype_id: string;
  title: string;
  description?: string;
  task_type: string;
  status: string;
  estimated_duration: number;
  order_index: number;
}

interface CodeTemplate {
  path: string;
  content: string;
  type: string;
}

serve(async (req) => {
  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders });
  }

  try {
    console.log('Edge function called with method:', req.method);

    let requestBody;
    try {
      requestBody = await req.json();
    } catch (parseError) {
      console.error('Failed to parse request body:', parseError);
      return new Response(
        JSON.stringify({ error: 'Invalid JSON in request body' }),
        {
          status: 400,
          headers: { ...corsHeaders, 'Content-Type': 'application/json' }
        }
      );
    }

    const { prototypeId } = requestBody;
    console.log('Processing prototype ID:', prototypeId);

    if (!prototypeId) {
      return new Response(
        JSON.stringify({ error: 'Prototype ID is required' }),
        {
          status: 400,
          headers: { ...corsHeaders, 'Content-Type': 'application/json' }
        }
      );
    }

    // Create Supabase client with proper error handling
    const supabaseUrl = Deno.env.get('SUPABASE_URL');
    const supabaseServiceKey = Deno.env.get('SUPABASE_SERVICE_ROLE_KEY');

    if (!supabaseUrl || !supabaseServiceKey) {
      console.error('Missing Supabase environment variables');
      throw new Error('Server configuration error');
    }

    const supabase = createClient(supabaseUrl, supabaseServiceKey);
    console.log('Supabase client created successfully');

    // Fetch prototype and its configuration
    const { data: prototype, error: prototypeError } = await supabase
      .from('prototypes')
      .select(`
        *,
        project_configurations (*)
      `)
      .eq('id', prototypeId)
      .single();

    if (prototypeError) throw prototypeError;

    // Fetch tasks for this prototype
    const { data: tasks, error: tasksError } = await supabase
      .from('prototype_tasks')
      .select('*')
      .eq('prototype_id', prototypeId)
      .order('order_index');

    if (tasksError) throw tasksError;

    // Update prototype status to generating
    await supabase
      .from('prototypes')
      .update({ status: 'generating' })
      .eq('id', prototypeId);

    // Start processing tasks asynchronously (don't wait for completion)
    processTasksAsync(supabase, tasks, prototype).catch(error => {
      console.error('Error in async task processing:', error);
      // Update prototype status to failed
      supabase
        .from('prototypes')
        .update({
          status: 'failed',
          error_message: error.message
        })
        .eq('id', prototypeId);
    });

    // Return immediately - tasks will process in background
    return new Response(
      JSON.stringify({
        success: true,
        message: 'Prototype generation started',
        prototypeId
      }),
      { headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
    );

  } catch (error) {
    console.error('Error in generate-prototype function:', error);
    return new Response(
      JSON.stringify({ 
        error: error.message,
        success: false 
      }),
      { 
        status: 400,
        headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
      }
    );
  }
});

async function processTasksAsync(supabase: any, tasks: PrototypeTask[], prototype: any) {
  console.log(`Starting async processing of ${tasks.length} tasks`);

  try {
    // Process tasks sequentially with delays to avoid overwhelming the system
    for (let i = 0; i < tasks.length; i++) {
      const task = tasks[i];
      console.log(`Processing task ${i + 1}/${tasks.length}: ${task.title}`);

      await processTask(supabase, task, prototype);

      // Add a small delay between tasks to prevent overwhelming the system
      if (i < tasks.length - 1) {
        await new Promise(resolve => setTimeout(resolve, 2000)); // 2 second delay
      }
    }

    // Update prototype status to completed
    await supabase
      .from('prototypes')
      .update({
        status: 'completed',
        completed_at: new Date().toISOString(),
        preview_url: `https://prototype-${prototype.id}.vercel.app`,
        github_repo_url: `https://github.com/generated/${prototype.name.toLowerCase().replace(/\s+/g, '-')}`
      })
      .eq('id', prototype.id);

    console.log('All tasks completed successfully');
  } catch (error) {
    console.error('Error in async task processing:', error);

    // Update prototype status to failed
    await supabase
      .from('prototypes')
      .update({
        status: 'failed',
        error_message: error.message
      })
      .eq('id', prototype.id);
  }
}

async function callClaudeAPI(prompt: string): Promise<string> {
  const claudeApiKey = Deno.env.get('ANTHROPIC_API_KEY');
  console.log('Anthropic API Key available:', claudeApiKey ? 'Yes' : 'No');
  console.log('Anthropic API Key length:', claudeApiKey?.length || 0);

  if (!claudeApiKey) {
    console.log('Anthropic API key not found in environment variables');
    throw new Error('Anthropic API key not found');
  }

  try {
    const response = await fetch('https://api.anthropic.com/v1/messages', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'x-api-key': claudeApiKey,
        'anthropic-version': '2023-06-01'
      },
      body: JSON.stringify({
        model: 'claude-3-sonnet-20240229',
        max_tokens: 4000,
        messages: [
          {
            role: 'user',
            content: prompt
          }
        ]
      })
    });

    if (!response.ok) {
      throw new Error(`Claude API error: ${response.status}`);
    }

    const data = await response.json();
    return data.content[0].text;
  } catch (error) {
    console.error('Claude API call failed:', error);
    return '// Claude API unavailable - using fallback template';
  }
}

async function processTask(supabase: any, task: PrototypeTask, prototype: any) {
  const startTime = new Date();
  console.log(`Starting task: ${task.title}`);

  try {
    // Update task status to in_progress
    await supabase
      .from('prototype_tasks')
      .update({
        status: 'in_progress',
        started_at: startTime.toISOString()
      })
      .eq('id', task.id);

    // Generate files using Claude AI (with timeout)
    const generatedFiles = await Promise.race([
      generateFilesWithClaude(task, prototype, supabase),
      new Promise((_, reject) =>
        setTimeout(() => reject(new Error('Task timeout')), 30000) // 30 second timeout per task
      )
    ]) as CodeTemplate[];

    // Store generated files
    if (generatedFiles.length > 0) {
      const filesToInsert = generatedFiles.map(file => ({
        prototype_id: task.prototype_id,
        task_id: task.id,
        file_path: file.path,
        file_content: file.content,
        file_type: file.type,
        template_used: 'claude-ai-generated'
      }));

      await supabase
        .from('prototype_files')
        .insert(filesToInsert);
    }

    // Calculate actual duration
    const endTime = new Date();
    const actualDuration = Math.round((endTime.getTime() - startTime.getTime()) / 1000);

    // Update task status to completed
    await supabase
      .from('prototype_tasks')
      .update({
        status: 'completed',
        completed_at: endTime.toISOString(),
        actual_duration: actualDuration,
        generated_files: generatedFiles.map(f => f.path)
      })
      .eq('id', task.id);

    console.log(`Task completed: ${task.title} (${actualDuration}s)`);

  } catch (error) {
    console.error(`Error processing task ${task.id}:`, error);

    // Update task status to failed
    await supabase
      .from('prototype_tasks')
      .update({
        status: 'failed',
        error_message: error.message,
        completed_at: new Date().toISOString()
      })
      .eq('id', task.id);
  }
}

function getRealisticProcessingTime(taskType: string): number {
  const timings = {
    'setup': 15000,      // 15 seconds
    'component': 25000,  // 25 seconds
    'page': 35000,       // 35 seconds
    'service': 20000,    // 20 seconds
    'integration': 30000, // 30 seconds
    'styling': 12000     // 12 seconds
  };
  return timings[taskType] || 20000;
}

async function generateFilesWithClaude(task: PrototypeTask, prototype: any, supabase: any): Promise<CodeTemplate[]> {
  const config = prototype.project_configurations[0];
  const appName = config.app_name;

  // Get validation data to understand the actual features
  const { data: validationData } = await supabase
    .from('idea_validations')
    .select('*')
    .eq('id', prototype.validation_id)
    .single();

  const features = validationData?.projectConcept?.features || {};
  const targetUsers = validationData?.targetUsers || [];
  const marketValidation = validationData?.marketValidation || {};

  const prompt = `You are building a real, interactive React + TypeScript prototype for: ${appName}

VALIDATION CONTEXT:
Project Concept: ${validationData?.projectConcept?.overview || ''}
Target Users: ${JSON.stringify(targetUsers.slice(0, 2))}
Market Opportunity: ${marketValidation?.opportunity || ''}

CURRENT TASK: ${task.title}
Description: ${task.description}
Task Type: ${task.task_type}

FEATURES TO IMPLEMENT:
Core Features: ${JSON.stringify(features.core || [])}
Must-Have Features: ${JSON.stringify(features.mustHave || [])}

DESIGN SYSTEM:
- Primary Color: ${config.colors.primary}
- Secondary Color: ${config.colors.secondary}
- Font Family: ${config.typography.fontFamily}

REQUIREMENTS:
1. Generate REAL, WORKING React + TypeScript code
2. Create interactive components with actual functionality
3. Include realistic mock data relevant to the business concept
4. Use modern React patterns (useState, useEffect, custom hooks)
5. Style with Tailwind CSS using the provided colors
6. Make it feel like a real application, not a demo
7. Include proper TypeScript interfaces
8. Add interactive elements (buttons, forms, lists, etc.)
9. Create realistic user flows and interactions

For ${task.task_type} tasks:
- If component: Create reusable, interactive components
- If page: Build complete pages with real functionality
- If service: Create mock APIs with realistic data
- If setup: Generate proper configuration files
- If styling: Create comprehensive theme system

Generate complete, production-ready code that brings this business idea to life.
Return only the code files, no explanations.`;

  try {
    const claudeResponse = await callClaudeAPI(prompt);

    // Parse Claude's response and create appropriate files
    const files = parseClaudeResponse(claudeResponse, task, config, validationData);
    return files;
  } catch (error) {
    console.error('Claude generation failed, using fallback:', error);
    return generateFallbackFiles(task, prototype);
  }
}

function parseClaudeResponse(response: string, task: PrototypeTask, config: any, validationData?: any): CodeTemplate[] {
  // Extract code blocks from Claude's response
  const codeBlocks = response.match(/```[\s\S]*?```/g) || [];
  const files: CodeTemplate[] = [];

  if (codeBlocks.length > 0) {
    codeBlocks.forEach((block, index) => {
      const code = block.replace(/```[\w]*\n?/, '').replace(/```$/, '');
      const fileName = generateSmartFileName(task, index, validationData);

      files.push({
        path: fileName,
        content: code,
        type: getFileType(fileName)
      });
    });
  } else {
    // If no code blocks, treat entire response as code
    const fileName = generateSmartFileName(task, 0, validationData);
    files.push({
      path: fileName,
      content: response,
      type: getFileType(fileName)
    });
  }

  return files;
}

function generateSmartFileName(task: PrototypeTask, index: number, validationData?: any): string {
  const taskName = task.title.toLowerCase().replace(/[^a-z0-9]/g, '-');
  const appContext = validationData?.projectConcept?.title?.toLowerCase().replace(/[^a-z0-9]/g, '-') || 'app';

  switch (task.task_type) {
    case 'setup':
      if (index === 0) return 'package.json';
      if (index === 1) return 'vite.config.ts';
      if (index === 2) return 'tailwind.config.js';
      return `${taskName}.config.ts`;

    case 'component':
      if (task.feature_id?.includes('core')) {
        return `src/components/${appContext}/${taskName.split('-').map(w => w.charAt(0).toUpperCase() + w.slice(1)).join('')}.tsx`;
      }
      return `src/components/${taskName.split('-').map(w => w.charAt(0).toUpperCase() + w.slice(1)).join('')}.tsx`;

    case 'page':
      const pageName = taskName.split('-').map(w => w.charAt(0).toUpperCase() + w.slice(1)).join('');
      return `src/pages/${pageName}.tsx`;

    case 'service':
      return `src/services/${appContext}Service.ts`;

    case 'styling':
      if (index === 0) return 'src/styles/globals.css';
      return `src/styles/${appContext}-theme.css`;

    case 'integration':
      if (index === 0) return `src/hooks/use${appContext.charAt(0).toUpperCase() + appContext.slice(1)}.ts`;
      return `src/utils/${appContext}Utils.ts`;

    default:
      return `src/generated/${taskName}.ts`;
  }
}

function generateFileName(task: PrototypeTask, index: number): string {
  const taskName = task.title.toLowerCase().replace(/[^a-z0-9]/g, '-');

  switch (task.task_type) {
    case 'setup':
      return index === 0 ? 'package.json' : `${taskName}.config.ts`;
    case 'component':
      return `src/components/${taskName.replace(/-/g, '')}/${taskName.split('-').map(w => w.charAt(0).toUpperCase() + w.slice(1)).join('')}.tsx`;
    case 'page':
      return `src/pages/${taskName.split('-').map(w => w.charAt(0).toUpperCase() + w.slice(1)).join('')}.tsx`;
    case 'service':
      return `src/services/${taskName}.ts`;
    case 'styling':
      return `src/styles/${taskName}.css`;
    default:
      return `src/generated/${taskName}.ts`;
  }
}

function getFileType(fileName: string): string {
  if (fileName.endsWith('.tsx')) return 'component';
  if (fileName.endsWith('.ts')) return 'service';
  if (fileName.endsWith('.css')) return 'style';
  if (fileName.includes('/pages/')) return 'page';
  if (fileName.includes('package.json')) return 'config';
  return 'type';
}

async function generateFallbackFiles(task: PrototypeTask, prototype: any): Promise<CodeTemplate[]> {
  const config = prototype.project_configurations[0];
  const files: CodeTemplate[] = [];

  switch (task.task_type) {
    case 'setup':
      if (task.title.includes('Project Structure')) {
        files.push(...generateProjectStructure(config));
      } else if (task.title.includes('Routing')) {
        files.push(...generateRouting(config));
      } else if (task.title.includes('Theme')) {
        files.push(...generateTheme(config));
      }
      break;

    case 'component':
      if (task.title.includes('Layout')) {
        files.push(...generateLayout(config));
      } else if (task.title.includes('User Profile')) {
        files.push(...generateUserProfile(config));
      } else if (task.title.includes('Chart')) {
        files.push(...generateChartComponents(config));
      }
      break;

    case 'page':
      if (task.title.includes('Authentication')) {
        files.push(...generateAuthPages(config));
      } else if (task.title.includes('Dashboard')) {
        files.push(...generateDashboard(config));
      }
      break;

    case 'service':
      if (task.title.includes('Mock API')) {
        files.push(...generateMockAPI(config));
      } else if (task.title.includes('Authentication')) {
        files.push(...generateAuthService(config));
      }
      break;

    default:
      // Generate a placeholder file
      files.push({
        path: `src/generated/${task.task_type}-${Date.now()}.ts`,
        content: `// Generated file for task: ${task.title}\n// TODO: Implement ${task.description}`,
        type: 'config'
      });
  }

  return files;
}

function generateProjectStructure(config: any): CodeTemplate[] {
  return [
    {
      path: 'package.json',
      content: JSON.stringify({
        name: config.app_name.toLowerCase().replace(/\s+/g, '-'),
        version: '0.1.0',
        type: 'module',
        scripts: {
          dev: 'vite',
          build: 'tsc && vite build',
          preview: 'vite preview'
        },
        dependencies: {
          react: '^18.2.0',
          'react-dom': '^18.2.0',
          'react-router-dom': '^6.8.0',
          '@radix-ui/react-slot': '^1.0.2',
          'class-variance-authority': '^0.7.0',
          clsx: '^2.0.0',
          'tailwind-merge': '^1.14.0'
        },
        devDependencies: {
          '@types/react': '^18.2.43',
          '@types/react-dom': '^18.2.17',
          '@vitejs/plugin-react': '^4.2.1',
          autoprefixer: '^10.4.16',
          postcss: '^8.4.32',
          tailwindcss: '^3.4.0',
          typescript: '^5.2.2',
          vite: '^5.0.8'
        }
      }, null, 2),
      type: 'config'
    },
    {
      path: 'vite.config.ts',
      content: `import { defineConfig } from 'vite'
import react from '@vitejs/plugin-react'
import path from 'path'

export default defineConfig({
  plugins: [react()],
  resolve: {
    alias: {
      '@': path.resolve(__dirname, './src'),
    },
  },
})`,
      type: 'config'
    },
    {
      path: 'tailwind.config.js',
      content: `/** @type {import('tailwindcss').Config} */
export default {
  content: [
    "./index.html",
    "./src/**/*.{js,ts,jsx,tsx}",
  ],
  theme: {
    extend: {
      colors: {
        primary: '${config.colors.primary}',
        secondary: '${config.colors.secondary}',
      },
      fontFamily: {
        sans: ['${config.typography.fontFamily}', 'sans-serif'],
        heading: ['${config.typography.headingFont}', 'sans-serif'],
      },
    },
  },
  plugins: [],
}`,
      type: 'config'
    }
  ];
}

function generateRouting(config: any): CodeTemplate[] {
  return [
    {
      path: 'src/App.tsx',
      content: `import React from 'react';
import { BrowserRouter as Router, Routes, Route } from 'react-router-dom';
import { ThemeProvider } from './contexts/ThemeContext';
import { AuthProvider } from './contexts/AuthContext';
import Layout from './components/Layout';
import Dashboard from './pages/Dashboard';
import Login from './pages/Login';
import './App.css';

function App() {
  return (
    <ThemeProvider>
      <AuthProvider>
        <Router>
          <Layout>
            <Routes>
              <Route path="/" element={<Dashboard />} />
              <Route path="/login" element={<Login />} />
              <Route path="/dashboard" element={<Dashboard />} />
            </Routes>
          </Layout>
        </Router>
      </AuthProvider>
    </ThemeProvider>
  );
}

export default App;`,
      type: 'component'
    }
  ];
}

function generateTheme(config: any): CodeTemplate[] {
  return [
    {
      path: 'src/contexts/ThemeContext.tsx',
      content: `import React, { createContext, useContext, ReactNode } from 'react';

interface ThemeConfig {
  colors: {
    primary: string;
    secondary: string;
    background: string;
    text: string;
  };
  typography: {
    fontFamily: string;
    headingFont: string;
  };
}

const theme: ThemeConfig = ${JSON.stringify(config, null, 2)};

const ThemeContext = createContext<ThemeConfig>(theme);

export function ThemeProvider({ children }: { children: ReactNode }) {
  return (
    <ThemeContext.Provider value={theme}>
      {children}
    </ThemeContext.Provider>
  );
}

export const useTheme = () => useContext(ThemeContext);`,
      type: 'component'
    }
  ];
}

function generateLayout(config: any): CodeTemplate[] {
  return [
    {
      path: 'src/components/Layout.tsx',
      content: `import React, { ReactNode } from 'react';
import { useTheme } from '../contexts/ThemeContext';

interface LayoutProps {
  children: ReactNode;
}

export default function Layout({ children }: LayoutProps) {
  const theme = useTheme();

  return (
    <div className="min-h-screen bg-background">
      <header className="border-b" style={{ borderColor: theme.colors.primary + '20' }}>
        <div className="container mx-auto px-4 py-4">
          <h1 className="text-2xl font-heading font-bold" style={{ color: theme.colors.primary }}>
            ${config.app_name}
          </h1>
        </div>
      </header>
      
      <main className="container mx-auto px-4 py-8">
        {children}
      </main>
      
      <footer className="border-t mt-auto">
        <div className="container mx-auto px-4 py-4 text-center text-sm text-gray-600">
          © 2024 ${config.app_name}. Generated prototype.
        </div>
      </footer>
    </div>
  );
}`,
      type: 'component'
    }
  ];
}

function generateAuthPages(config: any): CodeTemplate[] {
  return [
    {
      path: 'src/pages/Login.tsx',
      content: `import React, { useState } from 'react';
import { useAuth } from '../contexts/AuthContext';
import { useNavigate } from 'react-router-dom';

export default function Login() {
  const [email, setEmail] = useState('<EMAIL>');
  const [password, setPassword] = useState('password');
  const { login } = useAuth();
  const navigate = useNavigate();

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (login(email, password)) {
      navigate('/dashboard');
    } else {
      alert('Invalid credentials');
    }
  };

  return (
    <div className="max-w-md mx-auto">
      <h2 className="text-2xl font-heading font-bold mb-6">Login to ${config.app_name}</h2>
      
      <form onSubmit={handleSubmit} className="space-y-4">
        <div>
          <label className="block text-sm font-medium mb-1">Email</label>
          <input
            type="email"
            value={email}
            onChange={(e) => setEmail(e.target.value)}
            className="w-full p-2 border rounded-md"
            required
          />
        </div>
        
        <div>
          <label className="block text-sm font-medium mb-1">Password</label>
          <input
            type="password"
            value={password}
            onChange={(e) => setPassword(e.target.value)}
            className="w-full p-2 border rounded-md"
            required
          />
        </div>
        
        <button
          type="submit"
          className="w-full py-2 px-4 rounded-md text-white font-medium"
          style={{ backgroundColor: '${config.colors.primary}' }}
        >
          Login
        </button>
      </form>
    </div>
  );
}`,
      type: 'page'
    }
  ];
}

function generateDashboard(config: any): CodeTemplate[] {
  return [
    {
      path: 'src/pages/Dashboard.tsx',
      content: `import React from 'react';
import { useAuth } from '../contexts/AuthContext';

export default function Dashboard() {
  const { user, logout } = useAuth();

  return (
    <div>
      <div className="flex justify-between items-center mb-8">
        <h1 className="text-3xl font-heading font-bold">Dashboard</h1>
        <button
          onClick={logout}
          className="px-4 py-2 text-sm border rounded-md hover:bg-gray-50"
        >
          Logout
        </button>
      </div>
      
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <div className="p-6 border rounded-lg">
          <h3 className="font-semibold mb-2">Welcome</h3>
          <p className="text-gray-600">Hello, {user?.name || 'User'}!</p>
        </div>
        
        <div className="p-6 border rounded-lg">
          <h3 className="font-semibold mb-2">Quick Stats</h3>
          <p className="text-2xl font-bold" style={{ color: '${config.colors.primary}' }}>42</p>
        </div>
        
        <div className="p-6 border rounded-lg">
          <h3 className="font-semibold mb-2">Recent Activity</h3>
          <p className="text-gray-600">No recent activity</p>
        </div>
      </div>
    </div>
  );
}`,
      type: 'page'
    }
  ];
}

function generateAuthService(config: any): CodeTemplate[] {
  return [
    {
      path: 'src/contexts/AuthContext.tsx',
      content: `import React, { createContext, useContext, useState, ReactNode } from 'react';

interface User {
  id: string;
  email: string;
  name: string;
}

interface AuthContextType {
  user: User | null;
  login: (email: string, password: string) => boolean;
  logout: () => void;
  isAuthenticated: boolean;
}

const mockUsers: User[] = [
  { id: '1', email: '<EMAIL>', name: 'Demo User' }
];

const AuthContext = createContext<AuthContextType | null>(null);

export function AuthProvider({ children }: { children: ReactNode }) {
  const [user, setUser] = useState<User | null>(null);

  const login = (email: string, password: string): boolean => {
    const foundUser = mockUsers.find(u => u.email === email);
    if (foundUser && password === 'password') {
      setUser(foundUser);
      return true;
    }
    return false;
  };

  const logout = () => {
    setUser(null);
  };

  return (
    <AuthContext.Provider value={{
      user,
      login,
      logout,
      isAuthenticated: !!user
    }}>
      {children}
    </AuthContext.Provider>
  );
}

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (!context) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};`,
      type: 'service'
    }
  ];
}

function generateMockAPI(config: any): CodeTemplate[] {
  return [
    {
      path: 'src/services/api.ts',
      content: `// Mock API service for ${config.app_name}

export interface ApiResponse<T> {
  data: T;
  success: boolean;
  message?: string;
}

// Simulate API delay
const delay = (ms: number) => new Promise(resolve => setTimeout(resolve, ms));

export const api = {
  async get<T>(endpoint: string): Promise<ApiResponse<T>> {
    await delay(500);
    return {
      data: {} as T,
      success: true,
      message: 'Mock data retrieved successfully'
    };
  },

  async post<T>(endpoint: string, data: any): Promise<ApiResponse<T>> {
    await delay(800);
    return {
      data: data as T,
      success: true,
      message: 'Data created successfully'
    };
  },

  async put<T>(endpoint: string, data: any): Promise<ApiResponse<T>> {
    await delay(600);
    return {
      data: data as T,
      success: true,
      message: 'Data updated successfully'
    };
  },

  async delete(endpoint: string): Promise<ApiResponse<null>> {
    await delay(400);
    return {
      data: null,
      success: true,
      message: 'Data deleted successfully'
    };
  }
};`,
      type: 'service'
    }
  ];
}

function generateUserProfile(config: any): CodeTemplate[] {
  return [];
}

function generateChartComponents(config: any): CodeTemplate[] {
  return [];
}
