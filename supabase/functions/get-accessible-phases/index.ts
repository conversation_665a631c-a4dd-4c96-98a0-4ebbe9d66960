
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2.48.1'

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
}

Deno.serve(async (req) => {
  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    return new Response(null, { headers: corsHeaders })
  }

  try {
    // Get the request body
    const { projectId } = await req.json()
    
    // Validate project ID
    if (!projectId) {
      return new Response(
        JSON.stringify({ error: 'Project ID is required' }),
        { headers: { ...corsHeaders, 'Content-Type': 'application/json' }, status: 400 }
      )
    }
    
    // Get authorization header
    const authHeader = req.headers.get('Authorization')
    if (!authHeader) {
      return new Response(
        JSON.stringify({ error: 'Authorization header is required' }),
        { headers: { ...corsHeaders, 'Content-Type': 'application/json' }, status: 401 }
      )
    }
    
    // Create Supabase client
    const supabaseClient = createClient(
      Deno.env.get('SUPABASE_URL') ?? '',
      Deno.env.get('SUPABASE_ANON_KEY') ?? '',
      { global: { headers: { Authorization: authHeader } } }
    )
    
    // Get user from auth context
    const {
      data: { user },
      error: userError,
    } = await supabaseClient.auth.getUser()
    
    if (userError || !user) {
      return new Response(
        JSON.stringify({ error: 'Unauthorized', details: userError?.message }),
        { headers: { ...corsHeaders, 'Content-Type': 'application/json' }, status: 401 }
      )
    }
    
    // Check if user is admin first - direct DB query
    const { data: adminRoles, error: adminError } = await supabaseClient
      .from('user_roles')
      .select('role')
      .eq('user_id', user.id)
      .eq('role', 'admin')
      .limit(1)
    
    const isAdmin = !adminError && adminRoles && adminRoles.length > 0
    
    if (isAdmin) {
      // Admins have access to all phases
      console.log(`Admin user ${user.id} has access to all phases`)
      return new Response(
        JSON.stringify({ 
          phases: ['interactive', 'poc', 'mvp', 'production'] 
        }),
        { headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
      )
    }
    
    // Interactive is always accessible
    const accessiblePhases = ['interactive']
    
    // Check for pro subscription - properly using gt for valid_until
    const { data: proSubscriptions, error: proError } = await supabaseClient
      .from('user_subscriptions')
      .select('*')
      .eq('user_id', user.id)
      .eq('status', 'active')
      .eq('plan_id', 'pro')
      .gt('valid_until', 'now()')  // FIXED: Correct comparison for date
      .limit(1)
    
    const isPro = !proError && proSubscriptions && proSubscriptions.length > 0
    
    if (isPro) {
      // Pro users have access to all phases
      console.log(`Pro user ${user.id} has access to all phases`)
      return new Response(
        JSON.stringify({ 
          phases: ['interactive', 'poc', 'mvp', 'production'] 
        }),
        { headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
      )
    }
    
    // Debug all user's active subscriptions
    const { data: allSubs, error: allSubsError } = await supabaseClient
      .from('user_subscriptions')
      .select('*')
      .eq('user_id', user.id)
      .eq('status', 'active')
      .gt('valid_until', 'now()');
      
    console.log(`All active subscriptions for user ${user.id}:`, allSubs);
    
    // Check for project-specific roadmap packs - direct DB query
    const { data: projectSubscriptions, error: projectError } = await supabaseClient
      .from('user_subscriptions')
      .select('*')
      .eq('user_id', user.id)
      .eq('status', 'active')
      .eq('plan_id', 'roadmap-pack')
      .gt('valid_until', 'now()')  // Using gt for valid_until
      .or(`project_id.eq.${projectId},project_id.is.null`)
    
    console.log(`Project-specific subscriptions for user ${user.id}, project ${projectId}:`, projectSubscriptions)
    
    if (!projectError && projectSubscriptions && projectSubscriptions.length > 0) {
      // Process all matching subscriptions
      projectSubscriptions.forEach(sub => {
        if (sub.pack_type === 'poc' && !accessiblePhases.includes('poc')) {
          accessiblePhases.push('poc')
        }
        else if (sub.pack_type === 'poc-mvp') {
          if (!accessiblePhases.includes('poc')) accessiblePhases.push('poc')
          if (!accessiblePhases.includes('mvp')) accessiblePhases.push('mvp')
        }
        else if (sub.pack_type === 'all') {
          if (!accessiblePhases.includes('poc')) accessiblePhases.push('poc')
          if (!accessiblePhases.includes('mvp')) accessiblePhases.push('mvp')
          if (!accessiblePhases.includes('production')) accessiblePhases.push('production')
        }
      })
    }
    
    // Check for individual phase access - direct DB query
    const phases = ['poc', 'mvp', 'production']
    const planMappings = {
      'poc': ['poc-access', 'mvp-access', 'full-access'],
      'mvp': ['mvp-access', 'full-access'],
      'production': ['full-access']
    }
    
    for (const phase of phases) {
      // Skip if already accessible
      if (accessiblePhases.includes(phase)) continue
      
      const { data: phaseSubscriptions, error: phaseError } = await supabaseClient
        .from('user_subscriptions')
        .select('*')
        .eq('user_id', user.id)
        .eq('status', 'active')
        .gt('valid_until', 'now()')  // Using gt for valid_until
        .in('plan_id', planMappings[phase as keyof typeof planMappings])
        .or(`project_id.eq.${projectId},project_id.is.null`)
      
      if (!phaseError && phaseSubscriptions && phaseSubscriptions.length > 0) {
        accessiblePhases.push(phase)
      }
    }
    
    console.log(`Final accessible phases for user ${user.id}, project ${projectId}:`, accessiblePhases)
    
    return new Response(
      JSON.stringify({ phases: accessiblePhases }),
      { headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
    )
  } catch (error) {
    console.error('Error in get-accessible-phases:', error)
    
    return new Response(
      JSON.stringify({ error: 'Internal Server Error', details: error.message }),
      { headers: { ...corsHeaders, 'Content-Type': 'application/json' }, status: 500 }
    )
  }
})
