-- Migration: Create storage bucket for prototype files
-- Date: 2024-12-27
-- Description: Sets up storage bucket for generated prototype files

-- Create storage bucket for prototype files
INSERT INTO storage.buckets (id, name, public, file_size_limit, allowed_mime_types)
VALUES (
  'prototype-files',
  'prototype-files',
  true,
  52428800, -- 50MB limit
  ARRAY[
    'text/plain',
    'text/javascript',
    'text/css',
    'application/json',
    'application/typescript',
    'text/html',
    'text/markdown',
    'application/zip'
  ]
) ON CONFLICT (id) DO NOTHING;

-- Create RLS policies for prototype files bucket (will be created later when prototypes table exists)
-- For now, just create basic policies that allow authenticated users to manage their own files

-- Allow authenticated users to view files
CREATE POLICY "Authenticated users can view prototype files" ON storage.objects
  FOR SELECT USING (
    bucket_id = 'prototype-files' AND
    auth.role() = 'authenticated'
  );

-- Allow authenticated users to upload files
CREATE POLICY "Authenticated users can upload prototype files" ON storage.objects
  FOR INSERT WITH CHECK (
    bucket_id = 'prototype-files' AND
    auth.role() = 'authenticated'
  );

-- Allow authenticated users to update files
CREATE POLICY "Authenticated users can update prototype files" ON storage.objects
  FOR UPDATE USING (
    bucket_id = 'prototype-files' AND
    auth.role() = 'authenticated'
  );

-- Allow authenticated users to delete files
CREATE POLICY "Authenticated users can delete prototype files" ON storage.objects
  FOR DELETE USING (
    bucket_id = 'prototype-files' AND
    auth.role() = 'authenticated'
  );
