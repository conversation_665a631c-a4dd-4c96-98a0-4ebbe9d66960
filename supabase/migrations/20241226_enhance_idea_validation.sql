-- Migration: Add enhanced validation features with Tavily and Firecrawl support
-- Date: 2024-12-26
-- Description: Adds columns for real citations and market research data

-- Add new columns to idea_validations table for enhanced features
ALTER TABLE idea_validations 
ADD COLUMN IF NOT EXISTS real_citations J<PERSON>NB DEFAULT '[]',
ADD COLUMN IF NOT EXISTS market_research_data JSONB DEFAULT '{}',
ADD COLUMN IF NOT EXISTS enhanced_at TIMESTAMP,
ADD COLUMN IF NOT EXISTS research_sources TEXT[] DEFAULT '{}';

-- Create table to track competitor analysis from Firecrawl
CREATE TABLE IF NOT EXISTS competitor_analysis (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  validation_id UUID REFERENCES idea_validations(id) ON DELETE CASCADE,
  competitor_url TEXT NOT NULL,
  competitor_name TEXT,
  scraped_data JSONB DEFAULT '{}',
  scraping_status TEXT DEFAULT 'pending' CHECK (scraping_status IN ('pending', 'success', 'failed')),
  error_message TEXT,
  created_at TIMESTAMP DEFAULT NOW(),
  updated_at TIMESTAMP DEFAULT NOW()
);

-- Create table to track market research queries and results
CREATE TABLE IF NOT EXISTS market_research_queries (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  validation_id UUID REFERENCES idea_validations(id) ON DELETE CASCADE,
  query_type TEXT NOT NULL CHECK (query_type IN ('market_size', 'competitors', 'trends', 'funding')),
  search_query TEXT NOT NULL,
  search_results JSONB DEFAULT '[]',
  api_provider TEXT DEFAULT 'tavily' CHECK (api_provider IN ('tavily', 'serper', 'google')),
  created_at TIMESTAMP DEFAULT NOW()
);

-- Create table for verified citations
CREATE TABLE IF NOT EXISTS verified_citations (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  validation_id UUID REFERENCES idea_validations(id) ON DELETE CASCADE,
  citation_type TEXT NOT NULL CHECK (citation_type IN ('market_research', 'competitor', 'technology', 'trend')),
  url TEXT NOT NULL,
  title TEXT NOT NULL,
  source_name TEXT NOT NULL,
  relevance_score FLOAT DEFAULT 0.0 CHECK (relevance_score >= 0.0 AND relevance_score <= 1.0),
  snippet TEXT,
  published_date DATE,
  accessed_date TIMESTAMP DEFAULT NOW(),
  verification_status TEXT DEFAULT 'unverified' CHECK (verification_status IN ('verified', 'unverified', 'broken'))
);

-- Create indexes for better query performance
CREATE INDEX IF NOT EXISTS idx_competitor_analysis_validation_id ON competitor_analysis(validation_id);
CREATE INDEX IF NOT EXISTS idx_market_research_validation_id ON market_research_queries(validation_id);
CREATE INDEX IF NOT EXISTS idx_verified_citations_validation_id ON verified_citations(validation_id);
CREATE INDEX IF NOT EXISTS idx_verified_citations_type ON verified_citations(citation_type);

-- Enable Row Level Security on new tables
ALTER TABLE competitor_analysis ENABLE ROW LEVEL SECURITY;
ALTER TABLE market_research_queries ENABLE ROW LEVEL SECURITY;
ALTER TABLE verified_citations ENABLE ROW LEVEL SECURITY;

-- RLS Policies: Users can only see their own data
CREATE POLICY "Users can view their own competitor analysis" ON competitor_analysis
  FOR SELECT USING (
    validation_id IN (
      SELECT id FROM idea_validations WHERE "userId" = auth.uid()
    )
  );

CREATE POLICY "Users can view their own market research" ON market_research_queries
  FOR SELECT USING (
    validation_id IN (
      SELECT id FROM idea_validations WHERE "userId" = auth.uid()
    )
  );

CREATE POLICY "Users can view their own citations" ON verified_citations
  FOR SELECT USING (
    validation_id IN (
      SELECT id FROM idea_validations WHERE "userId" = auth.uid()
    )
  );

-- Admin policies for all new tables
CREATE POLICY "Admins can manage all competitor analysis" ON competitor_analysis
  FOR ALL USING (
    EXISTS (
      SELECT 1 FROM user_roles 
      WHERE user_id = auth.uid() AND role = 'admin'
    )
  );

CREATE POLICY "Admins can manage all market research" ON market_research_queries
  FOR ALL USING (
    EXISTS (
      SELECT 1 FROM user_roles 
      WHERE user_id = auth.uid() AND role = 'admin'
    )
  );

CREATE POLICY "Admins can manage all citations" ON verified_citations
  FOR ALL USING (
    EXISTS (
      SELECT 1 FROM user_roles 
      WHERE user_id = auth.uid() AND role = 'admin'
    )
  );

-- Create function to store market research results
CREATE OR REPLACE FUNCTION store_market_research(
  p_validation_id UUID,
  p_query_type TEXT,
  p_search_query TEXT,
  p_search_results JSONB,
  p_api_provider TEXT DEFAULT 'tavily'
)
RETURNS UUID AS $$
DECLARE
  research_id UUID;
BEGIN
  INSERT INTO market_research_queries (
    validation_id, 
    query_type, 
    search_query, 
    search_results, 
    api_provider
  )
  VALUES (
    p_validation_id, 
    p_query_type, 
    p_search_query, 
    p_search_results, 
    p_api_provider
  )
  RETURNING id INTO research_id;
  
  RETURN research_id;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create function to store competitor analysis
CREATE OR REPLACE FUNCTION store_competitor_analysis(
  p_validation_id UUID,
  p_competitor_url TEXT,
  p_competitor_name TEXT,
  p_scraped_data JSONB,
  p_status TEXT DEFAULT 'success'
)
RETURNS UUID AS $$
DECLARE
  analysis_id UUID;
BEGIN
  INSERT INTO competitor_analysis (
    validation_id,
    competitor_url,
    competitor_name,
    scraped_data,
    scraping_status
  )
  VALUES (
    p_validation_id,
    p_competitor_url,
    p_competitor_name,
    p_scraped_data,
    p_status
  )
  RETURNING id INTO analysis_id;
  
  RETURN analysis_id;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create function to store verified citations
CREATE OR REPLACE FUNCTION store_verified_citation(
  p_validation_id UUID,
  p_citation_type TEXT,
  p_url TEXT,
  p_title TEXT,
  p_source_name TEXT,
  p_relevance_score FLOAT DEFAULT 0.0,
  p_snippet TEXT DEFAULT NULL,
  p_published_date DATE DEFAULT NULL
)
RETURNS UUID AS $$
DECLARE
  citation_id UUID;
BEGIN
  INSERT INTO verified_citations (
    validation_id,
    citation_type,
    url,
    title,
    source_name,
    relevance_score,
    snippet,
    published_date
  )
  VALUES (
    p_validation_id,
    p_citation_type,
    p_url,
    p_title,
    p_source_name,
    p_relevance_score,
    p_snippet,
    p_published_date
  )
  RETURNING id INTO citation_id;
  
  RETURN citation_id;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create view for enhanced validation summary
CREATE OR REPLACE VIEW enhanced_validation_summary AS
SELECT 
  iv.id,
  iv."originalIdea",
  iv.status,
  iv.enhanced_at,
  iv.created_at,
  COALESCE(jsonb_array_length(iv.real_citations), 0) as real_citations_count,
  COALESCE(
    (SELECT COUNT(*) FROM competitor_analysis ca WHERE ca.validation_id = iv.id),
    0
  ) as competitors_analyzed,
  COALESCE(
    (SELECT COUNT(*) FROM market_research_queries mr WHERE mr.validation_id = iv.id),
    0
  ) as market_research_queries,
  COALESCE(
    (SELECT COUNT(*) FROM verified_citations vc WHERE vc.validation_id = iv.id),
    0
  ) as verified_citations_count,
  CASE 
    WHEN iv.enhanced_at IS NOT NULL THEN 'enhanced'
    WHEN iv.status = 'completed' THEN 'standard'
    ELSE iv.status
  END as validation_type
FROM idea_validations iv;

-- Grant necessary permissions
GRANT SELECT ON enhanced_validation_summary TO authenticated;
GRANT SELECT ON enhanced_validation_summary TO service_role;

-- Grant execute permissions on new functions
GRANT EXECUTE ON FUNCTION store_market_research TO service_role;
GRANT EXECUTE ON FUNCTION store_competitor_analysis TO service_role;
GRANT EXECUTE ON FUNCTION store_verified_citation TO service_role;

-- Add comment to document the enhancement
COMMENT ON COLUMN idea_validations.real_citations IS 'Stores actual citations from web search results (Tavily/Firecrawl)';
COMMENT ON COLUMN idea_validations.market_research_data IS 'Stores raw market research data from enhanced validation';
COMMENT ON COLUMN idea_validations.enhanced_at IS 'Timestamp when enhanced validation was completed';
COMMENT ON TABLE competitor_analysis IS 'Stores competitor data scraped via Firecrawl';
COMMENT ON TABLE market_research_queries IS 'Stores market research queries and results from Tavily';
COMMENT ON TABLE verified_citations IS 'Stores verified citations from real web sources';