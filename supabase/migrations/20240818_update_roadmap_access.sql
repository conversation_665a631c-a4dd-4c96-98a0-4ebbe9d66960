
-- Update the check_roadmap_access function to properly check roadmap access
CREATE OR REPLACE FUNCTION public.check_roadmap_access(phase text, project_id text DEFAULT NULL::text)
 RETURNS boolean
 LANGUAGE plpgsql
 SECURITY DEFINER
 SET search_path TO 'public'
AS $function$
DECLARE
  user_has_access boolean;
  project_uuid uuid;
BEGIN
  -- Convert project_id to UUID if provided
  IF project_id IS NOT NULL AND project_id != '' THEN
    project_uuid := project_id::uuid;
  END IF;

  -- Admins always have access
  IF (SELECT EXISTS (
    SELECT 1 
    FROM user_roles ur
    WHERE ur.user_id = auth.uid()
    AND ur.role = 'admin'
  )) THEN
    RETURN true;
  END IF;

  -- Check if user has pro subscription
  IF (SELECT EXISTS (
    SELECT 1 
    FROM user_subscriptions us
    WHERE us.user_id = auth.uid()
    AND us.status = 'active'
    AND us.valid_until > now()
    AND us.plan_id = 'pro'
  )) THEN
    RETURN true;
  END IF;

  -- Interactive Prototype is always accessible
  IF phase = 'interactive' THEN
    RETURN true;
  END IF;

  -- Check if user has a project-specific roadmap pack with access to this phase
  IF project_uuid IS NOT NULL THEN
    RAISE LOG 'Checking project-specific roadmap access for user %, phase %, project %', auth.uid(), phase, project_uuid;
    
    SELECT EXISTS (
      SELECT 1
      FROM user_subscriptions us
      WHERE us.user_id = auth.uid()
      AND us.status = 'active'
      AND us.valid_until > now()
      AND us.plan_id = 'roadmap-pack'
      AND (
        (us.project_id = project_uuid OR us.project_id IS NULL) -- Check both project-specific and global
        AND (
          (us.pack_type = 'poc' AND phase = 'poc') OR
          (us.pack_type = 'poc-mvp' AND (phase = 'poc' OR phase = 'mvp')) OR
          (us.pack_type = 'all')
        )
      )
    ) INTO user_has_access;
    
    RAISE LOG 'Project-specific roadmap pack access result for phase % on project %: %', phase, project_uuid, user_has_access;

    -- If we found access, return it
    IF user_has_access THEN
      RETURN true;
    END IF;
  END IF;

  -- Check for global roadmap pack access (no project_id)
  RAISE LOG 'Checking global roadmap access for user %, phase %', auth.uid(), phase;
  
  SELECT EXISTS (
    SELECT 1
    FROM user_subscriptions us
    WHERE us.user_id = auth.uid()
    AND us.status = 'active'
    AND us.valid_until > now()
    AND us.plan_id = 'roadmap-pack'
    AND us.project_id IS NULL
    AND (
      (us.pack_type = 'poc' AND phase = 'poc') OR
      (us.pack_type = 'poc-mvp' AND (phase = 'poc' OR phase = 'mvp')) OR
      (us.pack_type = 'all')
    )
  ) INTO user_has_access;
  
  RAISE LOG 'Global roadmap pack access result for phase %: %', phase, user_has_access;

  -- If we found access, return it
  IF user_has_access THEN
    RETURN true;
  END IF;

  -- Check for individual phase access
  RAISE LOG 'Checking individual phase access for user %, phase %', auth.uid(), phase;
  SELECT EXISTS (
    SELECT 1
    FROM user_subscriptions us
    WHERE us.user_id = auth.uid()
    AND us.status = 'active'
    AND us.valid_until > now()
    AND (
      (phase = 'poc' AND us.plan_id IN ('poc-access', 'mvp-access', 'full-access')) OR
      (phase = 'mvp' AND us.plan_id IN ('mvp-access', 'full-access')) OR
      (phase = 'production' AND us.plan_id = 'full-access')
    )
    AND (us.project_id IS NULL OR us.project_id = project_uuid)
  ) INTO user_has_access;
  
  RAISE LOG 'Individual phase access result for phase %: %', phase, user_has_access;

  RETURN user_has_access;
END;
$function$;

-- Make sure pack_type is correctly populated in user_subscriptions
UPDATE user_subscriptions 
SET pack_type = 'poc' 
WHERE plan_id = 'roadmap-pack' 
AND (pack_type IS NULL OR pack_type = '');
