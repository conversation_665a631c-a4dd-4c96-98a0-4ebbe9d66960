-- Migration: Add Supabase Storage for prototype files
-- Date: 2024-12-27
-- Description: Sets up storage bucket for generated prototype files

-- Create storage bucket for prototype files
INSERT INTO storage.buckets (id, name, public, file_size_limit, allowed_mime_types)
VALUES (
  'prototype-files',
  'prototype-files',
  true,
  52428800, -- 50MB limit
  ARRAY[
    'text/plain',
    'text/javascript',
    'text/css',
    'application/json',
    'application/typescript',
    'text/html',
    'text/markdown',
    'application/zip'
  ]
) ON CONFLICT (id) DO NOTHING;

-- Create RLS policies for prototype files bucket
CREATE POLICY "Users can view their own prototype files" ON storage.objects
  FOR SELECT USING (
    bucket_id = 'prototype-files' AND
    (storage.foldername(name))[1] IN (
      SELECT id::text FROM prototypes WHERE user_id = auth.uid()
    )
  );

CREATE POLICY "Users can upload their own prototype files" ON storage.objects
  FOR INSERT WITH CHECK (
    bucket_id = 'prototype-files' AND
    (storage.foldername(name))[1] IN (
      SELECT id::text FROM prototypes WHERE user_id = auth.uid()
    )
  );

CREATE POLICY "Users can update their own prototype files" ON storage.objects
  FOR UPDATE USING (
    bucket_id = 'prototype-files' AND
    (storage.foldername(name))[1] IN (
      SELECT id::text FROM prototypes WHERE user_id = auth.uid()
    )
  );

CREATE POLICY "Users can delete their own prototype files" ON storage.objects
  FOR DELETE USING (
    bucket_id = 'prototype-files' AND
    (storage.foldername(name))[1] IN (
      SELECT id::text FROM prototypes WHERE user_id = auth.uid()
    )
  );

-- Update prototype_files table to store storage paths instead of content
ALTER TABLE prototype_files 
ADD COLUMN IF NOT EXISTS storage_path TEXT,
ADD COLUMN IF NOT EXISTS file_size INTEGER,
ADD COLUMN IF NOT EXISTS public_url TEXT;

-- Add index for storage path lookups
CREATE INDEX IF NOT EXISTS idx_prototype_files_storage_path ON prototype_files(storage_path);

-- Add function to generate zip file for prototype
CREATE OR REPLACE FUNCTION generate_prototype_zip(prototype_uuid UUID)
RETURNS TEXT AS $$
DECLARE
  zip_path TEXT;
  prototype_name TEXT;
BEGIN
  -- Get prototype name for zip file naming
  SELECT name INTO prototype_name FROM prototypes WHERE id = prototype_uuid;
  
  -- Generate zip path
  zip_path := prototype_uuid::text || '/' || LOWER(REPLACE(prototype_name, ' ', '-')) || '-prototype.zip';
  
  RETURN zip_path;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Grant execute permission on the function
GRANT EXECUTE ON FUNCTION generate_prototype_zip(UUID) TO authenticated;

-- Add comment to document the storage setup
COMMENT ON TABLE prototype_files IS 'Stores metadata for prototype files stored in Supabase Storage';
