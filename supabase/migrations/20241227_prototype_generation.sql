-- Migration: Add prototype generation system
-- Date: 2024-12-27
-- Description: Adds tables for prototype generation, task planning, and project configuration

-- Create prototypes table
CREATE TABLE IF NOT EXISTS prototypes (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  validation_id UUID REFERENCES idea_validations(id) ON DELETE CASCADE,
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
  name TEXT NOT NULL,
  description TEXT,
  config JSONB NOT NULL DEFAULT '{}',
  status TEXT NOT NULL DEFAULT 'pending' CHECK (status IN ('pending', 'planning', 'generating', 'completed', 'failed', 'deployed')),
  github_repo_url TEXT,
  deployment_url TEXT,
  preview_url TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  completed_at TIMESTAMP WITH TIME ZONE
);

-- Create prototype_tasks table for task planning and tracking
CREATE TABLE IF NOT EXISTS prototype_tasks (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  prototype_id UUID REFERENCES prototypes(id) ON DELETE CASCADE,
  feature_id TEXT,
  title TEXT NOT NULL,
  description TEXT,
  task_type TEXT NOT NULL CHECK (task_type IN ('setup', 'component', 'page', 'service', 'integration', 'styling', 'deployment')),
  status TEXT NOT NULL DEFAULT 'pending' CHECK (status IN ('pending', 'in_progress', 'completed', 'failed', 'skipped')),
  parent_task_id UUID REFERENCES prototype_tasks(id),
  dependencies TEXT[] DEFAULT '{}',
  order_index INTEGER DEFAULT 0,
  estimated_duration INTEGER DEFAULT 5, -- in minutes
  actual_duration INTEGER,
  error_message TEXT,
  generated_files JSONB DEFAULT '[]',
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  started_at TIMESTAMP WITH TIME ZONE,
  completed_at TIMESTAMP WITH TIME ZONE
);

-- Create project_configurations table for storing app customization
CREATE TABLE IF NOT EXISTS project_configurations (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  prototype_id UUID REFERENCES prototypes(id) ON DELETE CASCADE,
  app_name TEXT NOT NULL,
  logo_url TEXT,
  colors JSONB NOT NULL DEFAULT '{"primary": "#3b82f6", "secondary": "#10b981", "background": "#ffffff", "text": "#1f2937"}',
  typography JSONB NOT NULL DEFAULT '{"fontFamily": "Inter", "headingFont": "Inter"}',
  selected_features JSONB NOT NULL DEFAULT '{"core": [], "mustHave": [], "shouldHave": []}',
  theme_config JSONB DEFAULT '{}',
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create prototype_files table for tracking generated files
CREATE TABLE IF NOT EXISTS prototype_files (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  prototype_id UUID REFERENCES prototypes(id) ON DELETE CASCADE,
  task_id UUID REFERENCES prototype_tasks(id),
  file_path TEXT NOT NULL,
  file_content TEXT,
  file_type TEXT NOT NULL CHECK (file_type IN ('component', 'page', 'hook', 'service', 'type', 'style', 'config', 'test')),
  template_used TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_prototypes_user_id ON prototypes(user_id);
CREATE INDEX IF NOT EXISTS idx_prototypes_validation_id ON prototypes(validation_id);
CREATE INDEX IF NOT EXISTS idx_prototypes_status ON prototypes(status);
CREATE INDEX IF NOT EXISTS idx_prototype_tasks_prototype_id ON prototype_tasks(prototype_id);
CREATE INDEX IF NOT EXISTS idx_prototype_tasks_status ON prototype_tasks(status);
CREATE INDEX IF NOT EXISTS idx_prototype_tasks_parent_id ON prototype_tasks(parent_task_id);
CREATE INDEX IF NOT EXISTS idx_project_configurations_prototype_id ON project_configurations(prototype_id);
CREATE INDEX IF NOT EXISTS idx_prototype_files_prototype_id ON prototype_files(prototype_id);

-- Add RLS policies
ALTER TABLE prototypes ENABLE ROW LEVEL SECURITY;
ALTER TABLE prototype_tasks ENABLE ROW LEVEL SECURITY;
ALTER TABLE project_configurations ENABLE ROW LEVEL SECURITY;
ALTER TABLE prototype_files ENABLE ROW LEVEL SECURITY;

-- Prototypes policies
CREATE POLICY "Users can view their own prototypes" ON prototypes
  FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can create their own prototypes" ON prototypes
  FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update their own prototypes" ON prototypes
  FOR UPDATE USING (auth.uid() = user_id);

-- Prototype tasks policies
CREATE POLICY "Users can view tasks for their prototypes" ON prototype_tasks
  FOR SELECT USING (
    EXISTS (
      SELECT 1 FROM prototypes 
      WHERE prototypes.id = prototype_tasks.prototype_id 
      AND prototypes.user_id = auth.uid()
    )
  );

CREATE POLICY "Users can create tasks for their prototypes" ON prototype_tasks
  FOR INSERT WITH CHECK (
    EXISTS (
      SELECT 1 FROM prototypes 
      WHERE prototypes.id = prototype_tasks.prototype_id 
      AND prototypes.user_id = auth.uid()
    )
  );

CREATE POLICY "Users can update tasks for their prototypes" ON prototype_tasks
  FOR UPDATE USING (
    EXISTS (
      SELECT 1 FROM prototypes 
      WHERE prototypes.id = prototype_tasks.prototype_id 
      AND prototypes.user_id = auth.uid()
    )
  );

-- Project configurations policies
CREATE POLICY "Users can view configs for their prototypes" ON project_configurations
  FOR SELECT USING (
    EXISTS (
      SELECT 1 FROM prototypes 
      WHERE prototypes.id = project_configurations.prototype_id 
      AND prototypes.user_id = auth.uid()
    )
  );

CREATE POLICY "Users can create configs for their prototypes" ON project_configurations
  FOR INSERT WITH CHECK (
    EXISTS (
      SELECT 1 FROM prototypes 
      WHERE prototypes.id = project_configurations.prototype_id 
      AND prototypes.user_id = auth.uid()
    )
  );

CREATE POLICY "Users can update configs for their prototypes" ON project_configurations
  FOR UPDATE USING (
    EXISTS (
      SELECT 1 FROM prototypes 
      WHERE prototypes.id = project_configurations.prototype_id 
      AND prototypes.user_id = auth.uid()
    )
  );

-- Prototype files policies
CREATE POLICY "Users can view files for their prototypes" ON prototype_files
  FOR SELECT USING (
    EXISTS (
      SELECT 1 FROM prototypes 
      WHERE prototypes.id = prototype_files.prototype_id 
      AND prototypes.user_id = auth.uid()
    )
  );

CREATE POLICY "Users can create files for their prototypes" ON prototype_files
  FOR INSERT WITH CHECK (
    EXISTS (
      SELECT 1 FROM prototypes 
      WHERE prototypes.id = prototype_files.prototype_id 
      AND prototypes.user_id = auth.uid()
    )
  );

-- Add functions for prototype management
CREATE OR REPLACE FUNCTION update_prototype_updated_at()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = NOW();
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER update_prototypes_updated_at
  BEFORE UPDATE ON prototypes
  FOR EACH ROW
  EXECUTE FUNCTION update_prototype_updated_at();

CREATE TRIGGER update_project_configurations_updated_at
  BEFORE UPDATE ON project_configurations
  FOR EACH ROW
  EXECUTE FUNCTION update_prototype_updated_at();

-- Function to calculate prototype progress
CREATE OR REPLACE FUNCTION calculate_prototype_progress(prototype_uuid UUID)
RETURNS INTEGER AS $$
DECLARE
  total_tasks INTEGER;
  completed_tasks INTEGER;
  progress INTEGER;
BEGIN
  SELECT COUNT(*) INTO total_tasks
  FROM prototype_tasks
  WHERE prototype_id = prototype_uuid;
  
  SELECT COUNT(*) INTO completed_tasks
  FROM prototype_tasks
  WHERE prototype_id = prototype_uuid AND status = 'completed';
  
  IF total_tasks = 0 THEN
    RETURN 0;
  END IF;
  
  progress := ROUND((completed_tasks::DECIMAL / total_tasks::DECIMAL) * 100);
  RETURN progress;
END;
$$ LANGUAGE plpgsql;

-- Add comment to document the enhancement
COMMENT ON TABLE prototypes IS 'Stores prototype generation projects and their status';
COMMENT ON TABLE prototype_tasks IS 'Tracks individual tasks for prototype generation with dependencies';
COMMENT ON TABLE project_configurations IS 'Stores app customization settings like colors, fonts, and features';
COMMENT ON TABLE prototype_files IS 'Tracks generated files for each prototype';
