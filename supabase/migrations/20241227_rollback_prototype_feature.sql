-- Migration: Rollback prototype generation feature
-- Date: 2024-12-27
-- Description: Completely removes all prototype generation related tables, policies, functions, and storage

-- First, delete all files from the prototype-files bucket
DELETE FROM storage.objects WHERE bucket_id = 'prototype-files';

-- Drop all RLS policies for storage objects related to prototype-files bucket
DROP POLICY IF EXISTS "Users can view their own prototype files" ON storage.objects;
DROP POLICY IF EXISTS "Users can upload their own prototype files" ON storage.objects;
DROP POLICY IF EXISTS "Users can update their own prototype files" ON storage.objects;
DROP POLICY IF EXISTS "Users can delete their own prototype files" ON storage.objects;
DROP POLICY IF EXISTS "Authenticated users can view prototype files" ON storage.objects;
DROP POLICY IF EXISTS "Authenticated users can upload prototype files" ON storage.objects;
DROP POLICY IF EXISTS "Authenticated users can update prototype files" ON storage.objects;
DROP POLICY IF EXISTS "Authenticated users can delete prototype files" ON storage.objects;

-- Now remove storage bucket (after all files are deleted)
DELETE FROM storage.buckets WHERE id = 'prototype-files';

-- Drop all functions related to prototypes
DROP FUNCTION IF EXISTS debug_prototype_access(UUID);
DROP FUNCTION IF EXISTS generate_prototype_zip(UUID);
DROP FUNCTION IF EXISTS calculate_prototype_progress(UUID);
DROP FUNCTION IF EXISTS update_prototype_updated_at();

-- Drop all triggers
DROP TRIGGER IF EXISTS update_prototypes_updated_at ON prototypes;
DROP TRIGGER IF EXISTS update_project_configurations_updated_at ON project_configurations;

-- Drop all RLS policies for prototype tables
DROP POLICY IF EXISTS "Users can view their own prototypes" ON prototypes;
DROP POLICY IF EXISTS "Users can create their own prototypes" ON prototypes;
DROP POLICY IF EXISTS "Users can update their own prototypes" ON prototypes;

DROP POLICY IF EXISTS "Users can view tasks for their prototypes" ON prototype_tasks;
DROP POLICY IF EXISTS "Users can create tasks for their prototypes" ON prototype_tasks;
DROP POLICY IF EXISTS "Users can update tasks for their prototypes" ON prototype_tasks;

DROP POLICY IF EXISTS "Users can view configs for their prototypes" ON project_configurations;
DROP POLICY IF EXISTS "Users can create configs for their prototypes" ON project_configurations;
DROP POLICY IF EXISTS "Users can update configs for their prototypes" ON project_configurations;

DROP POLICY IF EXISTS "Users can view files for their prototypes" ON prototype_files;
DROP POLICY IF EXISTS "Users can create files for their prototypes" ON prototype_files;

-- Drop all indexes
DROP INDEX IF EXISTS idx_prototypes_user_id;
DROP INDEX IF EXISTS idx_prototypes_validation_id;
DROP INDEX IF EXISTS idx_prototypes_status;
DROP INDEX IF EXISTS idx_prototype_tasks_prototype_id;
DROP INDEX IF EXISTS idx_prototype_tasks_status;
DROP INDEX IF EXISTS idx_prototype_tasks_parent_id;
DROP INDEX IF EXISTS idx_project_configurations_prototype_id;
DROP INDEX IF EXISTS idx_prototype_files_prototype_id;
DROP INDEX IF EXISTS idx_prototype_files_storage_path;

-- Drop all tables (in correct order due to foreign key constraints)
DROP TABLE IF EXISTS prototype_files CASCADE;
DROP TABLE IF EXISTS project_configurations CASCADE;
DROP TABLE IF EXISTS prototype_tasks CASCADE;
DROP TABLE IF EXISTS prototypes CASCADE;

-- Clean up any remaining references or orphaned data
-- (This is a safety measure in case there are any remaining dependencies)

-- Add comment to document the rollback
COMMENT ON SCHEMA public IS 'Prototype generation feature completely removed on 2024-12-27';
