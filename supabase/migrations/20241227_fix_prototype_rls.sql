-- Fix RLS policies for prototype generation
-- Date: 2024-12-27
-- Description: Updates RLS policies to ensure prototype creation works correctly

-- Drop existing policies if they exist
DROP POLICY IF EXISTS "Users can view their own prototypes" ON prototypes;
DROP POLICY IF EXISTS "Users can create their own prototypes" ON prototypes;
DROP POLICY IF EXISTS "Users can update their own prototypes" ON prototypes;

-- Recreate prototypes policies with better conditions
CREATE POLICY "Users can view their own prototypes" ON prototypes
  FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can create their own prototypes" ON prototypes
  FOR INSERT WITH CHECK (
    auth.uid() = user_id AND
    auth.uid() IS NOT NULL
  );

CREATE POLICY "Users can update their own prototypes" ON prototypes
  FOR UPDATE USING (
    auth.uid() = user_id AND
    auth.uid() IS NOT NULL
  );

-- Also update prototype_tasks policies to be more explicit
DROP POLICY IF EXISTS "Users can view tasks for their prototypes" ON prototype_tasks;
DROP POLICY IF EXISTS "Users can create tasks for their prototypes" ON prototype_tasks;
DROP POLICY IF EXISTS "Users can update tasks for their prototypes" ON prototype_tasks;

CREATE POLICY "Users can view tasks for their prototypes" ON prototype_tasks
  FOR SELECT USING (
    EXISTS (
      SELECT 1 FROM prototypes 
      WHERE prototypes.id = prototype_tasks.prototype_id 
      AND prototypes.user_id = auth.uid()
      AND auth.uid() IS NOT NULL
    )
  );

CREATE POLICY "Users can create tasks for their prototypes" ON prototype_tasks
  FOR INSERT WITH CHECK (
    EXISTS (
      SELECT 1 FROM prototypes 
      WHERE prototypes.id = prototype_tasks.prototype_id 
      AND prototypes.user_id = auth.uid()
      AND auth.uid() IS NOT NULL
    )
  );

CREATE POLICY "Users can update tasks for their prototypes" ON prototype_tasks
  FOR UPDATE USING (
    EXISTS (
      SELECT 1 FROM prototypes 
      WHERE prototypes.id = prototype_tasks.prototype_id 
      AND prototypes.user_id = auth.uid()
      AND auth.uid() IS NOT NULL
    )
  );

-- Update project_configurations policies
DROP POLICY IF EXISTS "Users can view configs for their prototypes" ON project_configurations;
DROP POLICY IF EXISTS "Users can create configs for their prototypes" ON project_configurations;
DROP POLICY IF EXISTS "Users can update configs for their prototypes" ON project_configurations;

CREATE POLICY "Users can view configs for their prototypes" ON project_configurations
  FOR SELECT USING (
    EXISTS (
      SELECT 1 FROM prototypes 
      WHERE prototypes.id = project_configurations.prototype_id 
      AND prototypes.user_id = auth.uid()
      AND auth.uid() IS NOT NULL
    )
  );

CREATE POLICY "Users can create configs for their prototypes" ON project_configurations
  FOR INSERT WITH CHECK (
    EXISTS (
      SELECT 1 FROM prototypes 
      WHERE prototypes.id = project_configurations.prototype_id 
      AND prototypes.user_id = auth.uid()
      AND auth.uid() IS NOT NULL
    )
  );

CREATE POLICY "Users can update configs for their prototypes" ON project_configurations
  FOR UPDATE USING (
    EXISTS (
      SELECT 1 FROM prototypes 
      WHERE prototypes.id = project_configurations.prototype_id 
      AND prototypes.user_id = auth.uid()
      AND auth.uid() IS NOT NULL
    )
  );

-- Update prototype_files policies
DROP POLICY IF EXISTS "Users can view files for their prototypes" ON prototype_files;
DROP POLICY IF EXISTS "Users can create files for their prototypes" ON prototype_files;

CREATE POLICY "Users can view files for their prototypes" ON prototype_files
  FOR SELECT USING (
    EXISTS (
      SELECT 1 FROM prototypes 
      WHERE prototypes.id = prototype_files.prototype_id 
      AND prototypes.user_id = auth.uid()
      AND auth.uid() IS NOT NULL
    )
  );

CREATE POLICY "Users can create files for their prototypes" ON prototype_files
  FOR INSERT WITH CHECK (
    EXISTS (
      SELECT 1 FROM prototypes 
      WHERE prototypes.id = prototype_files.prototype_id 
      AND prototypes.user_id = auth.uid()
      AND auth.uid() IS NOT NULL
    )
  );

-- Add a function to help debug RLS issues
CREATE OR REPLACE FUNCTION debug_prototype_access(prototype_uuid UUID)
RETURNS TABLE (
  current_user_id UUID,
  prototype_exists BOOLEAN,
  prototype_user_id UUID,
  can_access BOOLEAN
) AS $$
BEGIN
  RETURN QUERY
  SELECT 
    auth.uid() as current_user_id,
    EXISTS(SELECT 1 FROM prototypes WHERE id = prototype_uuid) as prototype_exists,
    (SELECT user_id FROM prototypes WHERE id = prototype_uuid) as prototype_user_id,
    (auth.uid() = (SELECT user_id FROM prototypes WHERE id = prototype_uuid)) as can_access;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Grant execute permission on the debug function
GRANT EXECUTE ON FUNCTION debug_prototype_access(UUID) TO authenticated;

-- Add comment
COMMENT ON FUNCTION debug_prototype_access(UUID) IS 'Debug function to help troubleshoot RLS issues with prototypes';
